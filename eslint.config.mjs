import { defineConfig } from 'eslint/config';
import js from '@eslint/js';
import globals from 'globals';
import tseslint from 'typescript-eslint';
import pluginReact from 'eslint-plugin-react';
// import json from "@eslint/json";
import pluginPrettierRecommended from 'eslint-plugin-prettier/recommended';
import reactStrictDOMCheck from './eslint-plugin-react-strict-dom-check.mjs';
import pluginImport from 'eslint-plugin-import';
import pluginUnusedImports from 'eslint-plugin-unused-imports';
import pluginSimpleImportSort from 'eslint-plugin-simple-import-sort';

export default defineConfig([
  {
    files: ['**/*.{js,mjs,cjs,ts,jsx,tsx}'],
    plugins: {
      js,
      'react-strict-dom-check': reactStrictDOMCheck,
      import: pluginImport,
      'unused-imports': pluginUnusedImports,
      'simple-import-sort': pluginSimpleImportSort,
    },
    extends: ['js/recommended'],
  },
  {
    files: ['**/*.{js,mjs,cjs,ts,jsx,tsx}'],
    languageOptions: { globals: { ...globals.browser, ...globals.node } },
  },
  tseslint.configs.recommended,
  pluginReact.configs.flat.recommended,
  // { files: ["**/*.json"], plugins: { json }, language: "json/json", extends: ["json/recommended"] },
  pluginPrettierRecommended,
  {
    files: ['**/*.{js,jsx,ts,tsx}'],
    rules: {
      'react-strict-dom-check/no-react-without-web': 'error',
      'react-strict-dom-check/no-react-native-in-web': 'error',
    },
  },
  {
    files: ['**/*.{js,jsx,ts,tsx}'],
    rules: {
      // 导入排序规则
      'simple-import-sort/imports': 'error',
      'simple-import-sort/exports': 'error',
      // 移除未使用的导入
      'unused-imports/no-unused-imports': 'error',
      'unused-imports/no-unused-vars': [
        'warn',
        {
          vars: 'all',
          varsIgnorePattern: '^_',
          args: 'after-used',
          argsIgnorePattern: '^_',
        },
      ],
      // 为纯类型导入添加type关键字
      '@typescript-eslint/consistent-type-imports': [
        'error',
        { prefer: 'type-imports', disallowTypeAnnotations: false },
      ],
      // 禁用@typescript-eslint/no-unused-vars，使用unused-imports/no-unused-vars代替
      '@typescript-eslint/no-unused-vars': 'off',
      // 确保导入存在
      'import/no-unresolved': 'off',
      // 确保导入按字母顺序排序
      'import/order': 'off',
    },
  },
]);
