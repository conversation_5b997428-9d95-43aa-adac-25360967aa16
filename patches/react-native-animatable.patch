diff --git a/createAnimatableComponent.js b/createAnimatableComponent.js
index 7be96ea7fb685b3b83c8697042b10d8f71eec5e0..43cd61bd61d0edaafc82feea3f51ab08623a93e9 100644
--- a/createAnimatableComponent.js
+++ b/createAnimatableComponent.js
@@ -318,7 +318,7 @@ export default function createAnimatableComponent(WrappedComponent) {
     }
 
     // eslint-disable-next-line camelcase
-    UNSAFE_componentWillReceiveProps(props) {
+    componentDidUpdate(props) {
       const {
         animation,
         delay,
