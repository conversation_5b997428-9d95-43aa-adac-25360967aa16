import { platformStorage } from '@dp-frontend/utils';
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import axios from 'axios';
import { isFunction, isObject } from 'radash';

const REQUEST_BASE_URL = process.env.EXPO_PUBLIC_REQUEST_BASE_URL;

/**
 * API响应接口
 */
export interface ApiResponse<T = unknown> {
  code: number;
  data: T;
  message?: string;
}

/**
 * 请求参数类型
 */
export type RequestParams = Record<
  string,
  string | number | boolean | null | undefined
>;

/**
 * 请求数据类型
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type RequestData = Record<string, any> | unknown[] | FormData;

/**
 * HTTP请求配置
 */
export interface HttpRequestConfig {
  baseURL?: string;
  timeout?: number;
  headers?: Record<string, string>;
}

/**
 * HTTP请求类
 * 提供基于Axios的HTTP请求方法
 */
export class HttpRequest {
  private axiosInstance: AxiosInstance;

  /**
   * 创建HTTP请求实例
   * @param config 请求配置
   */
  constructor(config?: HttpRequestConfig) {
    this.axiosInstance = axios.create({
      baseURL: config?.baseURL || REQUEST_BASE_URL,
      timeout: config?.timeout || 10000,
      headers: {
        'Content-Type': 'application/json',
        ...config?.headers,
      },
    });

    this.setupInterceptors();
  }

  /**
   * 设置请求和响应拦截器
   */
  private setupInterceptors(): void {
    this.axiosInstance.interceptors.request.use(
      async (config) => {
        const token = await platformStorage.getItem('AuthorizationToken');
        if (token) {
          config.headers.setAuthorization(`Bearer ${token}`);
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    this.axiosInstance.interceptors.response.use(
      async (response: AxiosResponse<ApiResponse<unknown>>) => {
        if (isFunction(response.headers.getAuthorization)) {
          const authorization = response.headers.getAuthorization()?.[1];
          if (authorization) {
            await platformStorage.setItem('AuthorizationToken', authorization);
          }
        }

        if (!isObject(response.data)) return response;
        if (response.data.code !== 200) return Promise.reject(response.data);
        return { ...response, data: response.data.data };
      },
      (error) => {
        return Promise.reject(error);
      }
    );
  }

  /**
   * 发送GET请求
   * @param url 请求URL
   * @param params 请求参数
   * @param config Axios配置
   * @returns 响应数据
   */
  public async get<T = unknown>(
    url: string,
    params?: RequestParams,
    config?: AxiosRequestConfig
  ): Promise<T> {
    const response = await this.axiosInstance.get<T>(url, {
      ...config,
      params,
    });
    return response.data;
  }

  /**
   * 发送POST请求
   * @param url 请求URL
   * @param data 请求数据
   * @param config Axios配置
   * @returns 响应数据
   */
  public async post<T = unknown>(
    url: string,
    data?: RequestData,
    config?: AxiosRequestConfig
  ): Promise<T> {
    const response = await this.axiosInstance.post<T>(url, data, config);
    return response.data;
  }

  /**
   * 发送PUT请求
   * @param url 请求URL
   * @param data 请求数据
   * @param config Axios配置
   * @returns 响应数据
   */
  public async put<T = unknown>(
    url: string,
    data?: RequestData,
    config?: AxiosRequestConfig
  ): Promise<T> {
    const response = await this.axiosInstance.put<T>(url, data, config);
    return response.data;
  }

  /**
   * 发送DELETE请求
   * @param url 请求URL
   * @param config Axios配置
   * @returns 响应数据
   */
  public async delete<T = unknown>(
    url: string,
    config?: AxiosRequestConfig
  ): Promise<T> {
    const response = await this.axiosInstance.delete<T>(url, config);
    return response.data;
  }

  /**
   * 发送PATCH请求
   * @param url 请求URL
   * @param data 请求数据
   * @param config Axios配置
   * @returns 响应数据
   */
  public async patch<T = unknown>(
    url: string,
    data?: RequestData,
    config?: AxiosRequestConfig
  ): Promise<T> {
    const response = await this.axiosInstance.patch<T>(url, data, config);
    return response.data;
  }
}

// 创建默认HTTP请求实例
export const request = new HttpRequest();
