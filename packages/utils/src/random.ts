import * as Crypto from 'expo-crypto';

/**
 * 生成随机UUID
 * 使用expo-crypto生成加密安全的随机UUID
 * @returns Promise<string> 随机生成的设备ID (UUID v4格式)
 */
export const generateUUID = async (): Promise<string> => {
  try {
    // 使用crypto生成UUID v4
    const uuid = Crypto.randomUUID();
    return uuid;
  } catch (error) {
    // 如果crypto API不可用，回退到手动生成UUID v4格式的ID
    console.warn(
      'Failed to generate crypto UUID, falling back to manual UUID generation:',
      error
    );

    // 手动生成符合UUID v4格式的ID
    // UUID v4格式: xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx
    // 其中x是任意十六进制数字，y是8、9、A或B中的一个

    // 生成随机十六进制字符串
    const hexDigits = '0123456789abcdef';
    let uuid = '';

    for (let i = 0; i < 36; i++) {
      if (i === 8 || i === 13 || i === 18 || i === 23) {
        uuid += '-';
      } else if (i === 14) {
        // 版本4的UUID在第14位必须是4
        uuid += '4';
      } else if (i === 19) {
        // 第19位必须是8、9、a或b
        uuid += hexDigits.charAt(Math.floor(Math.random() * 4) + 8);
      } else {
        uuid += hexDigits.charAt(Math.floor(Math.random() * 16));
      }
    }

    return uuid;
  }
};
