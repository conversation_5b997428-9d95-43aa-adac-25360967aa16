import { Platform } from 'react-native';

/**
 * 存储接口
 * 定义了存储操作的通用接口
 */
export interface Storage {
  /**
   * 获取存储的值
   * @param key 键名
   * @returns Promise<string | null> 存储的值，如果不存在则返回null
   */
  getItem(key: string): Promise<string | null>;

  /**
   * 设置存储的值
   * @param key 键名
   * @param value 值
   * @returns Promise<void>
   */
  setItem(key: string, value: string): Promise<void>;

  /**
   * 删除存储的值
   * @param key 键名
   * @returns Promise<void>
   */
  removeItem(key: string): Promise<void>;
}

/**
 * SecureStore存储
 * 使用expo-secure-store实现安全存储，适用于原生平台
 */
export class SecureStore implements Storage {
  private secureStore: typeof import('expo-secure-store');

  constructor() {
    // 动态导入SecureStore，以避免在Web环境中直接导入导致错误
    try {
      // eslint-disable-next-line @typescript-eslint/no-require-imports
      this.secureStore = require('expo-secure-store');
    } catch (error) {
      console.warn('SecureStore is not available:', error);
      throw new Error('SecureStore is not available on this platform');
    }
  }

  async getItem(key: string): Promise<string | null> {
    return await this.secureStore.getItemAsync(key);
  }

  async setItem(key: string, value: string): Promise<void> {
    await this.secureStore.setItemAsync(key, value);
  }

  async removeItem(key: string): Promise<void> {
    await this.secureStore.deleteItemAsync(key);
  }
}

/**
 * LocalStorage存储
 * 使用浏览器的localStorage实现存储，适用于Web环境
 */
export class LocalStorage implements Storage {
  constructor() {
    // 检查localStorage是否可用
    // if (typeof localStorage === 'undefined') {
    //   throw new Error('localStorage is not available on this platform');
    // }
  }

  async getItem(key: string): Promise<string | null> {
    return localStorage.getItem(key);
  }

  async setItem(key: string, value: string): Promise<void> {
    localStorage.setItem(key, value);
  }

  async removeItem(key: string): Promise<void> {
    localStorage.removeItem(key);
  }
}

/**
 * 内存存储
 * 使用内存Map实现存储，适用于任何环境
 */
export class MemoryStorage implements Storage {
  private storage = new Map<string, string>();

  async getItem(key: string): Promise<string | null> {
    return this.storage.get(key) || null;
  }

  async setItem(key: string, value: string): Promise<void> {
    this.storage.set(key, value);
  }

  async removeItem(key: string): Promise<void> {
    this.storage.delete(key);
  }
}

const storages = {
  secure: new SecureStore(),
  local: new LocalStorage(),
  memory: new MemoryStorage(),
} as const;

/**
 * 创建平台特定的存储实例
 * 在原生平台上使用SecureStore，在Web平台上使用LocalStorage
 * @returns Storage 平台特定的存储实例
 */
export const platformStorage = Platform.select({
  native: storages.secure,
  default: storages.local,
});
