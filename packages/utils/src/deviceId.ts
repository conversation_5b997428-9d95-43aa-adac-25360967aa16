import * as Application from 'expo-application';
import * as Device from 'expo-device';

import { generateUUID } from './random';
import { platformStorage as storage } from './storage';

// 存储设备ID的键名
const DEVICE_ID_KEY = 'dp_client_device_id';
// 存储原始设备标识符的键名（用于检测变化）
const NATIVE_DEVICE_ID_KEY = 'dp_client_native_device_id';
// 存储设备ID来源的键名
const DEVICE_ID_SOURCE_KEY = 'dp_client_device_id_source';

// 设备ID来源枚举
enum DeviceIdSource {
  ANDROID_ID = 'android_id',
  IOS_VENDOR_ID = 'ios_vendor_id',
  WEB_CLIENT_ID = 'web_client_id',
  GENERATED = 'generated',
}

/**
 * 获取原生设备标识符
 * 尝试获取平台特定的设备标识符
 * @returns Promise<{id: string | null, source: string | null}>
 */
const getNativeDeviceId = async (): Promise<{
  id: string | null;
  source: string | null;
}> => {
  // Android: 尝试获取Android ID
  if (Device.osName === 'Android') {
    try {
      // 使用类型断言处理可能不存在的方法
      if (typeof Application.getAndroidId === 'function') {
        const androidId = Application.getAndroidId();
        if (androidId) {
          return { id: androidId, source: DeviceIdSource.ANDROID_ID };
        }
      }
    } catch (error) {
      console.warn('Failed to get Android ID:', error);
    }
  }

  // iOS: 尝试获取iOS的vendor ID
  if (Device.osName === 'iOS' || Device.osName === 'iPadOS') {
    try {
      const vendorId = await Application.getIosIdForVendorAsync();
      if (vendorId) {
        return { id: vendorId, source: DeviceIdSource.IOS_VENDOR_ID };
      }
    } catch (error) {
      console.warn('Failed to get iOS vendor ID:', error);
    }
  }

  return { id: null, source: null };
};

/**
 * 获取设备ID
 * 使用增强的多层次获取策略和存储机制，支持多平台
 * @returns Promise<string> 设备ID
 */
export const getDeviceId = async (): Promise<string> => {
  try {
    // 步骤1: 尝试从存储中获取已存储的设备ID
    const storedDeviceId = await storage.getItem(DEVICE_ID_KEY);
    const storedNativeId = await storage.getItem(NATIVE_DEVICE_ID_KEY);

    // 步骤2: 获取当前的原生设备标识符
    const { id: currentNativeId, source: currentSource } =
      await getNativeDeviceId();

    // 步骤3: 检查是否需要保持现有ID或生成新ID

    // 情况1: 已有存储的设备ID
    if (storedDeviceId) {
      // 如果有存储的原生ID，检查它是否与当前原生ID匹配
      if (storedNativeId && currentNativeId) {
        // 如果原生ID发生变化（例如应用重装后iOS的vendor ID变化）
        // 但我们仍然保持使用之前存储的设备ID以保持一致性
        if (storedNativeId !== currentNativeId) {
          console.log(
            'Native device ID changed, but using stored device ID for consistency'
          );

          // 更新存储的原生ID为当前值
          if (currentNativeId) {
            await storage.setItem(NATIVE_DEVICE_ID_KEY, currentNativeId);
          }
          if (currentSource) {
            await storage.setItem(DEVICE_ID_SOURCE_KEY, currentSource);
          }
        }
      }
      return storedDeviceId;
    }

    // 情况2: 没有存储的设备ID，但有原生设备标识符或Web客户端ID
    if (currentNativeId) {
      // 使用原生设备标识符或Web客户端ID作为设备ID
      await storage.setItem(DEVICE_ID_KEY, currentNativeId);
      await storage.setItem(NATIVE_DEVICE_ID_KEY, currentNativeId);
      if (currentSource) {
        await storage.setItem(DEVICE_ID_SOURCE_KEY, currentSource);
      }
      return currentNativeId;
    }

    // 情况3: 没有存储的设备ID，也没有原生设备标识符或Web客户端ID，生成随机ID
    const generatedId = await generateUUID();
    await storage.setItem(DEVICE_ID_KEY, generatedId);
    await storage.setItem(DEVICE_ID_SOURCE_KEY, DeviceIdSource.GENERATED);
    return generatedId;
  } catch (error) {
    console.error('Error getting device ID:', error);
    throw error;
  }
};

/**
 * 获取设备信息
 * @returns Promise<DeviceInfo> 设备信息对象
 */
export const getDeviceInfo = async () => {
  const deviceId = await getDeviceId();

  return {
    deviceId,
    deviceType: Device.deviceType,
    brand: Device.brand,
    manufacturer: Device.manufacturer,
    modelName: Device.modelName,
    osName: Device.osName,
    osVersion: Device.osVersion,
    appId: Application.applicationId,
    appName: Application.applicationName,
    appVersion: Application.nativeApplicationVersion,
    buildVersion: Application.nativeBuildVersion,
  };
};
