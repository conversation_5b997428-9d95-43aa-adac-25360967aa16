# @dp-frontend/utils

这是一个通用工具函数库，提供了各种在应用程序中常用的工具函数。

## 安装

```bash
pnpm --filter <your-app> add @dp-frontend/utils@workspace:*
```

## 功能

### 设备ID

提供了获取设备唯一标识符的功能。

```typescript
import { getDeviceId, getDeviceInfo } from '@dp-frontend/utils';

// 获取设备ID
const deviceId = await getDeviceId();
console.log('Device ID:', deviceId);

// 获取完整的设备信息
const deviceInfo = await getDeviceInfo();
console.log('Device Info:', deviceInfo);
```

#### 设备ID获取策略

设备ID的获取遵循增强的多层次策略：

1. 首先尝试从存储中获取已存储的设备ID
2. 如果有已存储的设备ID：
   - 即使原生设备标识符发生变化（例如应用重装后iOS的vendor ID变化），仍然使用之前存储的设备ID以保持一致性
   - 同时更新存储的原生设备标识符为当前值，以便跟踪变化
3. 如果没有已存储的设备ID：
   - 在Android设备上，尝试获取Android ID
   - 在iOS设备上，尝试获取iOS的vendor ID (IDFV)
   - 在Web环境中，尝试基于浏览器特征生成相对稳定的客户端ID
   - 如果无法获取设备特有的标识符，则使用expo-crypto生成一个加密安全的随机UUID（不附加时间戳）
4. 将获取到的设备ID和来源信息存储在适当的存储机制中以便下次使用

#### 多平台存储支持

为了支持不同平台，我们实现了一个存储适配器系统，按以下优先级尝试不同的存储方式：

1. **SecureStore**：在原生平台（iOS/Android）上使用expo-secure-store进行安全存储
2. **LocalStorage**：在Web平台上使用浏览器的localStorage进行存储
3. **内存存储**：作为最后的后备方案，使用内存Map进行临时存储

这种适配器设计使得设备ID功能可以在不同平台上无缝工作，而不需要修改上层代码。

#### 应对设备ID变化的策略

为了解决设备ID在应用重装后可能变化的问题，我们采用了以下策略：

1. **持久化存储**：使用适当的存储机制存储设备ID，这样即使应用重启也能保持一致
2. **原生ID跟踪**：同时存储原始的原生设备标识符，以便检测变化
3. **ID来源记录**：记录设备ID的来源（Android ID、iOS Vendor ID、Web客户端ID或生成的随机ID）
4. **优先使用存储的ID**：一旦生成并存储了设备ID，即使原生设备标识符发生变化，也继续使用之前的ID
5. **错误处理和回退机制**：在每一步都有适当的错误处理和回退机制，确保始终能获取到设备ID

#### 设备信息

`getDeviceInfo`函数返回的设备信息包括：

- `deviceId`: 设备唯一标识符
- `deviceType`: 设备类型（手机、平板等）
- `brand`: 设备品牌
- `manufacturer`: 设备制造商
- `modelName`: 设备型号名称
- `osName`: 操作系统名称
- `osVersion`: 操作系统版本
- `appId`: 应用ID
- `appName`: 应用名称
- `appVersion`: 应用版本
- `buildVersion`: 应用构建版本

## 依赖

- expo-device: 用于获取设备信息
- expo-application: 用于获取应用信息和iOS Vendor ID
- expo-secure-store: 用于在原生平台上安全存储设备ID
- expo-crypto: 用于生成加密安全的随机UUID和Web客户端ID
