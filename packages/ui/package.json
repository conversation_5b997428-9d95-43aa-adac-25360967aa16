{"name": "@dp-frontend/ui", "version": "0.1.0", "main": "./src/index.ts", "types": "./src/index.ts", "private": true, "exports": {".": "./src/index.ts", "./variables.stylex": "./src/theme/vars.stylex.ts"}, "scripts": {"lint": "eslint ."}, "dependencies": {"@expo/vector-icons": "^14.1.0", "radash": "^12.1.0", "clsx": "^2.1.1", "react-native-modal": "^14.0.0-rc.1", "react-native-confirmation-code-field": "^7.4.0"}, "peerDependencies": {"react": ">=18.0.0", "react-dom": ">=18.0.0", "react-native": ">=0.70.0"}, "devDependencies": {"react": "^18.3.1", "react-native": "0.76.9", "react-strict-dom": "^0.0.34", "react-strict-dom-tailwind": "0.2.3-beta.1"}}