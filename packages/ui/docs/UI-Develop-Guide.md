# UI 组件库总结

## 组件库结构

`@dp-frontend/ui` 组件库位于 `packages/ui/src` 目录下，采用了模块化的结构设计，主要包含以下几个部分：

### 目录结构

```
packages/ui/src/
├── components/       # UI组件
│   ├── Button/       # 按钮组件
│   │   ├── index.tsx # 组件实现
│   │   ├── styles.ts # 组件样式
│   │   └── Button.md # 组件文档
│   └── ...           # 其他组件
├── config-provider/  # 配置提供器
│   └── index.tsx     # 主题配置上下文
├── hook/             # 自定义钩子
│   └── index.tsx     # 样式相关钩子
├── theme/            # 主题定义
│   ├── theme.ts      # 主题样式
│   └── vars.stylex.ts # 颜色变量
└── index.ts          # 库入口文件
```

### 组件开发规范

每个组件应遵循以下结构和规范：

1. **组件目录结构**：
   ```
   components/ComponentName/
   ├── index.tsx      # 组件实现
   ├── styles.ts      # 组件样式定义
   └── ComponentName.md # 组件文档
   ```

2. **组件文档**：每个组件必须有对应的 Markdown 文档，包含：
   - 组件概述
   - 设计规范（尺寸、类型、状态、颜色等）
   - API 接口说明
   - 使用示例
   - 实现注意事项

3. **组件实现原则**：
   - 使用 React Strict DOM 的 `html.*` 元素
   - 使用 TypeScript 定义明确的 Props 接口
   - 提供合理的默认值
   - 实现必要的状态管理（如按钮的按下状态）
   - 支持自定义样式覆盖

## 样式系统设计

组件库采用 React Strict DOM 和 react-strict-dom-tailwind 实现跨平台一致的样式系统，主要特点如下：

### 1. 颜色变量系统

在 `theme/vars.stylex.ts` 中定义了两层颜色变量：

- **基础色板（一级颜色）**：使用 `css.defineVars` 定义基础颜色变量，如：
  ```typescript
  export const colors = css.defineVars({
    colorWhite: '#FFFFFF',
    colorBlack: '#000000',

    // 灰度颜色 - 0为黑色，1000为白色，以50为分隔点
    colorGray0: '#000000', // 黑色
    colorGray50: '#151514', // 接近黑色
    colorGray100: '#24231D', // 深灰色 - 主要文本色
    colorGray150: '#272636', // 深灰色变体
    colorGray500: '#808080', // 中灰色
    colorGray600: '#989898', // 禁用文本色
    colorGray880: '#D5D5D5', // 浅灰色边框
    colorGray950: '#F0F0F0', // 背景色
    colorGray1000: '#FFFFFF', // 白色

    // 功能色系
    colorRed500: '#FF3B30', // 危险色
    colorGreen500: '#34C759', // 成功色
    colorOrange500: '#FF9735', // 警告色
    // 其他基础颜色...
  });
  ```

- **语义化颜色（二级颜色）**：基于基础色板定义具有语义的颜色变量：
  ```typescript
  export const semanticColors = css.defineVars({
    // 默认颜色
    default: colors.colorWhite, // #FFFFFF
    defaultText: colors.colorGray100, // #24231D
    defaultBorder: colors.colorGray880, // #D5D5D5
    defaultDisabled: colors.colorGray940, // #E4E4E4
    defaultTextDisabled: colors.colorGray100, // #24231D
    defaultBorderDisabled: colors.colorGray950, // #F0F0F0

    // 主要颜色
    primary: colors.colorGray100, // #24231D
    primaryText: colors.colorWhite,
    primaryDisabled: colors.colorGray600, // #989898
    primaryTextDisabled: colors.colorGray950, // #F0F0F0

    // 功能颜色
    danger: colors.colorRed500, // #FF3B30
    dangerText: colors.colorWhite,
    success: colors.colorGreen500, // #34C759
    successText: colors.colorWhite,
    warning: colors.colorOrange500, // #FF9735
    warningText: colors.colorWhite,
  });
  ```

### 2. 主题系统

主题系统是组件库的核心，它提供了一种统一的方式来定义和应用样式，确保在不同平台上保持一致的外观。主题系统由以下几个部分组成：

#### 2.1 主题定义

组件样式在各自的 `styles.ts` 文件中定义，然后在 `theme/theme.ts` 中汇总：

```typescript
// 在 components/Button/styles.ts 中定义按钮样式
export const buttonTheme: StyleObject = {
  'btn-flex': tw('flex-cc'),

  'btn-disabled': tw('cursor-not-allowed'),

  // 尺寸
  'btn-large': tw('h-[50px] px-[91px] py-[16px] text-[14px]'),
  'btn-medium': tw('h-[36px] px-[20px] py-[9px] text-[14px]'),
  'btn-small': tw('h-[28px] px-[14px] py-[9px] text-[12px]'),

  // 图标
  'btn-icon': tw('mr-2'),

  // 形状
  'btn-square': tw('rounded-[0px]'),
  'btn-rounded': tw('rounded-[999px]'),

  // 颜色
  ...css.create({
    'btn-default': {
      color: semanticColors.defaultText, // #24231D - 深灰色
      borderColor: semanticColors.defaultBorder, // #D5D5D5 - 浅灰色边框
      backgroundColor: semanticColors.default, // #FFFFFF - 白色背景
    },
    'btn-default-pressed': {
      backgroundColor: colors.colorGray950, // #F0F0F0 - 背景色
    },
    'btn-primary': {
      color: semanticColors.primaryText,
      borderColor: semanticColors.primary,
      backgroundColor: semanticColors.primary,
    },
    'btn-primary-pressed': {
      backgroundColor: colors.colorGray150, // #272636 - 深灰色变体
    },
    'btn-primary-disabled': {
      color: semanticColors.primaryTextDisabled,
      borderColor: semanticColors.primaryDisabled,
      backgroundColor: semanticColors.primaryDisabled,
    },
  }),

  // 填充
  ...css.create({
    'btn-solid': {
      borderWidth: 0.5,
    },
    'btn-outline': {
      backgroundColor: 'transparent',
      borderWidth: 0.5,
    },
    'btn-none': {
      backgroundColor: 'transparent',
      borderWidth: 0,
    },
  }),
  // 其他样式...
};

// 在 theme/theme.ts 中汇总所有组件样式
export const theme: StyleObject = {
  ...buttonTheme,
  // 其他组件样式...
};
```

这些主题样式可以在 Tailwind 类中使用，例如 `tw('btn-primary')` 会应用定义的样式。

#### 2.2 主题样式类型

主题系统支持两种类型的样式定义：

1. **CSS 样式对象**：使用 `css.create()` 创建的样式对象，可以直接引用颜色变量：
   ```typescript
   'btn-default': css.create({
     color: semanticColors.defaultText,
     backgroundColor: semanticColors.default
   })
   ```

2. **Tailwind 类组合**：使用 `tw()` 函数创建的 Tailwind 类组合：
   ```typescript
   'btn-small': tw('h-[28px] px-[14px] py-[9px] text-[12px]')
   ```

#### 2.3 主题上下文

主题通过 React Context 提供给整个应用，在 `config-provider/index.tsx` 中实现：

```typescript
export interface ConfigProviderProps {
  theme?: Record<string, unknown>;
}

const Context = React.createContext<ConfigProviderProps>({
  theme: {},
});

export const ConfigProvider = ({
  theme,
  children,
}: PropsWithChildren<ConfigProviderProps>) => {
  return <Context.Provider value={{ theme }}>{children}</Context.Provider>;
};
```

在应用根组件中使用 `ConfigProvider` 提供主题：

```tsx
// apps/dp-client/src/App.tsx
<ConfigProvider theme={theme}>
  <Navigation pages={pageConfigs} />
</ConfigProvider>
```

#### 2.4 主题与 Tailwind 集成

主题系统与 Tailwind 集成，允许在 Tailwind 类中使用自定义主题样式。这是通过 `useThemeTailwind` 钩子实现的：

```typescript
export const useThemeTailwind = () => {
  const { theme } = useConfigProvider();
  return {
    tw: (classNames: string, options?: TailwindOptions) => {
      return tailwind(classNames, { ...options, extraStyles: theme });
    },
  };
};
```

这个增强版的 `tw` 函数会将主题样式作为 `extraStyles` 传递给 Tailwind，使得可以在 Tailwind 类中引用主题样式：

```tsx
const { tw } = useThemeTailwind();
<html.div style={tw('btn-default flex-1')}>
  // 这里会应用 btn-default 主题样式和 flex-1 Tailwind 类
</html.div>
```

### 3. 样式钩子

组件库提供了两个主要的样式钩子：

- **useThemeTailwind**：获取主题上下文并提供增强的 `tw` 函数：
  ```typescript
  export const useThemeTailwind = () => {
    const { theme } = useConfigProvider();
    return {
      tw: (classNames: string, options?: TailwindOptions) => {
        return tailwind(classNames, { ...options, extraStyles: theme });
      },
    };
  };
  ```

- **其他自定义钩子**：根据需要可以创建其他样式相关的钩子函数，用于特定场景下的样式应用

### 4. 组件样式定义

组件样式应采用基于状态的映射方式，通过自定义钩子函数动态生成样式：

```typescript
// 按钮样式钩子示例
export const useButtonTailwind = (
  props: Pick<ButtonProps, 'size' | 'color' | 'disabled' | 'shape' | 'fill'> & {
    isPressed?: boolean;
  }
) => {
  const {
    size = 'medium',
    color = 'default',
    disabled = false,
    isPressed = false,
    shape = 'rounded',
    fill = 'solid',
  } = props;

  // 使用 clsx 组合样式类名
  const buttonStyles = useMemo(
    () =>
      clsx({
        'btn-flex': true,
        'btn-disabled': disabled,

        // 尺寸变体
        'btn-large': size === 'large',
        'btn-medium': !['large', 'small'].includes(size),
        'btn-small': size === 'small',

        // 形状变体
        'btn-square': shape === 'square',
        'btn-rounded': shape !== 'square',

        // 颜色和状态变体
        'btn-default': !['primary', 'success', 'danger', 'warning'].includes(color),
        'btn-primary': color === 'primary',
        'btn-success': color === 'success',
        'btn-danger': color === 'danger',
        'btn-warning': color === 'warning',

        // 填充变体
        'btn-solid': !['outline', 'none'].includes(fill),
        'btn-outline': fill === 'outline',
        'btn-none': fill === 'none',
      }),
    [size, color, disabled, fill, shape, isPressed]
  );

  return buttonStyles;
};
```

这种方式可以根据组件的属性和状态动态生成样式类名，然后在组件中使用：

```tsx
const buttonStyles = useButtonTailwind({
  size,
  color,
  disabled,
  shape,
  fill,
  isPressed
});
<html.button style={[tw(buttonStyles), style]}>
  {renderIcon()}
  <html.div>{children}</html.div>
</html.button>
```

## 组件使用方式

### 1. 配置主题

在应用根组件中使用 `ConfigProvider` 提供主题：

```tsx
// apps/dp-client/src/App.tsx
import { ConfigProvider, theme } from '@dp-frontend/ui';

const App: React.FC = () => {
  return (
    <ConfigProvider theme={theme}>
      <Navigation pages={pageConfigs} />
    </ConfigProvider>
  );
};
```

### 2. 使用组件

在页面中导入并使用组件：

```tsx
import { Button, useThemeTailwind } from '@dp-frontend/ui';

const Home = () => {
  const { tw } = useThemeTailwind();

  const handleButtonClick = () => {
    console.log('Button clicked');
  };

  return (
    <html.div style={tw('flex-1 bg-gray-50 flex flex-col pt-5')}>
      <Button
        onClick={handleButtonClick}
        color="primary"
        size="small"
        shape="rounded"
        fill="solid"
      >
        前往关于页面
      </Button>
    </html.div>
  );
};
```

### 3. 组件内部实现

组件内部实现应遵循以下模式：

```tsx
// Button组件实现示例
export const Button: React.FC<ButtonProps> = (props) => {
  const {
    onClick,
    color = 'default',
    size = 'large',
    disabled = false,
    shape = 'rounded',
    fill = 'solid',
    style,
    icon,
    children,
  } = props;
  const { tw } = useThemeTailwind();

  // 使用状态跟踪按钮的按下状态
  const [isPressed, setIsPressed] = useState(false);

  // 使用样式钩子生成样式
  const buttonStyles = useButtonTailwind({
    size,
    color,
    disabled,
    shape,
    fill,
    isPressed,
  });

  // 处理按钮按下事件
  const handlePressIn = () => {
    if (!disabled) setIsPressed(true);
  };

  // 处理按钮释放事件
  const handlePressOut = () => {
    setIsPressed(false);
  };

  const renderIcon = () => {
    if (!icon) return null;
    return <html.span style={tw('btn-icon')}>{icon}</html.span>;
  };

  // 渲染按钮
  return (
    <html.button
      style={[tw(buttonStyles), style]}
      disabled={disabled}
      onClick={onClick}
      onMouseDown={handlePressIn}
      onMouseUp={handlePressOut}
      onMouseLeave={handlePressOut}
      onTouchStart={handlePressIn}
      onTouchEnd={handlePressOut}
    >
      {renderIcon()}
      <html.div>{children}</html.div>
    </html.button>
  );
};
```

## 样式最佳实践

### 1. 使用 Tailwind 类

优先使用 Tailwind 类定义样式：

```tsx
<html.div style={tw('flex-1 bg-gray-50 p-4')}>
  <html.p style={tw('text-lg font-bold text-blue-500')}>
    Hello World
  </html.p>
</html.div>
```

### 2. 组合样式

使用数组组合多个样式源：

```tsx
<html.div style={[
  tw('flex-1'),
  isActive && tw('bg-blue-500'),
  !isActive && tw('bg-gray-200')
]}>
  Content
</html.div>
```

### 3. 样式限制

React Strict DOM 不支持以下 CSS 特性：

- CSS 简写属性（如 `border`, `margin`, `padding`）
- `position: fixed`
- 某些伪类选择器

请使用完整属性名：

```tsx
// 错误
const styles = css.create({
  box: {
    margin: 10, // 简写属性不支持
  },
});

// 正确
const styles = css.create({
  box: {
    marginTop: 10,
    marginRight: 10,
    marginBottom: 10,
    marginLeft: 10,
  },
});
```

## 跨平台注意事项

React Strict DOM 使用文件命名约定来区分平台：

- `*.web.tsx` - 仅在 Web 平台使用的代码
- `*.native.tsx` - 仅在原生平台使用的代码
- `*.tsx` - 在两个平台共享的代码

## 组件设计规范

每个组件都应该遵循统一的设计规范，确保视觉和交互的一致性。我们提供了一个[组件设计规范模板](./Component-Template.md)，用于指导新组件的创建。

在开发新组件时，请参考此模板创建组件专属的设计规范文档，详细描述组件的：

- 尺寸变体
- 类型变体
- 状态变体
- 颜色规范
- 文字和内边距规范
- API 接口
- 使用示例
- 实现注意事项

所有组件的设计规范应严格遵循 Figma 设计稿，确保组件的视觉表现与设计稿保持一致。

## 组件开发要点总结

开发组件时应遵循以下要点，确保组件的质量和一致性：

### 1. 设计规范一致性

- 严格遵循 Figma 设计稿中的尺寸、颜色、字体和间距规范
- 使用语义化的颜色变量，便于主题切换和样式统一
- 确保组件在各种状态下的视觉表现与设计稿一致

### 2. 组件结构与状态管理

- 将组件实现与样式定义分离，保持代码清晰
- 实现必要的状态管理，如激活状态、悬停状态等
- 使用 React 的 `useState` 钩子管理组件内部状态

### 3. 样式组织与性能优化

- 将样式定义放在独立的 `styles.ts` 文件中
- 使用 `clsx` 库组合条件样式类名
- 使用 `useMemo` 优化样式生成性能，避免不必要的重新计算

### 4. 事件处理与交互

- 实现完整的事件处理，包括鼠标和触摸事件
- 确保禁用状态下不触发事件
- 提供良好的键盘可访问性支持

### 5. 跨平台兼容性

- 使用 React Strict DOM 的 `html.*` 元素确保跨平台一致性
- 避免使用平台特定的样式属性
- 测试组件在 Web 和移动平台上的表现

### 6. 可扩展性与可定制性

- 支持自定义样式覆盖默认样式
- 提供合理的默认值
- 设计灵活的 API，满足各种使用场景

### 7. 文档与测试

- 为每个组件创建完整的文档，使用 [组件设计规范模板](./Component-Template.md)
- 在代码中添加详细的注释说明
- 编写单元测试确保组件功能正常

按照以上规范和要点开发的组件将具有一致的外观和行为，便于维护和扩展，同时确保在不同平台上的一致性。
