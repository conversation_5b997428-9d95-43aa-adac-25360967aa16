# 组件设计规范模板

本文档提供了创建新组件时应遵循的设计规范模板。每个组件应该有自己的文档，按照以下结构编写。

## 组件名称

> 例如：Button 按钮组件

## 组件概述

简要描述组件的用途、功能和适用场景。

> 例如：Button 是一个通用按钮组件，用于触发操作或提交表单。它支持多种尺寸、类型和状态，适用于各种交互场景。

## 设计规范

### 尺寸变体

描述组件支持的尺寸变体及其适用场景。

> 例如：
> - **大号 (large)**: 高度为 50px，适用于主要操作按钮
> - **中号 (medium)**: 高度为 36px，适用于一般操作按钮
> - **小号 (small)**: 高度为 28px，适用于辅助操作或空间受限的场景

### 颜色变体

描述组件支持的颜色变体及其适用场景。

> 例如：
> - **默认 (default)**: 白色背景，深灰色文字，适用于次要操作
> - **主要 (primary)**: 深灰色背景，白色文字，用于页面主要操作
> - **成功 (success)**: 绿色背景，白色文字，用于表示成功或确认操作
> - **危险 (danger)**: 红色背景，白色文字，用于表示危险或删除操作
> - **警告 (warning)**: 橙色背景，白色文字，用于表示警告操作

### 填充变体

描述组件支持的填充变体及其适用场景。

> 例如：
> - **实心 (solid)**: 有背景色，无边框，默认样式
> - **描边 (outline)**: 透明背景，有边框，适用于次要操作
> - **无填充 (none)**: 透明背景，无边框，仅文字和图标，适用于低强调操作

### 形状变体

描述组件支持的形状变体及其适用场景。

> 例如：
> - **圆角 (rounded)**: 圆角按钮，默认样式
> - **方形 (square)**: 直角按钮，适用于特定场景

### 状态变体

描述组件支持的状态变体。

> 例如：
> - **默认 (default)**: 组件的正常状态
> - **悬停 (hover)**: 鼠标悬停时的状态
> - **按下 (pressed)**: 组件被点击时的状态
> - **禁用 (disabled)**: 组件不可用的状态

### 颜色规范

描述组件在不同类型和状态下的颜色规范，应参考 Figma 设计稿并使用语义化颜色变量。

> 例如：
> - **主要按钮**:
>   - 默认: 背景色 primary，文字颜色 primaryText
>   - 禁用: 背景色 primaryDisabled，文字颜色 primaryTextDisabled，透明度 50%
>
> - **次要按钮**:
>   - 默认: 背景色 secondary，文字颜色 secondaryText
>   - 禁用: 背景色 secondaryDisabled，文字颜色 secondaryTextDisabled，透明度 70%

### 文字和内边距规范

描述组件的文字大小、字重和内边距规范。

> 例如：
> - **大号组件**: 14px 字体，Regular (400)，内边距 16px 91px (垂直 水平)
> - **中号组件**: 14px 字体，Medium (500)，内边距 9px 20px (垂直 水平)
> - **小号组件**: 12px 字体，Medium (500)，内边距 9px 14px (垂直 水平)

## 组件 API

### 属性接口

使用 TypeScript 定义组件的属性接口。

```typescript
export interface ComponentProps {
  /**
   * 组件尺寸
   * @default 'medium'
   */
  size?: 'large' | 'medium' | 'small';

  /**
   * 组件颜色
   * @default 'default'
   */
  color?: 'default' | 'primary' | 'success' | 'danger' | 'warning';

  /**
   * 组件形状
   * @default 'rounded'
   */
  shape?: 'square' | 'rounded';

  /**
   * 组件填充
   * @default 'solid'
   */
  fill?: 'solid' | 'outline' | 'none';

  /**
   * 是否禁用
   * @default false
   */
  disabled?: boolean;

  /**
   * 自定义样式
   */
  style?: StyleType;

  /**
   * 组件图标
   */
  icon?: React.ReactNode;

  /**
   * 点击事件处理函数
   */
  onClick: () => void;

  /**
   * 组件内容
   */
  children?: React.ReactNode;
}
```

## 使用示例

提供组件的基本使用示例和常见场景示例。

```tsx
import { Component } from '@dp-frontend/ui';

// 基本用法
<Component onClick={() => console.log('Clicked')}>
  基本用法
</Component>

// 不同尺寸
<Component size="large">大号组件</Component>
<Component size="medium">中号组件</Component>
<Component size="small">小号组件</Component>

// 不同颜色
<Component color="default">默认组件</Component>
<Component color="primary">主要组件</Component>
<Component color="success">成功组件</Component>
<Component color="danger">危险组件</Component>
<Component color="warning">警告组件</Component>

// 不同填充
<Component fill="solid">实心组件</Component>
<Component fill="outline">描边组件</Component>
<Component fill="none">无填充组件</Component>

// 不同形状
<Component shape="rounded">圆角组件</Component>
<Component shape="square">方形组件</Component>

// 带图标
<Component icon={<IconComponent />}>带图标组件</Component>

// 禁用状态
<Component disabled>禁用组件</Component>
```

## 实现注意事项

列出实现组件时需要注意的事项和最佳实践。

1. 使用 React Strict DOM 的 `html.*` 元素实现组件
2. 使用 `useThemeTailwind` 钩子提供的 `tw()` 函数应用 Tailwind 样式
3. 使用自定义钩子根据组件的属性和状态动态生成样式
4. 支持自定义样式覆盖默认样式
5. 确保组件在禁用状态下不触发事件
6. 实现必要的状态管理（如按下状态）
7. 确保组件在不同平台上具有一致的外观和行为
