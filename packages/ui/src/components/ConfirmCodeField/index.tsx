import type { ComponentProps } from 'react';
import React, { forwardRef, useImperativeHandle, useRef } from 'react';
import type { TextInput } from 'react-native';
import { Platform } from 'react-native';
import {
  CodeField,
  Cursor,
  useClearByFocusCell,
} from 'react-native-confirmation-code-field';
import { html } from 'react-strict-dom';
import type { StyleObject } from 'react-strict-dom-tailwind';

import { useControlledState, useThemeTailwind } from '../../hooks';
import { useConfirmCodeFieldTailwind } from './styles';

// 确认码输入框属性
export interface ConfirmCodeFieldProps {
  /**
   * 输入框数量
   * @default 6
   */
  length?: number;

  /**
   * 输入值（受控模式）
   */
  value: string;

  /**
   * 输入值变化事件处理函数
   */
  onChange: (value: string) => void;

  /**
   * 是否自动获取焦点
   * @default false
   */
  autoFocus?: boolean;

  /**
   * 是否显示错误状态
   * @default false
   */
  error?: boolean;

  /**
   * 错误提示文本
   */
  errorMessage?: string;

  /**
   * 输入完成回调
   */
  onComplete?: (value: string) => void;

  /**
   * 是否禁用
   * @default false
   */
  disabled?: boolean;

  /**
   * 自定义样式
   */
  style?: StyleObject;

  /**
   * 单个输入框尺寸
   * @default 50
   */
  cellSize?: number | { height: number | string; width: number | string };

  /**
   * 输入框间距
   * @default 10
   */
  cellSpacing?: number;

  /**
   * 是否显示为密码模式
   * @default false
   */
  secureTextEntry?: boolean;

  /**
   * 是否显示光标
   * @default true
   */
  showCursor?: boolean;
}

// 确认码输入框 Ref 接口
export interface ConfirmCodeFieldRef {
  /**
   * 获取焦点
   */
  focus: () => void;

  /**
   * 失去焦点
   */
  blur: () => void;

  /**
   * 清空输入内容
   */
  clear: () => void;
}

/**
 * ConfirmCodeField 确认码输入组件
 * 基于 react-native-confirmation-code-field 实现
 */
export const ConfirmCodeField = forwardRef<
  ConfirmCodeFieldRef,
  ConfirmCodeFieldProps
>((componentProps, ref) => {
  const {
    length = 6,
    value: controlledValue,
    onChange,
    autoFocus = false,
    error = false,
    errorMessage,
    onComplete,
    disabled = false,
    style,
    cellSize = 50,
    cellSpacing = 10,
    secureTextEntry = false,
    showCursor = true,
  } = componentProps;

  // 使用useControlledState处理受控和非受控状态
  const [value, setValue] = useControlledState(controlledValue, '', onChange);

  // 引用CodeField组件
  // 使用any类型，因为CodeField的ref类型定义不完整
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const codeFieldRef = useRef<any>(null);

  // 清除单元格内容
  const [cellInputProps] = useClearByFocusCell({
    value,
    setValue,
  });

  // 使用主题样式
  const { tw } = useThemeTailwind();

  // 获取样式
  const { containerStyles, cellStyles, errorTextStyles } =
    useConfirmCodeFieldTailwind({
      error,
      disabled,
    });

  // 暴露方法给 ref
  useImperativeHandle(ref, () => ({
    focus: () => {
      if (!disabled && codeFieldRef.current) {
        // 调用focus方法
        codeFieldRef.current?.focus();
      }
    },
    blur: () => {
      if (codeFieldRef.current) {
        // 调用blur方法
        codeFieldRef.current?.blur();
      }
    },
    clear: () => {
      setValue('');
      if (!disabled && codeFieldRef.current) {
        setTimeout(() => {
          // 调用focus方法
          codeFieldRef.current?.focus();
        }, 0);
      }
    },
  }));

  // 处理值变化
  const handleChange = (text: string) => {
    setValue(text);

    // 如果输入完成，触发完成回调
    if (text.length === length && onComplete) {
      onComplete(text);
    }
  };

  // 渲染单元格
  const renderCell = ({
    index,
    symbol,
    isFocused,
  }: {
    index: number;
    symbol: string;
    isFocused: boolean;
  }) => {
    const displayValue = symbol ? (secureTextEntry ? '•' : symbol) : '';
    const showFocusCursor = isFocused && showCursor;
    const cellWidth = typeof cellSize === 'number' ? cellSize : cellSize.width;
    const cellHeight =
      typeof cellSize === 'number' ? cellSize : cellSize.height;

    return (
      <html.div
        key={index}
        style={[
          tw(cellStyles),
          tw(`w-[${cellWidth}] h-[${cellHeight}]`),
          index > 0 ? tw(`ml-[${cellSpacing}]`) : {},
        ]}
      >
        {/* 直接显示内容，不使用onLayout */}
        <html.span style={tw('text-2xl')}>{displayValue}</html.span>
        {showFocusCursor && (
          <html.span>
            <Cursor />
          </html.span>
        )}
      </html.div>
    );
  };

  return (
    <html.div style={[tw(containerStyles), style]}>
      <html.div style={tw('flex flex-col items-start')}>
        {/* 输入框部分 */}
        <CodeField
          ref={codeFieldRef}
          value={value}
          onChangeText={handleChange}
          cellCount={length}
          rootStyle={tw('flex flex-row w-full')}
          keyboardType="number-pad"
          textContentType="oneTimeCode"
          // 移除autoComplete属性，因为它在类型定义中不存在
          renderCell={renderCell}
          editable={!disabled}
          autoFocus={autoFocus && !disabled}
          autoComplete={
            Platform.select({
              android: 'sms-otp',
              default: 'one-time-code',
            }) as ComponentProps<typeof TextInput>['autoComplete']
          }
          // 移除autoComplete属性，因为它在类型定义中不存在或类型不匹配
          {...cellInputProps}
        />

        {/* 错误提示 - 直接放在输入框下方，与第一个输入框左对齐 */}
        {error && errorMessage && (
          <html.div style={tw(`${errorTextStyles} mt-2`)}>
            {errorMessage}
          </html.div>
        )}
      </html.div>
    </html.div>
  );
});

// 设置组件名称
ConfirmCodeField.displayName = 'ConfirmCodeField';
