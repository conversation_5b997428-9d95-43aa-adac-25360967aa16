# ConfirmCodeField 确认码输入组件

## 组件概述

ConfirmCodeField 是一个用于输入验证码、PIN码等数字的分离式输入组件，基于 `react-native-confirmation-code-field` 库实现。它提供了分离式输入框和良好的输入体验，支持自动聚焦、自动跳转、粘贴识别等功能，适用于验证码、密码、PIN码等各种数字输入场景。组件确保在 Web 和移动平台上具有一致的外观和行为。

## 设计规范

### 尺寸变体

确认码输入框提供一种标准尺寸：

- **标准尺寸**: 每个输入框宽高为 50px，适用于所有数字输入场景

### 功能变体

确认码输入框支持以下功能变体：

- **基础数字输入框**: 提供基本的数字输入功能
- **自动聚焦输入框**: 组件挂载后自动获取焦点
- **自动提交输入框**: 输入完成后自动触发提交事件
- **密码模式输入框**: 输入内容显示为圆点

### 状态变体

确认码输入框支持以下状态：

- **默认状态**: 输入框的初始状态
- **聚焦状态**: 当前输入位置高亮显示
- **已填状态**: 已经输入数字的状态
- **错误状态**: 验证失败时的状态
- **禁用状态**: 输入框不可交互的状态

### 颜色规范

- **默认状态**:
  - 背景色: #F1F1F1
  - 边框: 无
  - 文字颜色: #000000

- **聚焦状态**:
  - 背景色: #F1F1F1
  - 边框: 0.5px #000000
  - 文字颜色: #000000

- **已填状态**:
  - 背景色: #F1F1F1
  - 边框: 无
  - 文字颜色: #000000

- **错误状态**:
  - 背景色: #FFFBFB
  - 边框: 0.5px #E72323
  - 文字颜色: #000000
  - 错误提示文字颜色: #E72323

- **禁用状态**:
  - 背景色: #F1F1F1 (50% 透明度)
  - 边框: 无
  - 文字颜色: #000000 (50% 透明度)

### 文字规范

- 输入数字: 18px，Noto Sans TC，Medium (500)
- 错误提示: 12px，Noto Sans TC，Regular (400)

### 间距规范

- 输入框间距: 10px
- 错误提示与输入框间距: 8px

## 组件 API

### 属性接口

```typescript
export interface ConfirmCodeFieldProps {
  /**
   * 输入框数量
   * @default 6
   */
  length?: number;

  /**
   * 输入值（受控模式）
   */
  value: string;

  /**
   * 输入值变化事件处理函数
   */
  onChange: (value: string) => void;

  /**
   * 是否自动获取焦点
   * @default false
   */
  autoFocus?: boolean;

  /**
   * 是否显示错误状态
   * @default false
   */
  error?: boolean;

  /**
   * 错误提示文本
   */
  errorMessage?: string;

  /**
   * 输入完成回调
   */
  onComplete?: (value: string) => void;

  /**
   * 是否禁用
   * @default false
   */
  disabled?: boolean;

  /**
   * 自定义样式
   */
  style?: StyleType;

  /**
   * 单个输入框尺寸
   * @default 50
   */
  cellSize?: number;

  /**
   * 输入框间距
   * @default 10
   */
  cellSpacing?: number;

  /**
   * 是否显示为密码模式
   * @default false
   */
  secureTextEntry?: boolean;

  /**
   * 是否显示光标
   * @default true
   */
  showCursor?: boolean;
}

export interface ConfirmCodeFieldRef {
  /**
   * 获取焦点
   */
  focus: () => void;

  /**
   * 失去焦点
   */
  blur: () => void;

  /**
   * 清空输入内容
   */
  clear: () => void;
}
```

## 使用示例

### 基本用法

```tsx
import { ConfirmCodeField } from '@dp-frontend/ui';
import { useState } from 'react';

// 基本数字输入框
const [code, setCode] = useState('');

<ConfirmCodeField
  value={code}
  onChange={(newValue) => setCode(newValue)}
  length={6}
  autoFocus
/>

// 带错误提示的数字输入框
<ConfirmCodeField
  value={code}
  onChange={(newValue) => setCode(newValue)}
  error={true}
  errorMessage="验证码错误，请重新输入"
/>

// 自定义尺寸的数字输入框
<ConfirmCodeField
  value={code}
  onChange={(newValue) => setCode(newValue)}
  cellSize={40}
  cellSpacing={8}
/>

// 密码模式的数字输入框
<ConfirmCodeField
  value={code}
  onChange={(newValue) => setCode(newValue)}
  secureTextEntry={true}
/>

// 禁用状态的数字输入框
<ConfirmCodeField
  value={code}
  onChange={(newValue) => setCode(newValue)}
  disabled={true}
/>

// 带完成回调的数字输入框
<ConfirmCodeField
  value={code}
  onChange={(newValue) => setCode(newValue)}
  onComplete={(value) => {
    console.log('验证码输入完成:', value);
    // 执行验证逻辑
    verifyCode(value);
  }}
/>
```

### 使用 Ref 示例

```tsx
import { ConfirmCodeField } from '@dp-frontend/ui';
import { useRef } from 'react';

const CodeFieldWithRef = () => {
  const [code, setCode] = useState('');
  const codeFieldRef = useRef<ConfirmCodeFieldRef>(null);

  const handleFocus = () => {
    codeFieldRef.current?.focus();
  };

  const handleClear = () => {
    codeFieldRef.current?.clear();
  };

  return (
    <>
      <ConfirmCodeField
        ref={codeFieldRef}
        value={code}
        onChange={(newValue) => setCode(newValue)}
      />
      <html.div style={tw('flex gap-2 mt-2')}>
        <html.button onClick={handleFocus} style={tw('btn-small')}>获取焦点</html.button>
        <html.button onClick={handleClear} style={tw('btn-small')}>清空内容</html.button>
      </html.div>
    </>
  );
};
```

## 实现注意事项

1. 基于 `react-native-confirmation-code-field` 库实现，确保跨平台兼容性
2. 使用 React Strict DOM 的 `html.*` 元素实现自定义样式部分
3. 使用 `useThemeTailwind` 钩子提供的 `tw()` 函数应用 Tailwind 样式
4. 支持自动跳转焦点，输入一位数字后自动跳转到下一位
5. 实现错误状态的视觉反馈，包括输入框变红和错误提示
6. 确保组件在禁用状态下不可交互
7. 支持自定义样式覆盖默认样式
8. 确保组件在不同平台上具有一致的外观和行为
9. 支持通过 ref 访问组件的方法
10. 支持密码模式，输入内容显示为圆点
