import clsx from 'clsx';
import { useMemo } from 'react';
import { css } from 'react-strict-dom';
import type { StyleObject } from 'react-strict-dom-tailwind';
import { tw } from 'react-strict-dom-tailwind';

import { colors } from '../../theme/vars.stylex';

/**
 * ConfirmCodeField组件的样式定义
 */
export const confirmCodeFieldTheme: StyleObject = {
  // 容器样式
  'confirm-code-field-container': tw('flex flex-col'),

  // 输入框容器
  'confirm-code-field-wrapper': tw('flex flex-row items-center'),

  // 单个输入框样式
  'confirm-code-field-cell': tw(
    'box-border flex items-center justify-center rounded-[8px] text-[18px] font-medium border-[0.5px] border-solid'
  ),

  // 错误提示样式
  'confirm-code-field-error-text': tw('text-[12px] text-error', {
    extraStyles: css.create({ 'text-error': { color: colors.colorRed500 } }),
  }),

  // 状态样式
  ...css.create({
    // 默认状态
    'confirm-code-field-cell-default': {
      backgroundColor: colors.colorGray950,
      color: colors.colorBlack,
      borderColor: colors.colorGray950,
    },

    // 聚焦状态
    'confirm-code-field-cell-focused': {
      backgroundColor: colors.colorGray950,
      color: colors.colorBlack,
      borderColor: colors.colorGray950,
    },

    // 已填状态
    'confirm-code-field-cell-filled': {
      backgroundColor: colors.colorGray950,
      color: colors.colorBlack,
      borderColor: colors.colorGray950,
    },

    // 错误状态
    'confirm-code-field-cell-error': {
      backgroundColor: '#FFFBFB',
      color: colors.colorBlack,
      borderColor: colors.colorRed500,
    },

    // 禁用状态
    'confirm-code-field-cell-disabled': {
      backgroundColor: colors.colorGray950,
      color: colors.colorBlack,
      borderColor: colors.colorGray950,
      opacity: 0.5,
    },
  }),
};

/**
 * 根据组件状态生成样式
 */
export const useConfirmCodeFieldTailwind = ({
  error = false,
  disabled = false,
}: {
  error?: boolean;
  disabled?: boolean;
}) => {
  // 容器样式
  const containerStyles = useMemo(() => 'confirm-code-field-container', []);

  // 单元格样式
  const cellStyles = useMemo(
    () =>
      clsx({
        'confirm-code-field-cell': true,
        'confirm-code-field-cell-default': !error && !disabled,
        'confirm-code-field-cell-error': error && !disabled,
        'confirm-code-field-cell-disabled': disabled,
      }),
    [error, disabled]
  );

  // 错误文本样式
  const errorTextStyles = useMemo(() => 'confirm-code-field-error-text', []);

  return {
    containerStyles,
    cellStyles,
    errorTextStyles,
  };
};
