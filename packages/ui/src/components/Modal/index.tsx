import React, { useMemo } from 'react';
import type {
  ModalProps as RNModalProps,
  SupportedAnimation,
} from 'react-native-modal';
import { default as RNModal } from 'react-native-modal';
import { html } from 'react-strict-dom';

import { useThemeTailwind } from '../../hooks';
import type { UseModalTypes } from '../../hooks/useModal';
import type { StyleType } from '../Button';
import { useModalTailwind } from './styles';

// 模态框尺寸
export type ModalSize = 'small' | 'medium' | 'large' | 'full';

// 模态框位置
export type ModalPosition = 'center' | 'bottom';

// 模态框动画类型
export type ModalAnimationType = 'none' | 'slide' | 'fade';

// 模态框属性
export interface ModalProps extends Omit<Partial<RNModalProps>, 'children'> {
  /**
   * 传递modal实例
   */
  modal?: UseModalTypes;

  /**
   * 是否可见
   * @default false
   */
  visible?: boolean;

  /**
   * 关闭事件处理函数
   */
  onClose?: () => void;

  /**
   * 模态框标题
   */
  title?: React.ReactNode;

  /**
   * 模态框内容
   */
  content?: React.ReactNode;

  /**
   * 模态框底部内容
   */
  footer?: React.ReactNode;

  /**
   * 模态框尺寸
   * @default 'full'
   */
  size?: ModalSize;

  /**
   * 模态框位置
   * @default 'bottom'
   */
  position?: ModalPosition;

  /**
   * 模态框动画类型
   * @default 'slide'
   */
  animationType?: ModalAnimationType;

  /**
   * 是否在点击蒙层时关闭
   * @default true
   */
  maskClosable?: boolean;

  /**
   * 是否显示关闭图标
   * @default false
   */
  closable?: boolean;

  /**
   * 自定义样式
   */
  style?: StyleType;

  /**
   * 自定义蒙层样式
   */
  backdropStyle?: StyleType;

  /**
   * 自定义内容区域样式
   */
  contentStyle?: StyleType;

  /**
   * 模态框内容
   */
  children?: React.ReactNode;
}

/**
 * 通用模态框组件
 *
 * 基于Figma设计稿实现，支持多种尺寸、位置和状态
 */
export const Modal: React.FC<ModalProps> = ({ modal, ...props }) => {
  const {
    visible = false,
    onClose,
    title,
    content,
    footer,
    size = 'full',
    position = 'bottom',
    maskClosable = true,
    closable = false,
    style,
    contentStyle,
    animationType = 'slide',
  } = { ...modal?.modalProps, ...props };

  const { tw } = useThemeTailwind();

  // 获取模态框样式
  const modalStyles = useModalTailwind({
    size,
    position,
  });

  const animationTypes = useMemo(() => {
    if (animationType === 'none') {
      return {};
    }
    if (animationType === 'slide') {
      return {
        animationIn: 'slideInUp',
        animationOut: 'slideOutDown',
      };
    }
    return {
      animationIn: 'fadeIn',
      animationOut: 'fadeOut',
    };
  }, [animationType]);

  // 渲染标题
  const renderTitle = () => {
    if (!title) return null;
    return <html.div style={tw('modal-title')}>{title}</html.div>;
  };

  // 渲染关闭按钮
  const renderCloseIcon = () => {
    if (!closable) return null;
    return (
      <html.div
        style={tw('modal-close')}
        onClick={(e) => {
          e.stopPropagation();
          onClose?.();
        }}
      >
        <html.span style={tw('modal-close-x')}>×</html.span>
      </html.div>
    );
  };

  // 渲染内容
  const renderContent = () => {
    if (props.children) {
      return props.children;
    }

    return (
      <html.div style={[tw('modal-content'), contentStyle]}>{content}</html.div>
    );
  };

  // 渲染底部
  const renderFooter = () => {
    if (!footer) return null;
    return <html.div style={tw('modal-footer')}>{footer}</html.div>;
  };

  return (
    <RNModal
      isVisible={visible}
      onBackdropPress={maskClosable ? onClose : undefined}
      style={tw('m-0')} // 移除默认的margin
      animationIn={animationTypes.animationIn as SupportedAnimation}
      animationOut={animationTypes.animationOut as SupportedAnimation}
      backdropTransitionOutTiming={0}
    >
      <html.div
        style={[tw(modalStyles), style]}
        onClick={(e) => e.stopPropagation()}
      >
        <html.div
          style={
            title || closable ? tw('modal-header') : tw('modal-header-empty')
          }
        >
          {renderTitle()}
          {renderCloseIcon()}
        </html.div>

        {renderContent()}
        {renderFooter()}
      </html.div>
    </RNModal>
  );
};

export default Modal;
