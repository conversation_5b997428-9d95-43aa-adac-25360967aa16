import clsx from 'clsx';
import { useMemo } from 'react';
import type { StyleObject } from 'react-strict-dom-tailwind';
import { tw } from 'react-strict-dom-tailwind';

import type { ModalProps } from '.';

export const useModalTailwind = (
  props: Pick<ModalProps, 'size' | 'position'>
) => {
  const { size = 'medium', position = 'center' } = props;

  const modalStyles = useMemo(
    () =>
      clsx({
        'modal-container': true,

        // 尺寸变体
        'modal-small': size === 'small',
        'modal-medium': size === 'medium',
        'modal-large': size === 'large',

        // 位置变体
        'modal-center': position === 'center',
        'modal-bottom': position === 'bottom',
      }),
    [size, position]
  );

  return modalStyles;
};

export const modalTheme: StyleObject = {
  // 蒙层
  'modal-mask': tw('h-screen bg-black opacity-50 z-50'),

  // 容器
  'modal-container': tw('bg-white rounded-[16px] shadow-lg relative p-8 pt-0'),

  // 内容容器基础样式
  'modal-content-container': tw('bg-white rounded-[16px] shadow-lg relative'),

  // 尺寸变体
  'modal-small': tw('w-[280px]'),
  'modal-medium': tw('w-[320px]'),
  'modal-large': tw('w-[360px]'),

  // 位置变体
  'modal-center': tw('m-auto'),
  'modal-bottom': tw('rounded-b-none mt-auto rounded-t-[16px] rounded-b-none'),

  'modal-header': tw('flex justify-between items-center py-4 pt-8'),
  'modal-header-empty': tw('py-4'),

  // 标题
  'modal-title': tw('w-full text-xl font-medium text-center'),

  // 内容
  'modal-content': tw('p-4 text-[14px]'),

  // 底部
  'modal-footer': tw('p-4 flex justify-end gap-2'),

  // 关闭按钮
  'modal-close': tw(
    'w-6 h-6 flex items-center justify-center cursor-pointer z-10'
  ),
  'modal-close-x': tw('text-[24px] leading-none text-gray-500 font-light'),
};

// 将modalTheme导出，以便在theme.ts中汇总
export default modalTheme;
