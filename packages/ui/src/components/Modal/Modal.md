# Modal 模态框组件

## 组件概述

Modal 是一个通用模态框组件，基于 Figma 设计稿实现，用于在当前页面上显示需要用户关注的内容，如确认信息、详细内容展示、操作提示等。组件支持多种尺寸、自定义内容和动画效果，适用于各种弹窗场景。组件使用 React Strict DOM 和 react-strict-dom-tailwind 实现，确保在 Web 和移动平台上具有一致的外观和行为。

## 设计规范

### 尺寸变体

模态框支持以下尺寸变体：

- **小号 (small)**: 宽度为 280px，适用于简单提示或确认操作
- **中号 (medium)**: 宽度为 320px，适用于一般内容展示
- **大号 (large)**: 宽度为 360px，适用于复杂内容展示

### 功能变体

模态框支持以下功能变体：

- **基础模态框**: 提供标题、内容和底部按钮区域
- **无标题模态框**: 仅包含内容和底部按钮区域
- **无底部模态框**: 仅包含标题和内容区域
- **自定义内容模态框**: 完全自定义内容的模态框

### 位置变体

模态框支持以下位置变体：

- **底部弹出 (bottom)**: 模态框从屏幕底部弹出，宽度占满屏幕，顶部圆角，蒙版淡入显示，不从底部弹出

### 状态变体

模态框支持以下状态：

- **显示状态**: 模态框可见
- **隐藏状态**: 模态框不可见
- **加载状态**: 模态框内容加载中

### 颜色规范

- **背景蒙层**:
  - 颜色: #000000
  - 透明度: 50%

- **模态框容器**:
  - 背景色: #FFFFFF
  - 边框: 无
  - 阴影: 0px 4px 12px rgba(0, 0, 0, 0.1)

- **标题区域**:
  - 背景色: #FFFFFF
  - 文字颜色: #000000
  - 分割线: 0.5px #F0F0F0

- **内容区域**:
  - 背景色: #FFFFFF
  - 文字颜色: #000000

- **底部按钮区域**:
  - 背景色: #FFFFFF
  - 分割线: 0.5px #F0F0F0
  - 按钮颜色: 根据按钮组件规范

### 形状规范

- **圆角**: 16px，适用于所有尺寸

### 文字规范

- 标题文字: 16px，Noto Sans TC，Medium (500)
- 内容文字: 14px，Noto Sans TC，Regular (400)

### 间距规范

- 标题内边距: 16px
- 内容内边距: 16px
- 底部按钮区域内边距: 16px
- 按钮之间间距: 8px

## 组件 API

### 属性接口

```typescript
export interface ModalProps {
  /**
   * 是否可见
   * @default false
   */
  visible: boolean;

  /**
   * 关闭事件处理函数
   */
  onClose: () => void;

  /**
   * 模态框标题
   */
  title?: React.ReactNode;

  /**
   * 模态框内容
   */
  content?: React.ReactNode;

  /**
   * 模态框底部内容
   */
  footer?: React.ReactNode;

  /**
   * 模态框尺寸
   * @default 'medium'
   */
  size?: 'small' | 'medium' | 'large';

  /**
   * 是否在点击蒙层时关闭
   * @default true
   */
  maskClosable?: boolean;

  /**
   * 是否显示关闭图标
   * @default true
   */
  closable?: boolean;

  /**
   * 是否垂直居中显示
   * @default true
   */
  centered?: boolean;

  /**
   * 自定义样式
   */
  style?: StyleType;

  /**
   * 自定义蒙层样式
   */
  maskStyle?: StyleType;

  /**
   * 自定义内容区域样式
   */
  contentStyle?: StyleType;

  /**
   * 自定义动画持续时间，单位毫秒
   * @default 300
   */
  animationDuration?: number;

  /**
   * 模态框内容
   */
  children?: React.ReactNode;

  /**
   * 是否在组件卸载时销毁模态框
   * @default false
   */
  destroyOnClose?: boolean;

  /**
   * 模态框打开后的回调
   */
  afterOpen?: () => void;

  /**
   * 模态框关闭后的回调
   */
  afterClose?: () => void;
}
```

## 使用示例

### 使用 useModal Hook (推荐)

```tsx
import { Modal, Button, useModal } from '@dp-frontend/ui';

const MyComponent = () => {
  // 使用useModal钩子管理Modal状态，避免手动维护visible状态
  const { modalProps, open, close } = useModal({
    title: '提示',
    content: '这是一个使用useModal钩子的模态框内容',
    footer: (
      <html.div style={tw('flex justify-end')}>
        <Button onClick={() => close()}>
          确定
        </Button>
      </html.div>
    )
  });

  return (
    <>
      <Button onClick={open}>
        打开模态框
      </Button>

      {/* 使用modalProps传递所有属性 */}
      <Modal {...modalProps} />
    </>
  );
};
```

### 基本用法

```tsx
import { Modal, Button } from '@dp-frontend/ui';
import { useState } from 'react';

// 基本模态框
const [visible, setVisible] = useState(false);

<Button onClick={() => setVisible(true)}>
  打开模态框
</Button>

<Modal
  visible={visible}
  onClose={() => setVisible(false)}
  title="提示"
  content="这是一个基本的模态框内容"
  footer={
    <Button onClick={() => setVisible(false)}>
      确定
    </Button>
  }
/>

// 无标题模态框
<Modal
  visible={visible}
  onClose={() => setVisible(false)}
  content="这是一个无标题的模态框内容"
  footer={
    <Button onClick={() => setVisible(false)}>
      确定
    </Button>
  }
/>

// 无底部模态框
<Modal
  visible={visible}
  onClose={() => setVisible(false)}
  title="提示"
  content="这是一个无底部按钮的模态框内容"
/>

// 不同尺寸的模态框
<Modal
  visible={visible}
  onClose={() => setVisible(false)}
  title="小号模态框"
  content="这是一个小号模态框内容"
  size="small"
  footer={
    <Button onClick={() => setVisible(false)}>
      确定
    </Button>
  }
/>
```

### 自定义内容模态框

#### 使用 useModal Hook

```tsx
import { Modal, Button, Input, useModal } from '@dp-frontend/ui';
import { useState } from 'react';
import { html } from 'react-strict-dom';

const CustomModalWithHook = () => {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const { tw } = useThemeTailwind();

  // 使用useModal钩子管理Modal状态
  const { modalProps, open, close } = useModal({
    title: '用户信息',
  });

  const handleSubmit = () => {
    console.log('提交数据:', { name, email });
    close(); // 使用close方法关闭模态框
  };

  return (
    <>
      <Button onClick={open}>
        打开自定义模态框
      </Button>

      <Modal
        {...modalProps}
        footer={
          <html.div style={tw('flex justify-end gap-2')}>
            <Button
              fill="outline"
              onClick={close}
            >
              取消
            </Button>
            <Button onClick={handleSubmit}>
              提交
            </Button>
          </html.div>
        }
      >
        <html.div style={tw('flex flex-col gap-4 p-4')}>
          <Input
            value={name}
            onChange={(value) => setName(value)}
            placeholder="请输入姓名"
            title="姓名"
          />
          <Input
            value={email}
            onChange={(value) => setEmail(value)}
            placeholder="请输入邮箱"
            title="邮箱"
          />
        </html.div>
      </Modal>
    </>
  );
};
```

#### 传统方式

```tsx
import { Modal, Button, Input } from '@dp-frontend/ui';
import { useState } from 'react';

const CustomModal = () => {
  const [visible, setVisible] = useState(false);
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');

  const handleSubmit = () => {
    console.log('提交数据:', { name, email });
    setVisible(false);
  };

  return (
    <>
      <Button onClick={() => setVisible(true)}>
        打开自定义模态框
      </Button>

      <Modal
        visible={visible}
        onClose={() => setVisible(false)}
        title="用户信息"
        footer={
          <html.div style={tw('flex justify-end gap-2')}>
            <Button
              fill="outline"
              onClick={() => setVisible(false)}
            >
              取消
            </Button>
            <Button onClick={handleSubmit}>
              提交
            </Button>
          </html.div>
        }
      >
        <html.div style={tw('flex flex-col gap-4 p-4')}>
          <Input
            value={name}
            onChange={(value) => setName(value)}
            placeholder="请输入姓名"
            title="姓名"
          />
          <Input
            value={email}
            onChange={(value) => setEmail(value)}
            placeholder="请输入邮箱"
            title="邮箱"
          />
        </html.div>
      </Modal>
    </>
  );
};
```

### 确认对话框

```tsx
import { Modal, Button } from '@dp-frontend/ui';

// 封装确认对话框
const showConfirm = ({
  title = '确认',
  content,
  onOk,
  onCancel,
}) => {
  let modalVisible = true;

  const handleClose = () => {
    modalVisible = false;
    // 触发模态框重新渲染
    rerender();
  };

  const handleOk = () => {
    handleClose();
    if (onOk) onOk();
  };

  const handleCancel = () => {
    handleClose();
    if (onCancel) onCancel();
  };

  // 渲染模态框
  const renderModal = () => (
    <Modal
      visible={modalVisible}
      onClose={handleCancel}
      title={title}
      content={content}
      footer={
        <html.div style={tw('flex justify-end gap-2')}>
          <Button
            fill="outline"
            onClick={handleCancel}
          >
            取消
          </Button>
          <Button onClick={handleOk}>
            确定
          </Button>
        </html.div>
      }
    />
  );

  // 实际应用中需要使用动态渲染机制
  const rerender = () => {
    // 重新渲染模态框
  };

  return renderModal();
};

// 使用示例
<Button
  onClick={() => {
    showConfirm({
      title: '删除确认',
      content: '确定要删除这条记录吗？此操作不可撤销。',
      onOk: () => {
        console.log('执行删除操作');
        deleteRecord(id);
      },
      onCancel: () => {
        console.log('取消删除操作');
      }
    });
  }}
>
  删除
</Button>
```

## 实现注意事项

1. 使用 React Strict DOM 的 `html.*` 元素实现模态框
2. 使用 `useThemeTailwind` 钩子提供的 `tw()` 函数应用 Tailwind 样式
3. 使用 Portal 将模态框渲染到 document.body，避免被父元素样式影响
4. 实现模态框显示/隐藏的动画效果
5. 处理模态框打开时的焦点管理和键盘事件
6. 实现点击蒙层关闭功能
7. 支持自定义样式覆盖默认样式
8. 确保模态框在不同平台上具有一致的外观和行为
9. 处理模态框内容溢出时的滚动行为
10. 支持嵌套模态框的正确层叠顺序
11. 提供良好的无障碍支持，包括键盘操作和屏幕阅读器支持
12. 推荐使用 `useModal` 钩子管理模态框状态，避免手动维护 visible 状态

## useModal Hook

为了简化模态框的使用，我们提供了 `useModal` 钩子，它可以：

1. 自动管理模态框的可见状态
2. 提供 open、close 和 toggle 方法控制模态框
3. 允许预设模态框属性
4. 返回包含所有模态框属性的 modalProps 对象

### API

```typescript
function useModal(defaultProps?: Omit<ModalProps, 'visible' | 'onClose'>): {
  visible: boolean;
  open: () => void;
  close: () => void;
  toggle: () => void;
  modalProps: ModalProps;
}
```

### 参数

- `defaultProps`: 可选，模态框的默认属性，不包括 `visible` 和 `onClose`

### 返回值

- `visible`: 模态框的可见状态
- `open`: 打开模态框的方法
- `close`: 关闭模态框的方法
- `toggle`: 切换模态框可见状态的方法
- `modalProps`: 包含所有模态框属性的对象，可直接传递给 Modal 组件
