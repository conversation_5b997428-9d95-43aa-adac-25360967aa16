# Input 输入框组件

## 组件概述

Input 是一个通用输入框组件，基于 Figma 设计稿实现，支持多种状态和类型，适用于各种表单输入场景。组件使用 React Strict DOM 和 react-strict-dom-tailwind 实现，确保在 Web 和移动平台上具有一致的外观和行为。

## 设计规范

### 尺寸变体

输入框提供一种标准尺寸：

- **标准尺寸**: 高度为 50px，适用于所有输入场景

### 功能变体

输入框支持多种功能变体：

- **基础输入框**: 仅提供基本输入功能
- **可清除输入框**: 提供一键清除输入内容的功能
- **带额外内容的输入框**: 在输入框右侧添加额外内容，如验证码按钮
- **密码输入框**: 支持密码显示/隐藏切换

### 状态变体

输入框支持四种状态：

- **默认状态**: 输入框的初始状态，显示占位文本
- **错误状态**: 输入内容不符合要求的状态，显示错误提示
- **只读状态**: 输入框内容不可编辑，但可以选择和复制
- **禁用状态**: 输入框完全不可交互

### 颜色规范

- **默认状态**:
  - 背景色: #F1F1F1
  - 文字颜色: #000000 (50% 透明度用于占位文本)
  - 边框: 无

- **聚焦状态**:
  - 背景色: #F1F1F1
  - 文字颜色: #000000
  - 边框: 无
  - 清除按钮: 灰色圆形背景 (#D9D9D9)，黑色图标

- **错误状态**:
  - 背景色: #FFFBFB
  - 文字颜色: #000000
  - 边框: 0.2px #E72323
  - 错误提示文字颜色: #E72323

- **只读状态**:
  - 背景色: #F1F1F1
  - 文字颜色: #000000
  - 边框: 无
  - 不显示清除按钮

- **禁用状态**:
  - 背景色: #F1F1F1
  - 文字颜色: #000000 (50% 透明度)
  - 边框: 无
  - 不显示清除按钮

- **标题文本**:
  - 文字颜色: #1D1D1D (80% 透明度)

### 形状规范

- 圆角: 30px (pill 形状)

### 文字规范

- 输入文本: 14px，Noto Sans TC，Regular (400)
- 占位文本: 14px，Noto Sans TC，Regular (400)，50% 透明度
- 标题文本: 14px，Noto Sans TC，Regular (400)，80% 透明度
- 错误提示: 12px，Noto Sans TC，Regular (400)

### 内边距规范

- 输入框: 16px 20px (垂直 水平)
- 有清除按钮的输入框: 16px 16px 16px 20px (上 右 下 左)

### 对齐方式

输入框支持不同的文本对齐方式：

- **左对齐 (left)**: 默认对齐方式，适用于大多数输入场景
- **居中对齐 (center)**: 适用于特定场景，如验证码输入
- **右对齐 (right)**: 适用于数字输入，如金额、验证码等

## 组件 API

### 属性接口

```typescript
// 输入框文本对齐方式
export type InputTextAlign = 'left' | 'center' | 'right';

// 输入框属性
export interface InputProps extends ComponentProps<typeof html.input> {
  /**
   * 输入框值（受控模式）
   */
  value?: string;

  /**
   * 默认输入框值（非受控模式）
   */
  defaultValue?: string;

  /**
   * 输入框值变化事件处理函数
   */
  onChange?: (value: string) => void;

  /**
   * 输入框占位文本
   * @default '請輸入'
   */
  placeholder?: string;

  /**
   * 错误提示文本
   */
  error?: string;

  /**
   * 是否禁用输入框
   * @default false
   */
  disabled?: boolean;

  /**
   * 是否只读
   * @default false
   */
  readOnly?: boolean;

  /**
   * 是否自动获取焦点
   * @default false
   */
  autoFocus?: boolean;

  /**
   * 输入框类型
   * @default 'text'
   */
  type?: 'text' | 'password' | 'number' | 'email' | 'tel';

  /**
   * 最大输入长度
   */
  maxLength?: number;

  /**
   * 最小输入长度
   */
  minLength?: number;

  /**
   * 是否显示清除按钮
   * @default false
   */
  clearable?: boolean;

  /**
   * 文本对齐方式
   * @default 'left'
   */
  textAlign?: InputTextAlign;

  /**
   * 自定义输入框容器样式（不带标签时应用于整个输入框）
   */
  style?: StyleType;

  /**
   * 自定义输入框内部样式（应用于input元素）
   */
  inputStyle?: StyleType;

  /**
   * 自定义最外层容器样式
   */
  containerStyle?: StyleType;

  /**
   * 清除按钮点击事件处理函数
   */
  onClear?: () => void;

  /**
   * 输入框获取焦点事件处理函数
   */
  onFocus?: () => void;

  /**
   * 输入框失去焦点事件处理函数
   */
  onBlur?: () => void;

  /**
   * 按下回车键的回调
   */
  onEnterPress?: (e: { key: string }) => void;

  /**
   * 如果为 true，那么只有输入框聚焦时才会显示清除按钮
   * 如果为 false，那么输入框失去焦点后依旧会显示清除按钮
   * @default true
   */
  onlyShowClearWhenFocus?: boolean;

  /**
   * 输入框右侧内容
   */
  extra?: React.ReactNode;
}
```

## 使用示例

### 基本用法

```tsx
import { Input } from '@dp-frontend/ui';
import { useState } from 'react';

// 基本输入框
const [value, setValue] = useState('');

<Input
  value={value}
  onChange={(newValue) => setValue(newValue)}
  placeholder="請輸入"
/>

// 带标题的输入框
<Input
  value={value}
  onChange={(newValue) => setValue(newValue)}
  placeholder="請輸入"
  title="用户名"
/>

// 带清除按钮的输入框
<Input
  value={value}
  onChange={(newValue) => setValue(newValue)}
  placeholder="請輸入"
  clearable
/>

// 错误状态输入框
<Input
  value={value}
  onChange={(newValue) => setValue(newValue)}
  error="格式不正确"
/>

// 带错误提示的输入框
<Input
  value={value}
  onChange={(newValue) => setValue(newValue)}
  error="格式不正确"
/>

// 不同类型的输入框
<Input
  type="password"
  value={password}
  onChange={(newValue) => setPassword(newValue)}
  placeholder="請輸入密碼"
/>

<Input
  type="email"
  value={email}
  onChange={(newValue) => setEmail(newValue)}
  placeholder="請輸入郵箱"
/>

<Input
  type="tel"
  value={phone}
  onChange={(newValue) => setPhone(newValue)}
  placeholder="請輸入手機號"
/>

// 自动获取焦点的输入框
<Input
  value={value}
  onChange={(newValue) => setValue(newValue)}
  autoFocus={true}
/>

// 带最大长度限制的输入框
<Input
  value={value}
  onChange={(newValue) => setValue(newValue)}
  maxLength={10}
/>

// 禁用的输入框
<Input
  value={value}
  onChange={(newValue) => setValue(newValue)}
  disabled={true}
/>

// 只读状态的输入框
<Input
  value="只读模式的输入框"
  onChange={(newValue) => setValue(newValue)}
  readOnly={true}
/>

// 文本右对齐的输入框
<Input
  value={value}
  onChange={(newValue) => setValue(newValue)}
  placeholder="請輸入验证码"
  textAlign="right"
/>

// 带右侧附加内容的输入框
<Input
  value={value}
  onChange={(newValue) => setValue(newValue)}
  placeholder="請輸入验证码"
  extra={<html.button style={tw('text-primary')}>发送验证码</html.button>}
/>

// 非受控模式的输入框
<Input
  defaultValue="初始值"
  onChange={(newValue) => console.log(newValue)}
  placeholder="請輸入"
/>
```

### 密码输入框示例

```tsx
import { Input } from '@dp-frontend/ui';
import { useState } from 'react';
import { EyeIcon, EyeSlashIcon } from '@dp-frontend/ui/icons';

const PasswordInput = () => {
  const [password, setPassword] = useState('');
  const [visible, setVisible] = useState(false);

  return (
    <Input
      type={visible ? 'text' : 'password'}
      value={password}
      onChange={(newValue) => setPassword(newValue)}
      placeholder="請輸入密碼"
      clearable
      extra={
        <html.div onClick={() => setVisible(!visible)} style={tw('cursor-pointer')}>
          {visible ? <EyeIcon /> : <EyeSlashIcon />}
        </html.div>
      }
    />
  );
};
```

### 配合表单使用

```tsx
import { Form, Input } from '@dp-frontend/ui';

const LoginForm = () => {
  return (
    <Form onFinish={(values) => console.log(values)}>
      <Form.Item name="username" label="用户名">
        <Input placeholder="请输入用户名" clearable />
      </Form.Item>
      <Form.Item name="password" label="密码">
        <Input placeholder="请输入密码" type="password" clearable />
      </Form.Item>
      <Form.Item name="code" label="验证码">
        <Input
          placeholder="请输入验证码"
          clearable
          textAlign="right"
          extra={<html.button style={tw('text-primary')}>发送验证码</html.button>}
        />
      </Form.Item>
    </Form>
  );
};
```

## 组件 Ref

Input 组件支持通过 ref 访问以下方法：

```typescript
export interface InputRef {
  /**
   * 让输入框获取焦点
   */
  focus: () => void;

  /**
   * 让输入框失去焦点
   */
  blur: () => void;

  /**
   * 清空输入内容
   */
  clear: () => void;

  /**
   * 原始 input 元素
   */
  nativeElement: HTMLInputElement | null;
}
```

使用示例：

```tsx
import { Input } from '@dp-frontend/ui';
import { useRef } from 'react';

const InputWithRef = () => {
  const inputRef = useRef<InputRef>(null);

  const handleFocus = () => {
    inputRef.current?.focus();
  };

  const handleBlur = () => {
    inputRef.current?.blur();
  };

  const handleClear = () => {
    inputRef.current?.clear();
  };

  return (
    <>
      <Input
        ref={inputRef}
        value={value}
        onChange={(newValue) => setValue(newValue)}
        placeholder="請輸入"
      />
      <html.div style={tw('flex gap-2 mt-2')}>
        <html.button onClick={handleFocus} style={tw('btn-small')}>获取焦点</html.button>
        <html.button onClick={handleBlur} style={tw('btn-small')}>失去焦点</html.button>
        <html.button onClick={handleClear} style={tw('btn-small')}>清空内容</html.button>
      </html.div>
    </>
  );
};
```


## 实现注意事项

1. 使用 React Strict DOM 的 `html.input` 元素实现输入框
2. 使用 `useThemeTailwind` 钩子提供的 `tw()` 函数应用 Tailwind 样式
3. 使用自定义钩子根据输入框的状态动态生成样式类
4. 支持自定义样式覆盖默认样式
5. 实现清除按钮功能，点击时清空输入内容
6. 确保输入框在禁用状态下不可编辑，在只读状态下可选择但不可编辑
7. 根据 `status` 属性显示不同的样式和错误提示
8. 实现输入内容超出时的横向滚动功能
9. 确保输入框在不同平台上具有一致的外观和行为
10. 根据 `type` 属性设置适当的键盘类型（在移动平台）
11. 支持通过 ref 访问输入框的方法
12. 支持通过 CSS 变量自定义样式

## 内部实现

Input 组件内部使用了以下技术实现：

1. **状态管理**：使用 React 的 `useState` 钩子管理输入框的焦点状态和内部值
2. **样式应用**：使用自定义钩子根据输入框属性生成样式类
3. **事件处理**：实现了 `onChange`、`onFocus`、`onBlur`、`onEnterPress` 等事件处理函数
4. **清除按钮**：当输入框有内容且 `clearable` 为 true 时显示清除按钮，点击时清空输入内容
5. **错误提示**：当 `error` 属性存在时显示错误提示文本
6. **Ref 转发**：使用 `forwardRef` 和 `useImperativeHandle` 实现对外暴露的方法

```tsx
// 样式生成示例
const inputStyles = useInputTailwind({
  disabled,
  readOnly,
  isFocused: focusRef.current,
  textAlign,
  hasError: !!error,
  hasExtra: !!extra,
  hasClearButton: clearable && !!value,
});

// 清除按钮点击处理示例
const handleClear = () => {
  setValue('');
  if (onClear) {
    onClear();
  }
};

// Ref 实现示例
const Input = forwardRef<InputRef, InputProps>((props, ref) => {
  const inputRef = useRef<HTMLInputElement>(null);
  const focusRef = useRef(false);

  useImperativeHandle(ref, () => ({
    focus: () => {
      inputRef.current?.focus();
    },
    blur: () => {
      inputRef.current?.blur();
    },
    clear: () => {
      setValue('');
      if (onClear) {
        onClear();
      }
    },
    get nativeElement() {
      return inputRef.current;
    },
  }));

  // 组件实现...
});

// 受控与非受控状态处理
const [value, setValue] = useControlledState(
  controlledValue,
  defaultValue,
  onChange
);

// 密码可见性切换
const [visible, setVisible] = useState(false);
const inputType = type === 'password' && visible ? 'text' : type;
```

## 常见问题

### 在 iOS 下，为什么设置了 autoFocus 还是不能自动获取焦点？

这是预期行为，因为 iOS 系统对聚焦有限制。在 iOS 中，只有用户交互才能触发输入框的聚焦，这是为了防止键盘在用户不期望的情况下弹出。

### 当 type 为 number 时，maxLength 限制为什么没有生效？

在原生 input 中，maxlength 只在 type 为 text, search, url, tel, email, password 时生效。如果需要对 number 类型的输入框进行限制，可以通过 max 和 min 属性，或者在 onChange 事件中进行处理。

### 如何实现带单位的输入框？

可以使用 `extra` 属性添加单位：

```tsx
<Input
  value={value}
  onChange={(newValue) => setValue(newValue)}
  placeholder="请输入金额"
  extra={<html.div style={tw('text-gray-500')}>元</html.div>}
/>
```
