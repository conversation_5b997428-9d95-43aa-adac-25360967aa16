import clsx from 'clsx';
import { useMemo } from 'react';
import { css } from 'react-strict-dom';
import type { StyleObject } from 'react-strict-dom-tailwind';
import { tw } from 'react-strict-dom-tailwind';

import { colors } from '../../theme/vars.stylex';

/**
 * Input组件的样式定义
 */
export const inputTheme: StyleObject = {
  // 基础样式
  'input-base': tw(
    'box-border w-full rounded-[30px] text-[14px] h-[50px] min-w-[200px]'
  ),

  // 容器样式
  'input-container': tw('flex flex-col'),

  // 标题样式
  'input-title': tw('text-[14px] mb-1 opacity-80'),

  // 输入包装器
  'input-wrapper': tw(
    'box-border flex flex-row items-center px-5 py-4 rounded-[30px] bg-[#F1F1F1] h-[50px]'
  ),

  // 错误提示样式
  'input-error-text': tw('text-[12px] mt-1 ml-5 text-error', {
    extraStyles: css.create({ 'text-error': { color: colors.colorRed500 } }),
  }),

  // 对齐方式
  'input-align-left': tw('text-left'),
  'input-align-center': tw('text-center'),
  'input-align-right': tw('text-right'),

  // 清除按钮样式
  'input-clear-button': tw(
    'cursor-pointer ml-[-16px] w-[16px] h-[16px] min-w-[16px] min-h-[16px] max-w-[16px] max-h-[16px] flex items-center relative justify-center rounded-full bg-gray-300'
  ),
  'input-clear-icon': tw(
    'cursor-pointer text-[14px] text-white relative top-[-1px]'
  ),

  // 右侧内容样式
  'input-extra': tw('flex items-center h-full ml-[4px]'),

  // 输入框内部样式
  'input-inner': tw(
    'min-h-[50px] w-full bg-transparent border-none outline-none'
  ),
  'input-inner-closable': tw('pr-[24px]'),

  // 状态样式
  ...css.create({
    'input-default': {
      backgroundColor: colors.colorGray950, // #F1F1F1
      color: colors.colorBlack,
    },

    'input-error': {
      backgroundColor: '#FFFBFB',
      color: colors.colorBlack,
      borderWidth: 0.2,
      borderStyle: 'solid',
      borderColor: colors.colorRed500,
    },

    'input-readonly': {
      backgroundColor: colors.colorGray950, // #F1F1F1
      color: colors.colorBlack,
    },

    'input-disabled': {
      backgroundColor: colors.colorGray950, // #F1F1F1
      color: colors.colorBlack,
      opacity: 0.5,
    },

    // 占位符样式
    'input-placeholder': {
      color: colors.colorBlack,
      opacity: 0.5,
    },
  }),
};

/**
 * 根据输入框状态生成样式
 */
export const useInputTailwind = ({
  disabled = false,
  readOnly = false,
  isFocused = false,
  textAlign = 'left',
  hasError = false,
  hasExtra = false,
  hasClearButton = false,
}: {
  disabled?: boolean;
  readOnly?: boolean;
  isFocused?: boolean;
  textAlign?: 'left' | 'center' | 'right';
  hasError?: boolean;
  hasExtra?: boolean;
  hasClearButton?: boolean;
}) => {
  // 使用useMemo缓存样式计算结果
  const inputStyles = useMemo(
    () =>
      clsx({
        // 基础样式
        'input-wrapper': true,

        // 状态样式
        'input-default': !disabled && !readOnly && !hasError,
        'input-error': !disabled && !readOnly && hasError,
        'input-readonly': readOnly,
        'input-disabled': disabled,

        // 对齐方式
        'input-align-left': textAlign === 'left',
        'input-align-center': textAlign === 'center',
        'input-align-right': textAlign === 'right',
      }),
    [disabled, readOnly, textAlign, hasExtra, hasClearButton, isFocused]
  );

  return inputStyles;
};
