import type { ComponentProps } from 'react';
import React, { forwardRef, useImperativeHandle, useRef } from 'react';
import { html } from 'react-strict-dom';

import { useControlledState, useThemeTailwind } from '../../hooks';
import { useInputTailwind } from './styles';

// 输入框文本对齐方式
export type InputTextAlign = 'left' | 'center' | 'right';

// 使用已有的StyleType类型
import type { StyleType } from '../Button';

// 输入框属性
export interface InputProps extends ComponentProps<typeof html.input> {
  /**
   * 输入框值（受控模式）
   */
  value?: string;

  /**
   * 默认输入框值（非受控模式）
   */
  defaultValue?: string;

  /**
   * 输入框值变化事件处理函数
   */
  onChange?: (value: string) => void;

  /**
   * 输入框占位文本
   * @default '請輸入'
   */
  placeholder?: string;

  /**
   * 错误提示文本
   */
  error?: string;

  /**
   * 是否禁用输入框
   * @default false
   */
  disabled?: boolean;

  /**
   * 是否只读
   * @default false
   */
  readOnly?: boolean;

  /**
   * 是否自动获取焦点
   * @default false
   */
  autoFocus?: boolean;

  /**
   * 输入框类型
   * @default 'text'
   */
  type?: 'text' | 'password' | 'number' | 'email' | 'tel';

  /**
   * 最大输入长度
   */
  maxLength?: number;

  /**
   * 最小输入长度
   */
  minLength?: number;

  /**
   * 是否显示清除按钮
   * @default false
   */
  clearable?: boolean;

  /**
   * 文本对齐方式
   * @default 'left'
   */
  textAlign?: InputTextAlign;

  /**
   * 自定义输入框容器样式（不带标签时应用于整个输入框）
   */
  style?: StyleType;

  /**
   * 自定义输入框内部样式（应用于input元素）
   */
  inputStyle?: StyleType;

  /**
   * 自定义最外层容器样式
   */
  containerStyle?: StyleType;

  /**
   * 清除按钮点击事件处理函数
   */
  onClear?: () => void;

  /**
   * 输入框获取焦点事件处理函数
   */
  onFocus?: () => void;

  /**
   * 输入框失去焦点事件处理函数
   */
  onBlur?: () => void;

  /**
   * 按下回车键的回调
   */
  onEnterPress?: (e: { key: string }) => void;

  /**
   * 如果为 true，那么只有输入框聚焦时才会显示清除按钮
   * 如果为 false，那么输入框失去焦点后依旧会显示清除按钮
   * @default true
   */
  onlyShowClearWhenFocus?: boolean;

  /**
   * 输入框右侧内容
   */
  extra?: React.ReactNode;
}

// 输入框 Ref 接口
export interface InputRef {
  /**
   * 让输入框获取焦点
   */
  focus: () => void;

  /**
   * 让输入框失去焦点
   */
  blur: () => void;

  /**
   * 清空输入内容
   */
  clear: () => void;

  /**
   * 原始 input 元素
   */
  nativeElement: HTMLInputElement | null;
}

/**
 * Input 输入框组件
 */
export const Input = forwardRef<InputRef, InputProps>((props, ref) => {
  const {
    value: controlledValue,
    defaultValue = '',
    onChange,
    placeholder = '請輸入',
    error,
    disabled = false,
    readOnly = false,
    autoFocus = false,
    type = 'text',
    maxLength,
    minLength,
    clearable = false,
    textAlign = 'left',
    style,
    inputStyle,
    containerStyle,
    onClear,
    onFocus,
    onBlur,
    onEnterPress,
    onlyShowClearWhenFocus = true,
    extra,
  } = props;

  // 使用useControlledState处理受控和非受控状态
  const [value, setValue] = useControlledState(
    controlledValue,
    defaultValue,
    onChange
  );

  // 引用原生输入框元素
  const inputRef = useRef<HTMLInputElement>(null);

  const focusRef = useRef(false);

  // 使用主题样式
  const { tw } = useThemeTailwind();

  // 生成输入框样式
  const inputStyles = useInputTailwind({
    disabled,
    readOnly,
    isFocused: focusRef.current,
    textAlign,
    hasError: !!error,
    hasExtra: !!extra,
    hasClearButton: clearable && !!value,
  });

  // 暴露方法给 ref
  useImperativeHandle(ref, () => ({
    focus: () => {
      inputRef.current?.focus();
    },
    blur: () => {
      inputRef.current?.blur();
    },
    clear: () => {
      setValue('');
      if (onClear) {
        onClear();
      }
    },
    get nativeElement() {
      return inputRef.current;
    },
  }));

  const setFocus = () => {
    // 如果输入框不是禁用或只读状态，则获取焦点
    if (disabled || readOnly) return;
    inputRef.current?.focus();
    handleFocus();
  };

  // 处理输入变化
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setValue(e.target.value);
  };

  // 处理获取焦点
  const handleFocus = () => {
    focusRef.current = true;
    onFocus?.();
  };

  // 处理失去焦点
  const handleBlur = () => {
    focusRef.current = false;
    onBlur?.();
  };

  // 处理按键
  const handleKeyDown = (event: { key: string }) => {
    if (event.key === 'Enter' && onEnterPress) {
      onEnterPress(event);
    }
  };

  // 处理清除按钮点击
  const handleClear = () => {
    setValue('');
    setFocus();
    onClear?.();
  };

  // 是否显示清除按钮
  const showClearButton =
    clearable &&
    !!value &&
    (!onlyShowClearWhenFocus || focusRef.current) &&
    !disabled &&
    !readOnly;

  // 渲染清除按钮
  const renderClearButton = () => {
    if (!showClearButton) return null;

    return (
      <html.div
        style={tw('input-clear-button')}
        onClick={handleClear}
        onMouseDown={() => setFocus()}
        onTouchStart={() => setFocus()}
      >
        <html.div style={tw('input-clear-icon')}>×</html.div>
      </html.div>
    );
  };

  // 渲染右侧内容
  const renderExtra = () => {
    if (!extra) return null;
    return <html.div style={tw('input-extra')}>{extra}</html.div>;
  };

  // 渲染错误提示
  const renderErrorText = () => {
    if (!error) return null;
    return <html.div style={tw('input-error-text')}>{error}</html.div>;
  };

  return (
    <html.div style={[tw('input-container'), containerStyle]}>
      {/* 输入框包装器 */}
      <html.div style={[tw(inputStyles), style]} onClick={setFocus}>
        {/* 输入框 */}
        <html.input
          ref={inputRef}
          type={type}
          value={value}
          placeholder={placeholder}
          disabled={disabled}
          readOnly={readOnly || disabled}
          autoFocus={autoFocus}
          maxLength={maxLength}
          minLength={minLength}
          onChange={handleChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onKeyDown={handleKeyDown}
          style={[
            tw(`input-inner input-align-${textAlign}`),
            clearable && tw('input-inner-closable'),
            inputStyle,
          ]}
        />

        {/* 清除按钮 */}
        {renderClearButton()}

        {/* 右侧内容 */}
        {renderExtra()}
      </html.div>

      {/* 错误提示 */}
      {renderErrorText()}
    </html.div>
  );
});

// 设置组件名称
Input.displayName = 'Input';
