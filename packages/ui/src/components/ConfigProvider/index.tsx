import type { PropsWithChildren } from 'react';
import React from 'react';

import { ToastContainer } from '../Toast';

export interface ConfigProviderProps {
  theme?: Record<string, unknown>;
  /**
   * 是否禁用内置的Toast容器
   * @default false
   */
  disableToast?: boolean;
}

const Context = React.createContext<ConfigProviderProps>({
  theme: {},
  disableToast: false,
});

export const useConfigProvider = (): ConfigProviderProps => {
  return React.useContext(Context);
};

export const ConfigProvider = ({
  theme,
  disableToast = false,
  children,
}: PropsWithChildren<ConfigProviderProps>) => {
  return (
    <Context.Provider value={{ theme, disableToast }}>
      {children}
      {!disableToast && <ToastContainer />}
    </Context.Provider>
  );
};
