import React, { forwardRef, useCallback, useImperativeHandle } from 'react';
import { html } from 'react-strict-dom';
import type { StyleObject } from 'react-strict-dom-tailwind';

import { useThemeTailwind } from '../../hooks';
import { useControlledState } from '../../hooks/useControlledState';
import { useRadioStyles } from './styles';

// 单选框尺寸
export type RadioSize = 'normal' | 'small';

// 单选框颜色
export type RadioColor =
  | 'default'
  | 'primary'
  | 'success'
  | 'danger'
  | 'warning';

// 单选框属性
export interface RadioProps {
  /**
   * 单选框的值
   */
  value?: string;

  /**
   * 是否选中
   * @default false
   */
  checked?: boolean;

  /**
   * 默认是否选中（非受控模式）
   * @default false
   */
  defaultChecked?: boolean;

  /**
   * 单选框尺寸
   * @default 'normal'
   */
  size?: RadioSize;

  /**
   * 单选框颜色
   * @default 'default'
   */
  color?: RadioColor;

  /**
   * 是否禁用
   * @default false
   */
  disabled?: boolean;

  /**
   * 单选框文本内容
   */
  children?: React.ReactNode;

  /**
   * 自定义样式
   */
  style?: StyleObject;

  /**
   * 选中状态变化回调函数
   */
  onChange?: (checked: boolean, value?: string) => void;
}

// 单选框引用接口
export interface RadioRef {
  /**
   * 设置选中状态
   */
  setChecked: (checked: boolean) => void;
}

/**
 * Radio 单选框组件
 *
 * 基于Figma设计稿实现，支持多种尺寸、类型和状态
 */
const _Radio = forwardRef<RadioRef, RadioProps>((props, ref) => {
  const {
    checked: controlledChecked,
    defaultChecked = false,
    size = 'normal',
    color = 'default',
    disabled = false,
    children,
    style,
    onChange,
    value,
  } = props;

  // 使用useControlledState处理受控和非受控状态
  const [checked, setChecked] = useControlledState(
    controlledChecked,
    defaultChecked,
    (newChecked) => {
      onChange?.(newChecked, value);
    }
  );

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    setChecked: (newChecked: boolean) => {
      setChecked(newChecked);
    },
  }));

  // 使用主题样式
  const { tw } = useThemeTailwind();

  // 获取样式
  const {
    container: radioStyles,
    box: radioBoxStyle,
    label: radioLabelStyle,
    dot: radioDotStyle,
  } = useRadioStyles({
    size,
    color,
    disabled,
    checked,
  });

  // 处理点击事件
  const handleClick = useCallback(() => {
    if (!disabled && !checked) {
      setChecked(true);
    }
  }, [disabled, checked, setChecked]);

  // 渲染内部圆点
  const renderRadioDot = () => {
    if (!checked) return null;
    return <html.div style={tw(radioDotStyle)} />;
  };

  // 渲染单选框
  return (
    <html.div
      style={[tw(radioStyles), style]}
      onClick={handleClick}
      role="radio"
      aria-checked={checked}
      aria-disabled={disabled}
    >
      <html.div style={tw(radioBoxStyle)}>{renderRadioDot()}</html.div>
      {children && <html.div style={tw(radioLabelStyle)}>{children}</html.div>}
    </html.div>
  );
});

// 设置组件名称
_Radio.displayName = 'Radio';

// 单选框组属性
export interface RadioGroupProps {
  /**
   * 当前选中的选项值
   */
  value?: string;

  /**
   * 默认选中的选项值（非受控模式）
   */
  defaultValue?: string;

  /**
   * 单选框组尺寸，会被子单选框继承
   * @default 'normal'
   */
  size?: RadioSize;

  /**
   * 单选框组颜色，会被子单选框继承
   * @default 'default'
   */
  color?: RadioColor;

  /**
   * 是否禁用，会被子单选框继承
   * @default false
   */
  disabled?: boolean;

  /**
   * 自定义样式
   */
  style?: StyleObject;

  /**
   * 选项变化回调函数
   */
  onChange?: (value: string) => void;

  /**
   * 单选框组子元素
   */
  children?: React.ReactNode;

  /**
   * 可选项配置，优先级高于 children
   */
  options?: Array<{
    label: React.ReactNode;
    value: string;
    disabled?: boolean;
  }>;
}

/**
 * RadioGroup 单选框组组件
 */
const RadioGroup: React.FC<RadioGroupProps> = (props) => {
  const {
    value: controlledValue,
    defaultValue = '',
    size = 'normal',
    color = 'default',
    disabled = false,
    style,
    onChange,
    children,
    options,
  } = props;

  // 使用useControlledState处理受控和非受控状态
  const [value, setValue] = useControlledState(
    controlledValue,
    defaultValue,
    onChange
  );

  // 使用主题样式
  const { tw } = useThemeTailwind();

  // 处理单选框选中状态变化
  const handleRadioChange = (checked: boolean, radioValue?: string) => {
    if (checked && radioValue) {
      setValue(radioValue);
    }
  };

  // 渲染选项
  const renderOptions = () => {
    if (options) {
      return options.map((option) => (
        <_Radio
          key={option.value}
          value={option.value}
          checked={value === option.value}
          disabled={disabled || option.disabled}
          size={size}
          color={color}
          onChange={handleRadioChange}
        >
          {option.label}
        </_Radio>
      ));
    }

    // 处理子元素
    return React.Children.map(children, (child) => {
      if (!React.isValidElement(child)) return child;

      // 确保子元素是Radio组件
      const childType = child.type as { displayName?: string };
      if (childType.displayName !== 'Radio') return child;

      const childValue = child.props.value;
      if (!childValue) return child;

      // 克隆子元素并注入属性
      return React.cloneElement(child, {
        checked: value === childValue,
        disabled: disabled || child.props.disabled,
        size: child.props.size || size,
        color: child.props.color || color,
        onChange: handleRadioChange,
      } as React.ComponentProps<typeof _Radio>);
    });
  };

  // 渲染单选框组
  return (
    <html.div style={[tw('flex flex-col gap-3'), style]}>
      {renderOptions()}
    </html.div>
  );
};

// 设置组件名称
RadioGroup.displayName = 'RadioGroup';

export const Radio = Object.assign(_Radio, { Group: RadioGroup });
