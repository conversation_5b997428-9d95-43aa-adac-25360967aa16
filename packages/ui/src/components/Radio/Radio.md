# Radio 单选框组件

## 组件概述

Radio 是一个用于单选场景的单选框组件，基于 Figma 设计稿实现，支持多种尺寸、类型和状态，适用于各种表单和列表选择场景。组件使用 React Strict DOM 和 react-strict-dom-tailwind 实现，确保在 Web 和移动平台上具有一致的外观和行为。

## 设计规范

### 尺寸变体

单选框提供两种尺寸变体：

- **标准 (normal)**: 默认尺寸，适用于大多数场景
- **小号 (small)**: 小尺寸，适用于空间受限的场景或需要更紧凑布局的场合

### 颜色变体

单选框提供多种颜色变体：

- **默认 (default)**: 使用主题默认颜色，通常为深灰色
- **主要 (primary)**: 使用主题主色，用于强调重要性
- **成功 (success)**: 绿色，用于表示成功或确认操作
- **危险 (danger)**: 红色，用于表示危险或警告操作
- **警告 (warning)**: 橙色，用于表示警告操作

### 状态变体

单选框支持以下状态：

- **未选中 (unchecked)**: 单选框的默认未选中状态
- **选中 (checked)**: 单选框被选中的状态
- **禁用 (disabled)**: 单选框不可用的状态，可以与选中或未选中状态组合

### 颜色规范

- **未选中状态**:
  - 边框颜色: #D5D5D5 (colorGray880)
  - 背景色: 透明
  - 文字颜色: #24231D (colorGray100)

- **选中状态**:
  - 默认: 边框颜色 #24231D (colorGray100)，内部圆点颜色 #24231D
  - 主要: 边框颜色 #24231D (colorGray100)，内部圆点颜色 #24231D
  - 成功: 边框颜色 #34C759 (colorGreen500)，内部圆点颜色 #34C759
  - 危险: 边框颜色 #FF3B30 (colorRed500)，内部圆点颜色 #FF3B30
  - 警告: 边框颜色 #FF9735 (colorOrange500)，内部圆点颜色 #FF9735

- **禁用状态**:
  - 未选中: 边框颜色 #E4E4E4，背景色 透明，文字颜色 #989898 (colorGray600)
  - 选中: 边框颜色 #989898，内部圆点颜色 #989898，文字颜色 #989898 (colorGray600)

### 形状规范

单选框始终为圆形，这是与复选框的主要区别之一。

### 文字规范

- 标准尺寸: 14px，Noto Sans TC，Regular (400)
- 小号尺寸: 12px，Noto Sans TC，Regular (400)

### 间距规范

- 单选框与文字之间的间距: 8px
- 组内单选框之间的垂直间距: 12px (标准尺寸)，8px (小号尺寸)

## 组件 API

### 属性接口

```typescript
// 单选框尺寸
export type RadioSize = 'normal' | 'small';

// 单选框颜色
export type RadioColor =
  | 'default'
  | 'primary'
  | 'success'
  | 'danger'
  | 'warning';

// 单选框属性
export interface RadioProps {
  /**
   * 单选框的值
   */
  value?: string;

  /**
   * 是否选中
   * @default false
   */
  checked?: boolean;

  /**
   * 默认是否选中（非受控模式）
   * @default false
   */
  defaultChecked?: boolean;

  /**
   * 单选框尺寸
   * @default 'normal'
   */
  size?: RadioSize;

  /**
   * 单选框颜色
   * @default 'default'
   */
  color?: RadioColor;

  /**
   * 是否禁用
   * @default false
   */
  disabled?: boolean;

  /**
   * 单选框文本内容
   */
  children?: React.ReactNode;

  /**
   * 自定义样式
   */
  style?: StyleObject;

  /**
   * 选中状态变化回调函数
   */
  onChange?: (checked: boolean, value?: string) => void;
}

// 单选框组属性
export interface RadioGroupProps {
  /**
   * 当前选中的选项值
   */
  value?: string;

  /**
   * 默认选中的选项值（非受控模式）
   */
  defaultValue?: string;

  /**
   * 单选框组尺寸，会被子单选框继承
   * @default 'normal'
   */
  size?: RadioSize;

  /**
   * 单选框组颜色，会被子单选框继承
   * @default 'default'
   */
  color?: RadioColor;

  /**
   * 是否禁用，会被子单选框继承
   * @default false
   */
  disabled?: boolean;

  /**
   * 自定义样式
   */
  style?: StyleObject;

  /**
   * 选项变化回调函数
   */
  onChange?: (value: string) => void;

  /**
   * 单选框组子元素
   */
  children?: React.ReactNode;

  /**
   * 可选项配置，优先级高于 children
   */
  options?: Array<{
    label: React.ReactNode;
    value: string;
    disabled?: boolean;
  }>;
}
```

## 使用示例

### 基本用法

```tsx
import { Radio } from '@dp-frontend/ui';

// 基础单选框
<Radio onChange={(checked) => console.log('Checked:', checked)}>
  选项1
</Radio>

// 默认选中
<Radio defaultChecked>
  默认选中
</Radio>

// 禁用状态
<Radio disabled>
  禁用选项
</Radio>

// 禁用且选中
<Radio disabled checked>
  禁用且选中
</Radio>
```

### 不同尺寸

```tsx
import { Radio } from '@dp-frontend/ui';

// 标准尺寸
<Radio size="normal">标准尺寸</Radio>

// 小号尺寸
<Radio size="small">小号尺寸</Radio>
```

### 不同颜色

```tsx
import { Radio } from '@dp-frontend/ui';

// 默认颜色
<Radio color="default" checked>默认颜色</Radio>

// 主要颜色
<Radio color="primary" checked>主要颜色</Radio>

// 成功颜色
<Radio color="success" checked>成功颜色</Radio>

// 危险颜色
<Radio color="danger" checked>危险颜色</Radio>

// 警告颜色
<Radio color="warning" checked>警告颜色</Radio>
```

### 单选框组

```tsx
import { Radio } from '@dp-frontend/ui';

// 基础单选框组
<Radio.Group
  defaultValue="apple"
  onChange={(value) => console.log('Selected:', value)}
>
  <Radio value="apple">苹果</Radio>
  <Radio value="orange">橙子</Radio>
  <Radio value="banana">香蕉</Radio>
</Radio.Group>

// 使用 options 属性
<Radio.Group
  options={[
    { label: '选项1', value: 'option1' },
    { label: '选项2', value: 'option2' },
    { label: '选项3', value: 'option3', disabled: true }
  ]}
  defaultValue="option1"
  onChange={(value) => console.log('Selected:', value)}
/>

// 禁用整个单选框组
<Radio.Group disabled defaultValue="apple">
  <Radio value="apple">苹果</Radio>
  <Radio value="orange">橙子</Radio>
  <Radio value="banana">香蕉</Radio>
</Radio.Group>
```

## 实现注意事项

1. 使用 React Strict DOM 的 `html.div` 和 `html.input` 元素实现单选框
2. 使用 `useThemeTailwind` 钩子提供的 `tw()` 函数应用 Tailwind 样式
3. 使用 `useRadioTailwind` 钩子根据单选框的尺寸、类型和状态动态生成样式类
4. 支持受控和非受控两种使用模式
5. 确保单选框在禁用状态下不触发 onChange 事件
6. 确保单选框组能正确管理子单选框的状态，保证同一组内只有一个选项被选中
7. 确保单选框在不同平台上具有一致的外观和行为
8. 使用圆形样式实现单选框的视觉效果，选中状态显示内部圆点
9. 确保单选框具有良好的可访问性，支持键盘操作和屏幕阅读器
10. 单选框组应提供 value 和 onChange 接口，方便与表单组件集成
