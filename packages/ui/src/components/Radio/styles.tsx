import clsx from 'clsx';
import { useMemo } from 'react';
import { css } from 'react-strict-dom';
import type { StyleObject } from 'react-strict-dom-tailwind';
import { tw } from 'react-strict-dom-tailwind';

import { colors, semanticColors } from '../../theme/vars.stylex';
import type { RadioProps } from './index';

/**
 * Radio组件的样式钩子
 * 合并所有样式钩子，通过一个对象统一导出
 */
export const useRadioStyles = (
  props: Pick<RadioProps, 'size' | 'color' | 'disabled' | 'checked'>
) => {
  const {
    size = 'normal',
    color = 'default',
    disabled = false,
    checked = false,
  } = props;

  // 容器样式
  const container = useMemo(
    () =>
      clsx({
        'radio-container': true,
        'radio-disabled': disabled,

        // 尺寸变体
        'radio-normal': size === 'normal',
        'radio-small': size === 'small',
      }),
    [size, disabled]
  );

  // 盒子样式
  const box = useMemo(
    () =>
      clsx({
        // 基础样式
        'radio-box': true,

        // 尺寸样式
        'radio-normal-box': size === 'normal',
        'radio-small-box': size === 'small',

        // 颜色变体 - 未选中状态使用默认样式
        'radio-box-default': color === 'default' && !checked,
        'radio-box-default-checked': color === 'default' && checked,
        'radio-box-primary': color === 'primary' && checked,
        'radio-box-success': color === 'success' && checked,
        'radio-box-danger': color === 'danger' && checked,
        'radio-box-warning': color === 'warning' && checked,

        // 禁用状态
        'radio-box-default-disabled': disabled && !checked,
        'radio-box-checked-disabled': disabled && checked,

        // 状态变体
        'radio-checked': checked,
        'radio-unchecked': !checked,
      }),
    [size, color, checked, disabled]
  );

  // 标签样式
  const label = useMemo(
    () =>
      clsx({
        // 基础样式
        'radio-label': true,

        // 尺寸样式
        'radio-small-label': size === 'small',

        // 颜色样式
        'radio-text-default': color === 'default',
        'radio-text-primary': color === 'primary',
        'radio-text-success': color === 'success',
        'radio-text-danger': color === 'danger',
        'radio-text-warning': color === 'warning',
      }),
    [size, color]
  );

  // 内部圆点样式
  const dot = useMemo(
    () =>
      clsx({
        // 基础样式
        'radio-dot': true,

        // 尺寸样式
        'radio-dot-normal': size === 'normal',
        'radio-dot-small': size === 'small',

        // 颜色样式 - 禁用状态优先
        'radio-dot-disabled': disabled,
        'radio-dot-default': !disabled && color === 'default',
        'radio-dot-primary': !disabled && color === 'primary',
        'radio-dot-success': !disabled && color === 'success',
        'radio-dot-danger': !disabled && color === 'danger',
        'radio-dot-warning': !disabled && color === 'warning',
      }),
    [size, color, disabled]
  );

  return {
    container,
    box,
    label,
    dot,
  };
};

/**
 * Radio组件的样式定义
 */
export const radioTheme: StyleObject = {
  // 容器样式
  'radio-container': tw('flex flex-row items-center'),

  // 尺寸变体
  'radio-normal': tw('gap-2'),
  'radio-small': tw('gap-2'),

  // 单选框样式
  'radio-box': tw('flex-cc relative border-solid rounded-full'),
  'radio-normal-box': tw('w-[20px] h-[20px]'),
  'radio-small-box': tw('w-[16px] h-[16px]'),

  // 标签样式
  'radio-label': tw('text-[14px]'),
  'radio-small-label': tw('text-[12px]'),

  // 文字颜色样式
  'radio-text-default': tw('text-gray-800'),
  'radio-text-primary': tw('text-primary'),
  'radio-text-success': tw('text-success'),
  'radio-text-danger': tw('text-danger'),
  'radio-text-warning': tw('text-warning'),

  // 禁用样式
  'radio-disabled': tw('cursor-not-allowed opacity-50'),

  // 颜色样式
  ...css.create({
    // 未选中状态
    'radio-box-default': {
      borderColor: semanticColors.defaultBorder, // #D5D5D5
      backgroundColor: 'transparent',
      borderWidth: 1,
    },
    'radio-box-default-disabled': {
      borderColor: colors.colorGray940, // #E4E4E4
      backgroundColor: 'transparent',
      borderWidth: 1,
    },

    // 选中状态 - 默认
    'radio-box-default-checked': {
      borderColor: semanticColors.defaultBorder, // #D5D5D5
      backgroundColor: 'transparent',
      borderWidth: 1,
    },
    // 选中状态 - 主要
    'radio-box-primary': {
      borderColor: semanticColors.primary, // #24231D
      backgroundColor: 'transparent',
      borderWidth: 1,
    },
    'radio-box-checked-disabled': {
      borderColor: colors.colorGray600, // #989898
      backgroundColor: 'transparent',
      borderWidth: 1,
    },

    // 选中状态 - 成功
    'radio-box-success': {
      borderColor: semanticColors.success, // #34C759
      backgroundColor: 'transparent',
      borderWidth: 1,
    },

    // 选中状态 - 危险
    'radio-box-danger': {
      borderColor: semanticColors.danger, // #FF3B30
      backgroundColor: 'transparent',
      borderWidth: 1,
    },

    // 选中状态 - 警告
    'radio-box-warning': {
      borderColor: semanticColors.warning, // #FF9735
      backgroundColor: 'transparent',
      borderWidth: 1,
    },
  }),

  // 内部圆点样式
  'radio-dot': tw('rounded-full'),
  'radio-dot-normal': tw('w-[10px] h-[10px]'),
  'radio-dot-small': tw('w-[8px] h-[8px]'),

  // 内部圆点颜色样式
  ...css.create({
    'radio-dot-default': {
      backgroundColor: semanticColors.defaultText, // #24231D
    },
    'radio-dot-primary': {
      backgroundColor: semanticColors.primary, // #24231D
    },
    'radio-dot-success': {
      backgroundColor: semanticColors.success, // #34C759
    },
    'radio-dot-danger': {
      backgroundColor: semanticColors.danger, // #FF3B30
    },
    'radio-dot-warning': {
      backgroundColor: semanticColors.warning, // #FF9735
    },
    'radio-dot-disabled': {
      backgroundColor: colors.colorGray600, // #989898
    },
  }),

  // 选中和未选中状态样式
  'radio-checked': tw('border border-solid'),
  'radio-unchecked': tw('border border-solid bg-transparent'),
};

export default radioTheme;
