import clsx from 'clsx';
import { useMemo } from 'react';
import { css } from 'react-strict-dom';
import type { StyleObject } from 'react-strict-dom-tailwind';
import { tw } from 'react-strict-dom-tailwind';

import { colors } from '../../theme/vars.stylex';
import type { ToastProps } from './index';

// Toast 样式定义
export const ToastTheme: StyleObject = {
  // 基础样式
  'toast-base': tw(
    'flex-cc rounded-[20px] px-[20px] py-[9px] text-[14px] z-50'
  ),

  // 类型样式
  ...css.create({
    'toast-default': {
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      color: colors.colorWhite,
    },
    'toast-success': {
      backgroundColor: colors.colorGreen500,
      color: colors.colorWhite,
    },
    'toast-error': {
      backgroundColor: colors.colorRed500,
      color: colors.colorWhite,
    },
    'toast-warning': {
      backgroundColor: colors.colorOrange500,
      color: colors.colorWhite,
    },
  }),

  // 容器样式 - 使用Modal实现居中
  'toast-container': tw('flex-cc'),
};

// Toast 样式钩子
export const useToastTailwind = (props: Pick<ToastProps, 'type'>) => {
  const { type = 'default' } = props;

  const toastStyles = useMemo(
    () =>
      clsx({
        'toast-base': true,

        // 类型变体
        'toast-default': type === 'default',
        'toast-success': type === 'success',
        'toast-error': type === 'error',
        'toast-warning': type === 'warning',
      }),
    [type]
  );

  return toastStyles;
};
