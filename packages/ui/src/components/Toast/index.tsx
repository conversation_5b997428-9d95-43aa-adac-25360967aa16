import React, { useEffect, useState } from 'react';
import Modal from 'react-native-modal';
import { html } from 'react-strict-dom';

import { useThemeTailwind } from '../../hooks';
import type { StyleType } from '../Button';
import { useToastTailwind } from './styles';

// Toast 类型
export type ToastType = 'default' | 'success' | 'error' | 'warning';

// Toast 属性
export interface ToastProps {
  /**
   * Toast 内容
   */
  content?: string;

  /**
   * Toast 类型
   * @default 'default'
   */
  type?: ToastType;

  /**
   * Toast 显示时长(毫秒)，设置为 0 则不会自动关闭
   * @default 2000
   */
  duration?: number;

  /**
   * Toast 显示/隐藏回调
   */
  onClose?: () => void;

  /**
   * 自定义样式
   */
  style?: StyleType;
}

// Toast API 接口
export interface ToastAPI {
  show: (content: string, options?: Omit<ToastProps, 'content'>) => void;
  success: (
    content: string,
    options?: Omit<ToastProps, 'content' | 'type'>
  ) => void;
  error: (
    content: string,
    options?: Omit<ToastProps, 'content' | 'type'>
  ) => void;
  warning: (
    content: string,
    options?: Omit<ToastProps, 'content' | 'type'>
  ) => void;
  hide: () => void;
}

// 全局状态管理
type ToastState = {
  visible: boolean;
  props: ToastProps | null;
  timer: NodeJS.Timeout | null;
};

const initialState: ToastState = {
  visible: false,
  props: null,
  timer: null,
};

// 队列存储等待显示的Toast
const toastQueue: ToastProps[] = [];
let toastState = { ...initialState };
let listeners: (() => void)[] = [];
// 标记是否有Toast正在显示
let isProcessing = false;

// 通知所有监听器状态已更新
const notifyListeners = () => {
  listeners.forEach((listener) => listener());
};

// 清除定时器
const clearTimer = () => {
  if (toastState.timer) {
    clearTimeout(toastState.timer);
    toastState.timer = null;
  }
};

// 显示下一个Toast
const showNextToast = () => {
  if (toastQueue.length > 0 && !isProcessing) {
    isProcessing = true;
    const nextToast = toastQueue.shift()!;

    toastState = {
      visible: true,
      props: nextToast,
      timer: null,
    };

    notifyListeners();

    // 设置自动关闭定时器
    if (nextToast.duration !== 0) {
      toastState.timer = setTimeout(() => {
        hideToast();
      }, nextToast.duration || 2000);
    }
  }
};

// 隐藏 Toast
const hideToast = () => {
  clearTimer();

  if (toastState.props?.onClose) {
    toastState.props.onClose();
  }

  toastState.visible = false;
  notifyListeners();
};

// 显示 Toast
const showToast = (props: ToastProps) => {
  // 添加到队列
  toastQueue.push(props);

  // 如果当前有Toast正在显示，立即结束它
  if (isProcessing) {
    hideToast();
  } else {
    // 没有Toast正在显示，直接显示
    showNextToast();
  }
};

// Toast 组件实现
const ToastComponent: React.FC<ToastProps> = (props) => {
  const { content, type = 'default', style } = props;
  const { tw } = useThemeTailwind();
  const toastStyles = useToastTailwind({ type });

  return (
    <html.div style={[tw(toastStyles), style]}>
      <html.span>{content}</html.span>
    </html.div>
  );
};

// Toast 容器组件
export const ToastContainer: React.FC = () => {
  const [state, setState] = useState(toastState);
  const { tw } = useThemeTailwind();

  // 订阅状态更新
  useEffect(() => {
    const listener = () => {
      setState({ ...toastState });
    };

    listeners.push(listener);

    return () => {
      listeners = listeners.filter((l) => l !== listener);
    };
  }, []);

  // 处理动画结束事件
  const handleAnimationEnd = () => {
    // 重置处理状态
    isProcessing = false;
    // 检查队列中是否有下一个Toast
    showNextToast();
  };

  // 使用RNModal实现居中定位
  return (
    <Modal
      isVisible={state.visible}
      animationIn="fadeIn"
      animationOut="fadeOut"
      animationInTiming={200}
      animationOutTiming={200}
      useNativeDriver
      hasBackdrop={false}
      coverScreen={false}
      onModalHide={handleAnimationEnd}
      style={tw('toast-container')}
    >
      <ToastComponent {...state.props} />
    </Modal>
  );
};

// Toast API 实现
export const Toast: ToastAPI = {
  show: (content, options) => {
    showToast({ content, ...options });
  },
  success: (content, options) => {
    showToast({ content, type: 'success', ...options });
  },
  error: (content, options) => {
    showToast({ content, type: 'error', ...options });
  },
  warning: (content, options) => {
    showToast({ content, type: 'warning', ...options });
  },
  hide: () => {
    hideToast();
  },
};
