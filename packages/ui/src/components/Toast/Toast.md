# Toast 提示组件

## 组件概述

Toast 是一个轻量级的消息提示组件，用于向用户展示简短的提示信息。它会在屏幕中央短暂显示，然后自动消失，不会打断用户的操作流程。适用于操作反馈、提示信息等场景。

Toast 组件已集成到 ConfigProvider 中，使用 ConfigProvider 的应用无需手动添加 ToastContainer 组件。当新的 Toast 出现时，会立即结束当前显示的 Toast，并在当前 Toast 消失后显示新的 Toast。

## 设计规范

### 尺寸变体

Toast 组件只有一种尺寸，但宽度会根据内容自适应：
- **标准尺寸**: 内边距为 9px 20px，圆角为 20px

### 颜色变体

Toast 支持以下颜色变体：
- **默认 (default)**: 黑色半透明背景 (rgba(0, 0, 0, 0.8))，白色文字，用于一般性提示
- **成功 (success)**: 绿色背景 (#23C343)，白色文字，用于成功操作的反馈
- **错误 (error)**: 红色背景 (#F76560)，白色文字，用于错误信息的提示
- **警告 (warning)**: 橙色背景 (#FF9A2E)，白色文字，用于警告信息的提示

### 位置

Toast 始终显示在屏幕正中央位置，使用 react-native-modal 实现居中定位。

### 状态变体

Toast 组件有以下状态：
- **显示 (show)**: 组件显示状态，带有淡入动画效果
- **隐藏 (hide)**: 组件隐藏状态，带有淡出动画效果

### 颜色规范

- **默认 Toast**:
  - 背景色: rgba(0, 0, 0, 0.8)
  - 文字颜色: #FFFFFF (白色)

- **成功 Toast**:
  - 背景色: #23C343 (绿色)
  - 文字颜色: #FFFFFF (白色)

- **错误 Toast**:
  - 背景色: #F76560 (红色)
  - 文字颜色: #FFFFFF (白色)

- **警告 Toast**:
  - 背景色: #FF9A2E (橙色)
  - 文字颜色: #FFFFFF (白色)

### 文字和内边距规范

- 文字大小: 14px
- 字重: Regular (400)
- 内边距: 9px 20px (垂直 水平)
- 圆角: 20px

## 组件 API

### 属性接口

```typescript
export interface ToastProps {
  /**
   * Toast 内容
   */
  content?: string;

  /**
   * Toast 类型
   * @default 'default'
   */
  type?: 'default' | 'success' | 'error' | 'warning';

  /**
   * Toast 显示时长(毫秒)，设置为 0 则不会自动关闭
   * @default 2000
   */
  duration?: number;

  /**
   * Toast 显示/隐藏回调
   */
  onClose?: () => void;

  /**
   * 自定义样式
   */
  style?: StyleType;
}

export interface ToastAPI {
  /**
   * 显示默认 Toast
   */
  show: (content: string, options?: Omit<ToastProps, 'content'>) => void;

  /**
   * 显示成功 Toast
   */
  success: (content: string, options?: Omit<ToastProps, 'content' | 'type'>) => void;

  /**
   * 显示错误 Toast
   */
  error: (content: string, options?: Omit<ToastProps, 'content' | 'type'>) => void;

  /**
   * 显示警告 Toast
   */
  warning: (content: string, options?: Omit<ToastProps, 'content' | 'type'>) => void;

  /**
   * 隐藏当前显示的 Toast
   */
  hide: () => void;
}
```

## 使用示例

### 基本用法

```tsx
import { Toast } from '@dp-frontend/ui';

// 基本用法
Toast.show('这是一条提示信息');

// 不同类型
Toast.success('操作成功');
Toast.error('操作失败');
Toast.warning('警告信息');

// Toast 始终显示在屏幕中央

// 自定义显示时长
Toast.show('这条提示将显示5秒', { duration: 5000 });

// 不自动关闭
Toast.show('这条提示不会自动关闭', { duration: 0 });

// 关闭回调
Toast.show('操作完成', {
  onClose: () => {
    console.log('Toast已关闭');
  }
});

// 手动关闭
Toast.show('长时间提示');
setTimeout(() => {
  Toast.hide();
}, 1000);
```

### 配置 Toast 容器

Toast 组件已集成到 ConfigProvider 中，默认情况下不需要手动添加 ToastContainer。如果需要禁用内置的 Toast 容器，可以设置 ConfigProvider 的 disableToast 属性：

```tsx
import { ConfigProvider, themes } from '@dp-frontend/ui';

// 禁用内置的 Toast 容器
<ConfigProvider theme={themes} disableToast>
  <App />
</ConfigProvider>
```

如果需要在特定位置手动添加 Toast 容器，可以先禁用内置的 Toast 容器，然后手动添加：

```tsx
import { ConfigProvider, themes, ToastContainer } from '@dp-frontend/ui';

<ConfigProvider theme={themes} disableToast>
  <App />
  <ToastContainer /> {/* 手动添加 Toast 容器 */}
</ConfigProvider>
```

## 实现注意事项

1. 使用 React Strict DOM 的 `html.*` 元素实现组件内容
2. 使用 `useThemeTailwind` 钩子提供的 `tw()` 函数应用 Tailwind 样式
3. 使用 `react-native-modal` 实现居中定位和动画效果
4. 实现函数式调用 API，使用队列管理多个 Toast 请求
5. 确保 Toast 组件在不同平台上具有一致的外观和行为
6. 实现自动关闭功能，使用 setTimeout 控制显示时长
7. 实现动画效果，包括淡入淡出动画（fadeIn/fadeOut）
8. 确保同一时间只显示一个 Toast，新的 Toast 会立即结束当前显示的 Toast
9. 当新的 Toast 出现时，会在当前 Toast 完全消失后才显示新的 Toast
10. 允许背景点击穿透，Toast 不会阻止用户与下层界面的交互
11. 考虑跨平台兼容性，在 Web 和 Native 环境下都能正常工作
