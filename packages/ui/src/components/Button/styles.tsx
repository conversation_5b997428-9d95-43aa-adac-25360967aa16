import clsx from 'clsx';
import { useMemo } from 'react';
import { css } from 'react-strict-dom';
import type { StyleObject } from 'react-strict-dom-tailwind';
import { tw } from 'react-strict-dom-tailwind';

import { colors, semanticColors } from '../../theme/vars.stylex';
import type { ButtonProps } from '.';

export const useButtonTailwind = (
  props: Pick<ButtonProps, 'size' | 'color' | 'disabled' | 'shape' | 'fill'> & {
    isPressed?: boolean;
  }
) => {
  const {
    size = 'medium',
    color = 'default',
    disabled = false,
    isPressed = false,
    shape = 'rounded',
    fill = 'solid',
  } = props;

  const buttonStyles = useMemo(
    () =>
      clsx({
        'btn-flex': true,
        'btn-disabled': disabled,

        'btn-large': size === 'large',
        'btn-medium': !['large', 'small'].includes(size),
        'btn-small': size === 'small',

        'btn-square': shape === 'square',
        'btn-rounded': shape !== 'square',

        'btn-default': !['primary', 'success', 'danger', 'warning'].includes(
          color
        ),
        'btn-default-pressed': color === 'default' && isPressed,
        'btn-default-disabled': color === 'default' && disabled,

        'btn-primary': color === 'primary',
        'btn-primary-pressed': color === 'primary' && isPressed,
        'btn-primary-disabled': color === 'primary' && disabled,

        'btn-success': color === 'success',
        'btn-success-pressed': color === 'success' && isPressed,
        'btn-success-disabled': color === 'success' && disabled,

        'btn-danger': color === 'danger',
        'btn-danger-pressed': color === 'danger' && isPressed,
        'btn-danger-disabled': color === 'danger' && disabled,

        'btn-warning': color === 'warning',
        'btn-warning-pressed': color === 'warning' && isPressed,
        'btn-warning-disabled': color === 'warning' && disabled,

        'btn-solid': !['outline', 'none'].includes(fill),
        'btn-outline': fill === 'outline',
        'btn-none': fill === 'none',
      }),
    [size, color, disabled, fill, shape, isPressed]
  );
  return buttonStyles;
};

export const buttonTheme: StyleObject = {
  'btn-flex': tw('flex-cc box-border'),

  'btn-disabled': tw('cursor-not-allowed'),

  // 尺寸
  'btn-large': tw('w-[294px] h-[50px] p-[16px] text-[14px]'),
  'btn-medium': tw('w-[160px] h-[36px] p-[9px] text-[14px]'),
  'btn-small': tw('w-[82px] h-[28px] px-[9px] text-[12px]'),

  // 图标
  'btn-icon': tw('mr-2'),

  // 形状
  'btn-square': tw('rounded-[0px]'),
  'btn-rounded': tw('rounded-[999px]'),

  // 颜色
  ...css.create({
    'btn-default': {
      color: semanticColors.defaultText, // #24231D - 深灰色
      borderColor: semanticColors.defaultBorder, // #D5D5D5 - 浅灰色边框
      backgroundColor: semanticColors.default, // #FFFFFF - 白色背景
    },
    'btn-default-pressed': {
      backgroundColor: colors.colorGray950, // #F0F0F0 - 背景色
    },
    'btn-default-disabled': {
      color: semanticColors.defaultTextDisabled,
      borderColor: semanticColors.defaultBorderDisabled,
      backgroundColor: semanticColors.defaultDisabled,
    },

    'btn-primary': {
      color: semanticColors.primaryText,
      borderColor: semanticColors.primary,
      backgroundColor: semanticColors.primary,
    },
    'btn-primary-pressed': {
      backgroundColor: colors.colorGray150, // #272636 - 深灰色变体
    },
    'btn-primary-disabled': {
      color: semanticColors.primaryTextDisabled,
      borderColor: semanticColors.primaryDisabled,
      backgroundColor: semanticColors.primaryDisabled,
    },

    'btn-success': {
      color: semanticColors.successText,
      borderColor: semanticColors.success,
      backgroundColor: semanticColors.success,
    },
    'btn-success-pressed': {
      backgroundColor: colors.colorGreen700, // #D9EC78 - 绿色变体
    },
    'btn-success-disabled': {
      color: semanticColors.successTextDisabled,
      borderColor: semanticColors.successDisabled,
      backgroundColor: semanticColors.successDisabled,
    },

    'btn-danger': {
      color: semanticColors.dangerText,
      borderColor: semanticColors.danger,
      backgroundColor: semanticColors.danger,
    },
    'btn-danger-pressed': {
      backgroundColor: colors.colorRed700, // #FFECE8 - 红色变体
    },
    'btn-danger-disabled': {
      color: semanticColors.dangerTextDisabled,
      borderColor: semanticColors.dangerDisabled,
      backgroundColor: semanticColors.dangerDisabled,
    },

    'btn-warning': {
      color: semanticColors.warningText,
      borderColor: semanticColors.warning,
      backgroundColor: semanticColors.warning,
    },
    'btn-warning-pressed': {
      backgroundColor: colors.colorOrange700, // #FFF7E8 - 橙色变体
    },
    'btn-warning-disabled': {
      color: semanticColors.warningTextDisabled,
      borderColor: semanticColors.warningDisabled,
      backgroundColor: semanticColors.warningDisabled,
    },
  }),

  // 填充
  ...css.create({
    'btn-none': {
      backgroundColor: 'transparent',
      borderWidth: 0,
    },
    'btn-solid': {
      borderWidth: 0.5,
    },
    'btn-outline': {
      backgroundColor: 'transparent',
      borderWidth: 0.5,
    },
  }),
};
