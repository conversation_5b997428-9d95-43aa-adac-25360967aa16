import React, { useState } from 'react';
import { html } from 'react-strict-dom';
import type { StyleObject } from 'react-strict-dom-tailwind';

import { useThemeTailwind } from '../../hooks';
import { useButtonTailwind } from './styles';

// 按钮尺寸
export type ButtonSize = 'large' | 'medium' | 'small';

// 按钮类型
export type ButtonColor =
  | 'default'
  | 'primary'
  | 'success'
  | 'danger'
  | 'warning'
  | 'ghost';

// 按钮填充
export type ButtonFill = 'solid' | 'outline' | 'none';

// 按钮形状
export type ButtonShape = 'square' | 'rounded';

// 按钮属性
export interface ButtonProps {
  /**
   * 按钮点击事件处理函数
   */
  onClick?: () => void;

  /**
   * 按钮尺寸，默认为 'large'
   */
  size?: ButtonSize;

  /**
   * 按钮类型，默认为 'primary'
   */
  color?: ButtonColor;

  /**
   * 按钮形状，默认为 'rounded'
   */
  shape?: ButtonShape;

  /**
   * 按钮填充，默认为 'solid'
   */
  fill?: ButtonFill;

  /**
   * 是否禁用按钮，设置为 true 时会将状态设为 'disabled'
   */
  disabled?: boolean;

  /**
   * 自定义按钮样式
   */
  style?: StyleObject;

  /**
   * 按钮图标
   */
  icon?: React.ReactNode;

  /**
   * 按钮内容
   */
  children?: React.ReactNode;
}

/**
 * 通用按钮组件
 *
 * 基于Figma设计稿实现，支持多种尺寸、类型和状态
 */
export const Button: React.FC<ButtonProps> = (props) => {
  const {
    onClick,
    color = 'default',
    size = 'large',
    disabled = false,
    shape = 'rounded',
    fill = 'solid',
    style,
    icon,
    children,
  } = props;
  const { tw } = useThemeTailwind();

  // 使用状态跟踪按钮的按下状态
  const [isPressed, setIsPressed] = useState(false);
  const buttonStyles = useButtonTailwind({
    size,
    color,
    disabled,
    shape,
    fill,
    isPressed,
  });

  // 处理按钮按下事件
  const handlePressIn = () => {
    if (!disabled) setIsPressed(true);
  };

  // 处理按钮释放事件
  const handlePressOut = () => {
    setIsPressed(false);
  };

  const renderIcon = () => {
    if (!icon) return null;
    return <html.span style={tw('btn-icon')}>{icon}</html.span>;
  };

  // 渲染按钮
  return (
    <html.button
      style={[tw(buttonStyles), style]}
      disabled={disabled}
      onClick={onClick}
      onMouseDown={handlePressIn}
      onMouseUp={handlePressOut}
      onMouseLeave={handlePressOut}
      onTouchStart={handlePressIn}
      onTouchEnd={handlePressOut}
    >
      {renderIcon()}
      <html.div>{children}</html.div>
    </html.button>
  );
};

export default Button;
