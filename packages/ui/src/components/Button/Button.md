# Button 按钮组件

## 组件概述

Button 是一个通用按钮组件，基于 Figma 设计稿实现，支持多种尺寸、类型和状态，适用于各种交互场景。组件使用 React Strict DOM 和 react-strict-dom-tailwind 实现，确保在 Web 和移动平台上具有一致的外观和行为。

## 设计规范

### 尺寸变体

按钮提供三种尺寸变体：

- **大号 (large)**: 高度为 50px，适用于主要操作按钮，如登录、提交等
- **中号 (medium)**: 高度为 36px，适用于一般操作按钮
- **小号 (small)**: 高度为 28px，适用于辅助操作或空间受限的场景

### 颜色变体

按钮提供多种颜色变体：

- **默认 (default)**: 白色背景，深灰色文字，适用于次要操作
- **主要 (primary)**: 深灰色背景，白色文字，用于页面主要操作
- **成功 (success)**: 绿色背景，白色文字，用于表示成功或确认操作
- **危险 (danger)**: 红色背景，白色文字，用于表示危险或删除操作
- **警告 (warning)**: 橙色背景，白色文字，用于表示警告操作

### 填充变体

按钮提供三种填充变体：

- **实心 (solid)**: 有背景色，无边框，默认样式
- **描边 (outline)**: 透明背景，有边框，适用于次要操作
- **无填充 (none)**: 透明背景，无边框，仅文字和图标，适用于低强调操作

### 形状变体

按钮提供两种形状变体：

- **圆角 (rounded)**: 圆角按钮，默认样式
- **方形 (square)**: 直角按钮，适用于特定场景

### 状态变体

按钮支持三种状态：

- **默认 (default)**: 按钮的正常状态
- **点击 (pressed)**: 按钮被点击时的状态
- **禁用 (disabled)**: 按钮不可用的状态

### 颜色规范

- **默认按钮 (default)**:
  - 默认: 背景色 #FFFFFF，边框颜色 #D5D5D5，文字颜色 #24231D
  - 点击: 背景色 #F0F0F0，边框颜色 #D5D5D5，文字颜色 #24231D
  - 禁用: 背景色 #E4E4E4，边框颜色 #F0F0F0，文字颜色 #24231D

- **主要按钮 (primary)**:
  - 默认: 背景色 #24231D，文字颜色 #FFFFFF
  - 点击: 背景色 #272636，文字颜色 #FFFFFF
  - 禁用: 背景色 #989898，文字颜色 #F0F0F0

- **成功按钮 (success)**:
  - 默认: 背景色 #34C759，文字颜色 #FFFFFF
  - 点击: 背景色变深，文字颜色 #FFFFFF
  - 禁用: 背景色变浅，文字颜色 #FFFFFF

- **危险按钮 (danger)**:
  - 默认: 背景色 #FF3B30，文字颜色 #FFFFFF
  - 点击: 背景色变深，文字颜色 #FFFFFF
  - 禁用: 背景色变浅，文字颜色 #FFFFFF

- **警告按钮 (warning)**:
  - 默认: 背景色 #FF9735，文字颜色 #FFFFFF
  - 点击: 背景色变深，文字颜色 #FFFFFF
  - 禁用: 背景色变浅，文字颜色 #FFFFFF

### 形状规范

- **圆角 (rounded)**: 圆角 999px (pill 形状)，适用于所有尺寸
- **方形 (square)**: 圆角 0px，直角按钮

### 文字规范

- 大号按钮: 14px，Noto Sans TC，Regular (400)
- 中号按钮: 14px，Noto Sans TC，Medium (500)
- 小号按钮: 12px，Noto Sans TC，Medium (500)

### 内边距规范

- 大号按钮: 16px 91px (垂直 水平)
- 中号按钮: 9px 20px (垂直 水平)
- 小号按钮: 9px 14px (垂直 水平)

## 组件 API

### 属性接口

```typescript
// 按钮尺寸
export type ButtonSize = 'large' | 'medium' | 'small';

// 按钮颜色
export type ButtonColor =
  | 'default'
  | 'primary'
  | 'success'
  | 'danger'
  | 'warning'
  | 'ghost';

// 按钮填充
export type ButtonFill = 'solid' | 'outline' | 'none';

// 按钮形状
export type ButtonShape = 'square' | 'rounded';

// 定义样式类型
export type StyleType = Record<string, string | number | undefined> | undefined;

// 按钮属性
export interface ButtonProps {
  /**
   * 按钮点击事件处理函数
   */
  onClick: () => void;

  /**
   * 按钮尺寸，默认为 'large'
   */
  size?: ButtonSize;

  /**
   * 按钮颜色，默认为 'default'
   */
  color?: ButtonColor;

  /**
   * 按钮形状，默认为 'rounded'
   */
  shape?: ButtonShape;

  /**
   * 按钮填充，默认为 'solid'
   */
  fill?: ButtonFill;

  /**
   * 是否禁用按钮，设置为 true 时会将状态设为 'disabled'
   */
  disabled?: boolean;

  /**
   * 自定义按钮样式
   */
  style?: StyleType;

  /**
   * 按钮图标
   */
  icon?: React.ReactNode;

  /**
   * 按钮内容
   */
  children?: React.ReactNode;
}
```

## 使用示例

### 基本用法

```tsx
import { Button } from '@dp-frontend/ui';

// 默认按钮
<Button
  onClick={() => console.log('Default button pressed')}
>
  默认按钮
</Button>

// 主要按钮
<Button
  color="primary"
  onClick={() => console.log('Primary button pressed')}
>
  主要按钮
</Button>

// 成功按钮
<Button
  color="success"
  onClick={() => console.log('Success button pressed')}
>
  成功按钮
</Button>

// 危险按钮
<Button
  color="danger"
  onClick={() => console.log('Danger button pressed')}
>
  危险按钮
</Button>

// 警告按钮
<Button
  color="warning"
  onClick={() => console.log('Warning button pressed')}
>
  警告按钮
</Button>

// 描边按钮
<Button
  fill="outline"
  onClick={() => console.log('Outline button pressed')}
>
  描边按钮
</Button>

// 无填充按钮
<Button
  fill="none"
  onClick={() => console.log('None fill button pressed')}
>
  无填充按钮
</Button>

// 方形按钮
<Button
  shape="square"
  onClick={() => console.log('Square button pressed')}
>
  方形按钮
</Button>

// 不同尺寸按钮
<Button size="large">大号按钮</Button>
<Button size="medium">中号按钮</Button>
<Button size="small">小号按钮</Button>

// 禁用按钮
<Button
  disabled={true}
  onClick={() => console.log('This will not be called')}
>
  禁用按钮
</Button>
```

### 带图标按钮

```tsx
import { Button } from '@dp-frontend/ui';
import LineIcon from '../icons/LineIcon';

// 带图标按钮
<Button
  icon={<LineIcon />}
  onClick={() => console.log('Icon button pressed')}
>
  使用 LINE 登录
</Button>
```

## 实现注意事项

1. 使用 React Strict DOM 的 `html.button` 和 `html.div` 元素实现按钮
2. 使用 `useThemeTailwind` 钩子提供的 `tw()` 函数应用 Tailwind 样式
3. 使用 `useButtonTailwind` 钩子根据按钮的尺寸、类型和状态动态生成样式类
4. 支持自定义样式覆盖默认样式
5. 确保按钮在禁用状态下不触发 onClick 事件
6. 通过内部状态 `isPressed` 实现按钮的 pressed 状态样式变化
7. 确保按钮在不同平台上具有一致的外观和行为

## 内部实现

Button 组件内部使用了以下技术实现：

1. **状态管理**：使用 React 的 `useState` 钩子管理按钮的按下状态
2. **样式应用**：使用 `useButtonTailwind` 钩子根据按钮属性生成样式类
3. **事件处理**：实现了 `onMouseDown`、`onMouseUp`、`onMouseLeave`、`onTouchStart` 和 `onTouchEnd` 事件处理函数，用于管理按钮的按下状态
4. **图标渲染**：通过条件渲染实现图标的显示

```tsx
// 样式生成示例
const buttonStyles = useButtonTailwind({
  size,
  color,
  disabled,
  shape,
  fill,
  isPressed
});

// 事件处理示例
const handlePressIn = () => {
  if (!disabled) setIsPressed(true);
};

const handlePressOut = () => {
  setIsPressed(false);
};
```
