import Feather from '@expo/vector-icons/Feather';
import React, { forwardRef, useCallback, useImperativeHandle } from 'react';
import { html } from 'react-strict-dom';
import type { StyleObject } from 'react-strict-dom-tailwind';

import { useThemeTailwind } from '../../hooks';
import { useControlledState } from '../../hooks/useControlledState';
import {
  useCheckboxBoxStyles,
  useCheckboxLabelStyles,
  useCheckboxTailwind,
} from './styles';

// 复选框尺寸
export type CheckboxSize = 'normal' | 'small';

// 复选框颜色
export type CheckboxColor =
  | 'default'
  | 'primary'
  | 'success'
  | 'danger'
  | 'warning';

// 复选框形状
export type CheckboxShape = 'square' | 'round';

// 复选框属性
export interface CheckboxProps {
  /**
   * 是否选中
   * @default false
   */
  checked?: boolean;

  /**
   * 默认是否选中（非受控模式）
   * @default false
   */
  defaultChecked?: boolean;

  /**
   * 半选状态，只会影响样式
   * @default false
   */
  indeterminate?: boolean;

  /**
   * 复选框尺寸
   * @default 'normal'
   */
  size?: CheckboxSize;

  /**
   * 复选框颜色
   * @default 'default'
   */
  color?: CheckboxColor;

  /**
   * 复选框形状
   * @default 'square'
   */
  shape?: CheckboxShape;

  /**
   * 是否禁用
   * @default false
   */
  disabled?: boolean;

  /**
   * 复选框文本内容
   */
  children?: React.ReactNode;

  /**
   * 自定义样式
   */
  style?: StyleObject;

  /**
   * 选中状态变化回调函数
   */
  onChange?: (checked: boolean) => void;

  /**
   * 复选框的值，用于Checkbox.Group
   */
  value?: string;
}

// 复选框引用接口
export interface CheckboxRef {
  /**
   * 设置选中状态
   */
  setChecked: (checked: boolean) => void;
}

/**
 * Checkbox 复选框组件
 *
 * 基于Figma设计稿实现，支持多种尺寸、类型和状态
 */
const _Checkbox = forwardRef<CheckboxRef, CheckboxProps>((props, ref) => {
  const {
    checked: controlledChecked,
    defaultChecked = false,
    indeterminate = false,
    size = 'normal',
    color = 'default',
    shape = 'square',
    disabled = false,
    children,
    style,
    onChange,
    value: _value, // 未使用的值，添加下划线前缀
  } = props;

  // 使用useControlledState处理受控和非受控状态
  const [checked, setChecked] = useControlledState(
    controlledChecked,
    defaultChecked,
    onChange
  );

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    setChecked: (newChecked: boolean) => {
      setChecked(newChecked);
    },
  }));

  // 使用主题样式
  const { tw } = useThemeTailwind();

  // 获取样式
  const checkboxStyles = useCheckboxTailwind({
    size,
    color,
    disabled,
    shape,
    checked,
    indeterminate,
  });

  const checkboxBoxStyle = useCheckboxBoxStyles({
    size,
    color,
    shape,
    checked,
    indeterminate,
    disabled,
  });

  const checkboxLabelStyle = useCheckboxLabelStyles({
    size,
    color,
  });

  // 处理点击事件
  const handleClick = useCallback(() => {
    if (!disabled) {
      setChecked(!checked);
    }
  }, [disabled, checked, setChecked]);

  // 渲染勾选图标
  const renderCheckIcon = () => {
    if (!checked && !indeterminate) return null;

    // 确定图标颜色
    const iconColor = color === 'default' && !disabled ? '#24231D' : 'white';

    if (indeterminate) {
      // 渲染半选状态图标（短横线）
      return (
        <html.div style={tw('checkbox-icon checkbox-icon-indeterminate')}>
          <html.div
            style={tw(
              color === 'default'
                ? 'w-[10px] h-[2px] bg-gray-100'
                : 'w-[10px] h-[2px] bg-white'
            )}
          />
        </html.div>
      );
    }

    // 渲染选中状态图标（勾选）- 使用Feather图标
    return (
      <html.div style={tw('checkbox-icon checkbox-icon-checked')}>
        <Feather
          name="check"
          size={size === 'normal' ? 16 : 12}
          color={iconColor}
        />
      </html.div>
    );
  };

  // 渲染复选框
  return (
    <html.div
      style={[tw(checkboxStyles), style]}
      onClick={handleClick}
      role="checkbox"
      aria-checked={checked}
      aria-disabled={disabled}
    >
      <html.div style={tw(checkboxBoxStyle)}>{renderCheckIcon()}</html.div>
      {children && (
        <html.div style={tw(checkboxLabelStyle)}>{children}</html.div>
      )}
    </html.div>
  );
});

// 设置组件名称
_Checkbox.displayName = 'Checkbox';

// 复选框组属性
export interface CheckboxGroupProps {
  /**
   * 当前选中的选项值数组
   */
  value?: string[];

  /**
   * 默认选中的选项值数组（非受控模式）
   */
  defaultValue?: string[];

  /**
   * 复选框组尺寸，会被子复选框继承
   * @default 'normal'
   */
  size?: CheckboxSize;

  /**
   * 复选框组颜色，会被子复选框继承
   * @default 'default'
   */
  color?: CheckboxColor;

  /**
   * 复选框组形状，会被子复选框继承
   * @default 'square'
   */
  shape?: CheckboxShape;

  /**
   * 是否禁用，会被子复选框继承
   * @default false
   */
  disabled?: boolean;

  /**
   * 自定义样式
   */
  style?: StyleObject;

  /**
   * 选项变化回调函数
   */
  onChange?: (value: string[]) => void;

  /**
   * 复选框组子元素
   */
  children?: React.ReactNode;

  /**
   * 可选项配置，优先级高于 children
   */
  options?: Array<{
    label: React.ReactNode;
    value: string;
    disabled?: boolean;
  }>;
}

/**
 * CheckboxGroup 复选框组组件
 */
const CheckboxGroup: React.FC<CheckboxGroupProps> = (props) => {
  const {
    value: controlledValue,
    defaultValue = [],
    size = 'normal',
    color = 'default',
    shape = 'square',
    disabled = false,
    style,
    onChange,
    children,
    options,
  } = props;

  // 使用useControlledState处理受控和非受控状态
  const [value, setValue] = useControlledState(
    controlledValue,
    defaultValue,
    onChange
  );

  // 使用主题样式
  const { tw } = useThemeTailwind();

  // 处理复选框选中状态变化
  const handleCheckboxChange = (checkboxValue: string, checked: boolean) => {
    const newValue = [...value];
    if (checked) {
      // 添加值
      if (!newValue.includes(checkboxValue)) {
        newValue.push(checkboxValue);
      }
    } else {
      // 移除值
      const index = newValue.indexOf(checkboxValue);
      if (index !== -1) {
        newValue.splice(index, 1);
      }
    }
    setValue(newValue);
  };

  // 渲染选项
  const renderOptions = () => {
    if (options) {
      return options.map((option) => (
        <_Checkbox
          key={option.value}
          value={option.value}
          checked={value.includes(option.value)}
          disabled={disabled || option.disabled}
          size={size}
          color={color}
          shape={shape}
          onChange={(checked) => handleCheckboxChange(option.value, checked)}
        >
          {option.label}
        </_Checkbox>
      ));
    }

    // 处理子元素
    return React.Children.map(children, (child) => {
      if (!React.isValidElement(child)) return child;

      // 确保子元素是Checkbox组件
      const childType = child.type as { displayName?: string };
      if (childType.displayName !== 'Checkbox') return child;

      const childValue = child.props.value;
      if (!childValue) return child;

      // 克隆子元素并注入属性
      return React.cloneElement(child, {
        checked: value.includes(childValue),
        disabled: disabled || child.props.disabled,
        size: child.props.size || size,
        color: child.props.color || color,
        shape: child.props.shape || shape,
        onChange: (checked: boolean) =>
          handleCheckboxChange(childValue, checked),
      } as React.ComponentProps<typeof _Checkbox>);
    });
  };

  // 渲染复选框组
  return (
    <html.div style={[tw('flex flex-col gap-3'), style]}>
      {renderOptions()}
    </html.div>
  );
};

// 设置组件名称
CheckboxGroup.displayName = 'CheckboxGroup';

export const Checkbox = Object.assign(_Checkbox, { Group: CheckboxGroup });
