# Checkbox 复选框组件

## 组件概述

Checkbox 是一个用于多选场景的复选框组件，基于 Figma 设计稿实现，支持多种尺寸、类型和状态，适用于各种表单和列表选择场景。组件使用 React Strict DOM 和 react-strict-dom-tailwind 实现，确保在 Web 和移动平台上具有一致的外观和行为。

## 设计规范

### 尺寸变体

复选框提供两种尺寸变体：

- **标准 (normal)**: 默认尺寸，适用于大多数场景
- **小号 (small)**: 小尺寸，适用于空间受限的场景或需要更紧凑布局的场合

### 颜色变体

复选框提供多种颜色变体：

- **默认 (default)**: 使用主题默认颜色，通常为深灰色
- **主要 (primary)**: 使用主题主色，用于强调重要性
- **成功 (success)**: 绿色，用于表示成功或确认操作
- **危险 (danger)**: 红色，用于表示危险或警告操作
- **警告 (warning)**: 橙色，用于表示警告操作

### 状态变体

复选框支持以下状态：

- **未选中 (unchecked)**: 复选框的默认未选中状态
- **选中 (checked)**: 复选框被选中的状态
- **半选中 (indeterminate)**: 复选框的半选中状态，通常用于表示子项部分选中的情况
- **禁用 (disabled)**: 复选框不可用的状态，可以与选中或未选中状态组合

### 颜色规范

- **未选中状态**:
  - 边框颜色: #D5D5D5 (colorGray880)
  - 背景色: 透明
  - 文字颜色: #24231D (colorGray100)

- **选中状态**:
  - 默认: 背景色 #24231D (colorGray100)，边框颜色 #24231D，勾选图标颜色 #FFFFFF
  - 主要: 背景色 #24231D (colorGray100)，边框颜色 #24231D，勾选图标颜色 #FFFFFF
  - 成功: 背景色 #34C759 (colorGreen500)，边框颜色 #34C759，勾选图标颜色 #FFFFFF
  - 危险: 背景色 #FF3B30 (colorRed500)，边框颜色 #FF3B30，勾选图标颜色 #FFFFFF
  - 警告: 背景色 #FF9735 (colorOrange500)，边框颜色 #FF9735，勾选图标颜色 #FFFFFF

- **半选中状态**:
  - 与选中状态相同，但内部显示一个短横线而非勾选图标

- **禁用状态**:
  - 未选中: 边框颜色 #E4E4E4，背景色 透明，文字颜色 #989898 (colorGray600)
  - 选中: 边框颜色 #989898，背景色 #989898，勾选图标颜色 #F0F0F0 (colorGray950)

### 形状规范

- **方形 (square)**: 默认形状，直角或微圆角 (2px)
- **圆形 (round)**: 圆形复选框，适用于特定设计风格

### 文字规范

- 标准尺寸: 14px，Noto Sans TC，Regular (400)
- 小号尺寸: 12px，Noto Sans TC，Regular (400)

### 间距规范

- 复选框与文字之间的间距: 8px
- 组内复选框之间的垂直间距: 12px (标准尺寸)，8px (小号尺寸)

## 组件 API

### 属性接口

```typescript
// 复选框尺寸
export type CheckboxSize = 'normal' | 'small';

// 复选框颜色
export type CheckboxColor =
  | 'default'
  | 'primary'
  | 'success'
  | 'danger'
  | 'warning';

// 复选框形状
export type CheckboxShape = 'square' | 'round';

// 复选框属性
export interface CheckboxProps {
  /**
   * 是否选中
   * @default false
   */
  checked?: boolean;

  /**
   * 默认是否选中（非受控模式）
   * @default false
   */
  defaultChecked?: boolean;

  /**
   * 半选状态，只会影响样式
   * @default false
   */
  indeterminate?: boolean;

  /**
   * 复选框尺寸
   * @default 'normal'
   */
  size?: CheckboxSize;

  /**
   * 复选框颜色
   * @default 'default'
   */
  color?: CheckboxColor;

  /**
   * 复选框形状
   * @default 'square'
   */
  shape?: CheckboxShape;

  /**
   * 是否禁用
   * @default false
   */
  disabled?: boolean;

  /**
   * 复选框文本内容
   */
  children?: React.ReactNode;

  /**
   * 自定义样式
   */
  style?: StyleObject;

  /**
   * 选中状态变化回调函数
   */
  onChange?: (checked: boolean) => void;
}

// 复选框组属性
export interface CheckboxGroupProps {
  /**
   * 当前选中的选项值数组
   */
  value?: string[];

  /**
   * 默认选中的选项值数组（非受控模式）
   */
  defaultValue?: string[];

  /**
   * 复选框组尺寸，会被子复选框继承
   * @default 'normal'
   */
  size?: CheckboxSize;

  /**
   * 复选框组颜色，会被子复选框继承
   * @default 'default'
   */
  color?: CheckboxColor;

  /**
   * 复选框组形状，会被子复选框继承
   * @default 'square'
   */
  shape?: CheckboxShape;

  /**
   * 是否禁用，会被子复选框继承
   * @default false
   */
  disabled?: boolean;

  /**
   * 自定义样式
   */
  style?: StyleObject;

  /**
   * 选项变化回调函数
   */
  onChange?: (value: string[]) => void;

  /**
   * 复选框组子元素
   */
  children?: React.ReactNode;

  /**
   * 可选项配置，优先级高于 children
   */
  options?: Array<{
    label: React.ReactNode;
    value: string;
    disabled?: boolean;
  }>;
}
```

## 使用示例

### 基本用法

```tsx
import { Checkbox } from '@dp-frontend/ui';

// 基础复选框
<Checkbox onChange={(checked) => console.log('Checked:', checked)}>
  同意用户协议
</Checkbox>

// 默认选中
<Checkbox defaultChecked>
  记住我
</Checkbox>

// 禁用状态
<Checkbox disabled>
  禁用选项
</Checkbox>

// 禁用且选中
<Checkbox disabled checked>
  禁用且选中
</Checkbox>

// 半选状态
<Checkbox indeterminate>
  半选状态
</Checkbox>
```

### 不同尺寸

```tsx
import { Checkbox } from '@dp-frontend/ui';

// 标准尺寸
<Checkbox size="normal">标准尺寸</Checkbox>

// 小号尺寸
<Checkbox size="small">小号尺寸</Checkbox>
```

### 不同颜色

```tsx
import { Checkbox } from '@dp-frontend/ui';

// 默认颜色
<Checkbox color="default" checked>默认颜色</Checkbox>

// 主要颜色
<Checkbox color="primary" checked>主要颜色</Checkbox>

// 成功颜色
<Checkbox color="success" checked>成功颜色</Checkbox>

// 危险颜色
<Checkbox color="danger" checked>危险颜色</Checkbox>

// 警告颜色
<Checkbox color="warning" checked>警告颜色</Checkbox>
```

### 不同形状

```tsx
import { Checkbox } from '@dp-frontend/ui';

// 方形
<Checkbox shape="square" checked>方形复选框</Checkbox>

// 圆形
<Checkbox shape="round" checked>圆形复选框</Checkbox>
```

### 复选框组

```tsx
import { Checkbox } from '@dp-frontend/ui';

// 基础复选框组
<Checkbox.Group
  defaultValue={['apple']}
  onChange={(value) => console.log('Selected:', value)}
>
  <Checkbox value="apple">苹果</Checkbox>
  <Checkbox value="orange">橙子</Checkbox>
  <Checkbox value="banana">香蕉</Checkbox>
</Checkbox.Group>

// 使用 options 属性
<Checkbox.Group
  options={[
    { label: '选项1', value: 'option1' },
    { label: '选项2', value: 'option2' },
    { label: '选项3', value: 'option3', disabled: true }
  ]}
  defaultValue={['option1']}
  onChange={(value) => console.log('Selected:', value)}
/>

// 禁用整个复选框组
<Checkbox.Group disabled defaultValue={['apple']}>
  <Checkbox value="apple">苹果</Checkbox>
  <Checkbox value="orange">橙子</Checkbox>
  <Checkbox value="banana">香蕉</Checkbox>
</Checkbox.Group>
```

## 实现注意事项

1. 使用 React Strict DOM 的 `html.div` 和 `html.input` 元素实现复选框
2. 使用 `useThemeTailwind` 钩子提供的 `tw()` 函数应用 Tailwind 样式
3. 使用 `useCheckboxTailwind` 钩子根据复选框的尺寸、类型和状态动态生成样式类
4. 支持受控和非受控两种使用模式
5. 确保复选框在禁用状态下不触发 onChange 事件
6. 实现半选中状态的样式和功能
7. 确保复选框组能正确管理子复选框的状态
8. 确保复选框在不同平台上具有一致的外观和行为
9. 使用 SVG 图标实现勾选和半选状态的视觉效果
10. 确保复选框具有良好的可访问性，支持键盘操作和屏幕阅读器
