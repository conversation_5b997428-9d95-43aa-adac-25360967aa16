import clsx from 'clsx';
import { useMemo } from 'react';
import { css } from 'react-strict-dom';
import type { StyleObject } from 'react-strict-dom-tailwind';
import { tw } from 'react-strict-dom-tailwind';

import { colors, semanticColors } from '../../theme/vars.stylex';
import type { CheckboxProps } from '.';

/**
 * Checkbox组件的样式钩子
 * 根据组件的属性和状态动态生成样式类
 */
export const useCheckboxTailwind = (
  props: Pick<
    CheckboxProps,
    'size' | 'color' | 'disabled' | 'shape' | 'checked' | 'indeterminate'
  >
) => {
  const {
    size = 'normal',
    color = 'default',
    disabled = false,
    shape = 'square',
    checked = false,
    indeterminate = false,
  } = props;

  const checkboxStyles = useMemo(
    () =>
      clsx({
        'checkbox-container': true,
        'checkbox-disabled': disabled,

        // 尺寸变体
        'checkbox-normal': size === 'normal',
        'checkbox-small': size === 'small',

        // 形状变体
        'checkbox-square': shape === 'square',
        'checkbox-round': shape === 'round',
      }),
    [size, color, disabled, shape, checked, indeterminate]
  );

  return checkboxStyles;
};

/**
 * Checkbox盒子样式钩子
 * 根据组件的属性和状态动态生成盒子样式类
 */
export const useCheckboxBoxStyles = (
  props: Pick<
    CheckboxProps,
    'size' | 'color' | 'shape' | 'checked' | 'indeterminate' | 'disabled'
  >
) => {
  const {
    size = 'normal',
    color = 'default',
    shape = 'square',
    checked = false,
    indeterminate = false,
    disabled = false,
  } = props;

  return useMemo(
    () =>
      clsx({
        // 基础样式
        'checkbox-box': true,

        // 尺寸样式
        'checkbox-normal-box': size === 'normal',
        'checkbox-small-box': size === 'small',

        // 形状样式
        'checkbox-square': shape === 'square',
        'checkbox-round': shape === 'round',

        // 颜色变体 - 未选中状态使用默认样式
        'checkbox-box-default':
          color === 'default' && !checked && !indeterminate,
        'checkbox-box-default-checked':
          color === 'default' && (checked || indeterminate),
        'checkbox-box-primary':
          color === 'primary' && (checked || indeterminate),
        'checkbox-box-success':
          color === 'success' && (checked || indeterminate),
        'checkbox-box-danger': color === 'danger' && (checked || indeterminate),
        'checkbox-box-warning':
          color === 'warning' && (checked || indeterminate),

        // 禁用状态
        'checkbox-box-default-disabled': disabled && !checked && !indeterminate,
        'checkbox-box-checked-disabled': disabled && (checked || indeterminate),

        // 状态变体
        'checkbox-checked': checked && !indeterminate,
        'checkbox-unchecked': !checked && !indeterminate,
        'checkbox-indeterminate': indeterminate,
      }),
    [size, shape, color, checked, indeterminate, disabled]
  );
};

/**
 * Checkbox标签样式钩子
 * 根据组件的属性动态生成标签样式类
 */
export const useCheckboxLabelStyles = (
  props: Pick<CheckboxProps, 'size' | 'color'>
) => {
  const { size = 'normal', color = 'default' } = props;

  return useMemo(
    () =>
      clsx({
        // 基础样式
        'checkbox-label': true,

        // 尺寸样式
        'checkbox-small-label': size === 'small',

        // 颜色样式
        'checkbox-text-default': color === 'default',
        'checkbox-text-primary': color === 'primary',
        'checkbox-text-success': color === 'success',
        'checkbox-text-danger': color === 'danger',
        'checkbox-text-warning': color === 'warning',
      }),
    [size, color]
  );
};

/**
 * Checkbox组件的样式定义
 */
export const checkboxTheme: StyleObject = {
  // 容器样式
  'checkbox-container': tw('flex flex-row items-center'),

  // 尺寸变体
  'checkbox-normal': tw('gap-2'),
  'checkbox-small': tw('gap-2'),

  // 复选框样式
  'checkbox-box': tw('flex-cc relative border-solid'),
  'checkbox-normal-box': tw('w-[20px] h-[20px]'),
  'checkbox-small-box': tw('w-[16px] h-[16px]'),

  // 形状变体
  'checkbox-square': tw('rounded-[2px]'),
  'checkbox-round': tw('rounded-full'),

  // 标签样式
  'checkbox-label': tw('text-[14px]'),
  'checkbox-small-label': tw('text-[12px]'),

  // 文字颜色样式
  'checkbox-text-default': tw('text-gray-800'),
  'checkbox-text-primary': tw('text-primary'),
  'checkbox-text-success': tw('text-success'),
  'checkbox-text-danger': tw('text-danger'),
  'checkbox-text-warning': tw('text-warning'),

  // 禁用样式
  'checkbox-disabled': tw('cursor-not-allowed opacity-50'),

  // 颜色样式
  ...css.create({
    // 未选中状态
    'checkbox-box-default': {
      borderColor: semanticColors.defaultBorder, // #D5D5D5
      backgroundColor: 'transparent',
      borderWidth: 1,
    },
    'checkbox-box-default-disabled': {
      borderColor: colors.colorGray940, // #E4E4E4
      backgroundColor: 'transparent',
      borderWidth: 1,
    },

    // 选中状态 - 默认
    'checkbox-box-default-checked': {
      borderColor: semanticColors.defaultBorder, // #D5D5D5
      backgroundColor: semanticColors.default, // #FFFFFF
      borderWidth: 1,
    },
    // 选中状态 - 主要
    'checkbox-box-primary': {
      borderColor: semanticColors.primary, // #24231D
      backgroundColor: semanticColors.primary, // #24231D
      borderWidth: 1,
    },
    'checkbox-box-checked-disabled': {
      borderColor: colors.colorGray600, // #989898
      backgroundColor: colors.colorGray600, // #989898
      borderWidth: 1,
    },

    // 选中状态 - 成功
    'checkbox-box-success': {
      borderColor: semanticColors.success, // #34C759
      backgroundColor: semanticColors.success, // #34C759
      borderWidth: 1,
    },

    // 选中状态 - 危险
    'checkbox-box-danger': {
      borderColor: semanticColors.danger, // #FF3B30
      backgroundColor: semanticColors.danger, // #FF3B30
      borderWidth: 1,
    },

    // 选中状态 - 警告
    'checkbox-box-warning': {
      borderColor: semanticColors.warning, // #FF9735
      backgroundColor: semanticColors.warning, // #FF9735
      borderWidth: 1,
    },
  }),

  // 图标样式
  'checkbox-icon': tw('absolute'),
  'checkbox-icon-checked': tw('flex-cc'),
  'checkbox-icon-indeterminate': tw('flex-cc'),

  // 选中和未选中状态样式
  'checkbox-checked': tw('border border-solid flex-cc'),
  'checkbox-unchecked': tw('border border-solid bg-transparent'),
  'checkbox-indeterminate': tw('border border-solid flex-cc'),
};

export default checkboxTheme;
