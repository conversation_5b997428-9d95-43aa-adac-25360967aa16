import { useCallback, useState } from 'react';

import type { ModalProps } from '../components/Modal';

export interface UseModalTypes {
  visible: boolean;
  open: () => void;
  close: () => void;
  toggle: () => void;
  modalProps: ModalProps;
}

/**
 * Modal钩子，用于管理Modal组件的状态和属性
 *
 * @param defaultProps Modal组件的默认属性
 * @returns 包含Modal状态和控制方法的对象
 *
 * @example
 * ```tsx
 * const modal = useModal({ title: '提示' });
 *
 * // 打开Modal
 * modal.open();
 *
 * // 关闭Modal
 * modal.close();
 *
 * // 在组件中使用
 * <Modal modal={modal}>内容</Modal>
 * ```
 */
export function useModal(
  defaultProps?: Omit<ModalProps, 'visible' | 'children'>
): UseModalTypes {
  // 管理Modal的可见状态
  const [visible, setVisible] = useState(false);

  // 打开Modal的方法
  const open = useCallback(() => {
    setVisible(true);
  }, []);

  // 关闭Modal的方法
  const close = useCallback(() => {
    setVisible(false);
  }, []);

  // 切换Modal可见状态的方法
  const toggle = useCallback(() => {
    setVisible((prev) => !prev);
  }, []);

  // 构建Modal的属性
  const modalProps = {
    ...defaultProps,
    visible,
    onClose: () => {
      close();
      defaultProps?.onClose?.();
    },
  } as ModalProps;

  return {
    visible,
    open,
    close,
    toggle,
    modalProps,
  };
}
