import { useCallback, useEffect, useRef, useState } from 'react';

/**
 * 同时支持受控和非受控组件的状态管理Hook
 *
 * @param controlledValue 受控值
 * @param defaultValue 默认值（非受控模式）
 * @returns [value, setValue] 当前值和更新函数
 */
export function useControlledState<T>(
  controlledValue: T | undefined,
  defaultValue: T | undefined,
  onChange?: (value: T) => void
): [T, (value: T) => void] {
  // 是否为受控模式
  const isControlled = controlledValue !== undefined;

  // 保存是否为受控模式的引用，用于检测模式变化
  const isControlledRef = useRef(isControlled);

  // 内部状态，仅在非受控模式下使用
  const [uncontrolledValue, setUncontrolledValue] = useState<T | undefined>(
    defaultValue
  );

  // 当前值，根据模式选择受控值或非受控值
  const value = isControlled ? controlledValue : uncontrolledValue;

  // 检测模式变化
  useEffect(() => {
    const wasControlled = isControlledRef.current;
    const nowControlled = controlledValue !== undefined;

    // 更新引用
    isControlledRef.current = nowControlled;

    // 检测模式变化并发出警告
    if (wasControlled !== nowControlled) {
      console.warn(
        `组件从${wasControlled ? '受控' : '非受控'}模式变为${
          nowControlled ? '受控' : '非受控'
        }模式。这通常是由于value属性从undefined变为定义值或反之导致的，这可能会导致意外的行为。`
      );

      // 如果从受控变为非受控，使用最后的受控值作为非受控值
      if (wasControlled && !nowControlled) {
        setUncontrolledValue(controlledValue);
      }
    }
  }, [controlledValue]);

  // 更新函数
  const setValue = useCallback(
    (nextValue: T) => {
      // 非受控模式下更新内部状态
      if (!isControlledRef.current) {
        setUncontrolledValue(nextValue);
      }

      // 调用外部onChange回调
      if (onChange) {
        onChange(nextValue);
      }
    },
    [onChange]
  );

  return [value as T, setValue];
}
