import { useCallback } from 'react';
import type { TailwindOptions } from 'react-strict-dom-tailwind';
import { tw as tailwind } from 'react-strict-dom-tailwind';

import { useConfigProvider } from '../components';

export const useThemeTailwind = () => {
  const { theme } = useConfigProvider();
  const tw = useCallback(
    (classNames: string, options?: TailwindOptions) =>
      tailwind(classNames, { ...options, extraStyles: theme }),
    [theme]
  );
  return { tw, theme };
};
