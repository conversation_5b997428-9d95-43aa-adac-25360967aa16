import { type StyleObject } from 'react-strict-dom-tailwind';

import { buttonTheme } from '../components/Button/styles';
import { checkboxTheme } from '../components/Checkbox/styles';
import { confirmCodeFieldTheme } from '../components/ConfirmCodeField/styles';
import { inputTheme } from '../components/Input/styles';
import { modalTheme } from '../components/Modal/styles';
import { radioTheme } from '../components/Radio/styles';
import { ToastTheme } from '../components/Toast/styles';

export const themes: StyleObject = {
  ...buttonTheme,
  ...checkboxTheme,
  ...inputTheme,
  ...modalTheme,
  ...confirmCodeFieldTheme,
  ...radioTheme,
  ...ToastTheme,
};
