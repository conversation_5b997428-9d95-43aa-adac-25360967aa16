import { css } from 'react-strict-dom';

// 基础色板 - 一级颜色
export const colors = css.defineVars({
  // 基础颜色
  colorWhite: '#FFFFFF',
  colorBlack: '#000000',

  // 灰度颜色 - 0为黑色，1000为白色，以50为分隔点
  // 深色系 (0-450)
  colorGray0: '#000000', // 黑色
  colorGray50: '#151514', // 接近黑色
  colorGray100: '#24231D', // 深灰色 - 主要文本色
  colorGray150: '#272636', // 深灰色变体
  colorGray200: '#3D3D3D',
  colorGray250: '#444444',
  colorGray300: '#5B5B5B',
  colorGray350: '#626262',
  colorGray400: '#707070',
  colorGray450: '#747474',
  // 中性色 (500)
  colorGray500: '#808080', // 中灰色
  // 浅色系 (550-1000)
  colorGray550: '#8A8A8A',
  colorGray600: '#989898', // 禁用文本色
  colorGray650: '#9C9C9C',
  colorGray700: '#A8A8A8',
  colorGray750: '#B2B2B2',
  colorGray800: '#B8B8B8',
  colorGray850: '#BABABA',
  colorGray880: '#D5D5D5', // 浅灰色边框
  colorGray900: '#DCDCDC', // 浅灰色 - 边框色
  colorGray920: '#DEDEDE', // 分割线色
  colorGray940: '#E4E4E4', // 次要禁用色
  colorGray950: '#F0F0F0', // 背景色
  colorGray970: '#F5F5F5', // 浅背景色
  colorGray980: '#F9F9F9', // 最浅背景色
  colorGray1000: '#FFFFFF', // 白色

  // 扩展色板 - 红色系 (100-1000)
  colorRed100: '#FFECE8',
  colorRed200: '#FDCDC5',
  colorRed300: '#FBACA3',
  colorRed400: '#F98981',
  colorRed500: '#F76560',
  colorRed600: '#F53F3F',
  colorRed700: '#CB272D',
  colorRed800: '#A1151E',
  colorRed900: '#770813',
  colorRed1000: '#4D000A',

  // 扩展色板 - 橙红色系 (100-1000)
  colorOrangered100: '#FFF3E8',
  colorOrangered200: '#FDDDC3',
  colorOrangered300: '#FCC59F',
  colorOrangered400: '#FAAC7B',
  colorOrangered500: '#F99057',
  colorOrangered600: '#F77234',
  colorOrangered700: '#CC5120',
  colorOrangered800: '#A23511',
  colorOrangered900: '#771F06',
  colorOrangered1000: '#4D0E00',

  // 扩展色板 - 橙色系 (100-1000)
  colorOrange100: '#FFF7E8',
  colorOrange200: '#FFE4BA',
  colorOrange300: '#FFCF8B',
  colorOrange400: '#FFB65D',
  colorOrange500: '#FF9A2E',
  colorOrange600: '#FF7D00',
  colorOrange700: '#D25F00',
  colorOrange800: '#A64500',
  colorOrange900: '#792E00',
  colorOrange1000: '#4D1B00',

  // 扩展色板 - 金色系 (100-1000)
  colorGold100: '#FFFCE8',
  colorGold200: '#FDF4BF',
  colorGold300: '#FCE996',
  colorGold400: '#FADC6D',
  colorGold500: '#F9CC45',
  colorGold600: '#F7BA1E',
  colorGold700: '#CC9213',
  colorGold800: '#A26D0A',
  colorGold900: '#774B04',
  colorGold1000: '#4D2D00',

  // 扩展色板 - 黄色系 (100-1000)
  colorYellow100: '#FEFFE8',
  colorYellow200: '#FEFEBE',
  colorYellow300: '#FDFA94',
  colorYellow400: '#FCF26B',
  colorYellow500: '#FBE842',
  colorYellow600: '#FADC19',
  colorYellow700: '#CFAF0F',
  colorYellow800: '#A38408',
  colorYellow900: '#785D03',
  colorYellow1000: '#4D3800',

  // 扩展色板 - 酸橙色系 (100-1000)
  colorLime100: '#FCFFE8',
  colorLime200: '#EDF8BB',
  colorLime300: '#DCF190',
  colorLime400: '#C9E968',
  colorLime500: '#B5E241',
  colorLime600: '#9FDB1D',
  colorLime700: '#7EB712',
  colorLime800: '#5F940A',
  colorLime900: '#437004',
  colorLime1000: '#2A4D00',

  // 扩展色板 - 绿色系 (100-1000)
  colorGreen100: '#E8FFEA',
  colorGreen200: '#AFF0B5',
  colorGreen300: '#7BE188',
  colorGreen400: '#4CD263',
  colorGreen500: '#23C343',
  colorGreen600: '#00B42A',
  colorGreen700: '#009A29',
  colorGreen800: '#008026',
  colorGreen900: '#006622',
  colorGreen1000: '#004D1C',

  // 扩展色板 - 青色系 (100-1000)
  colorCyan100: '#E8FFFB',
  colorCyan200: '#B7F4EC',
  colorCyan300: '#89E9E0',
  colorCyan400: '#5EDFD6',
  colorCyan500: '#37D4CF',
  colorCyan600: '#14C9C9',
  colorCyan700: '#0DA5AA',
  colorCyan800: '#07828B',
  colorCyan900: '#03616C',
  colorCyan1000: '#00424D',

  // 扩展色板 - 蓝色系 (100-1000)
  colorBlue100: '#E8F7FF',
  colorBlue200: '#C3E7FE',
  colorBlue300: '#9FD4FD',
  colorBlue400: '#7BC0FC',
  colorBlue500: '#579FB',
  colorBlue600: '#3491F',
  colorBlue700: '#206CCF',
  colorBlue800: '#114B3',
  colorBlue900: '#063078',
  colorBlue1000: '#0014D',

  // 扩展色板 - Arco蓝色系 (100-1000)
  colorArcoblue100: '#E8F3FF',
  colorArcoblue200: '#BEDAFF',
  colorArcoblue300: '#94BFFF',
  colorArcoblue400: '#6AA1FF',
  colorArcoblue500: '#4080FF',
  colorArcoblue600: '#165DFF',
  colorArcoblue700: '#0E42D2',
  colorArcoblue800: '#072CA6',
  colorArcoblue900: '#031A79',
  colorArcoblue1000: '#000D4D',

  // 扩展色板 - 紫色系 (100-1000)
  colorPurple100: '#F5E8FF',
  colorPurple200: '#DDBEF6',
  colorPurple300: '#C396ED',
  colorPurple400: '#A871E3',
  colorPurple500: '#8D4EDA',
  colorPurple600: '#722ED1',
  colorPurple700: '#551DB0',
  colorPurple800: '#3C108F',
  colorPurple900: '#27066E',
  colorPurple1000: '#16004D',

  // 扩展色板 - 粉紫色系 (100-1000)
  colorPinkpurple100: '#FFE8FB',
  colorPinkpurple200: '#F7BAEF',
  colorPinkpurple300: '#F08EE6',
  colorPinkpurple400: '#E865DF',
  colorPinkpurple500: '#E13EDB',
  colorPinkpurple600: '#D91AD9',
  colorPinkpurple700: '#B010B6',
  colorPinkpurple800: '#8A0993',
  colorPinkpurple900: '#650370',
  colorPinkpurple1000: '#42004D',

  // 扩展色板 - 品红色系 (100-1000)
  colorMagenta100: '#FFE8F1',
  colorMagenta200: '#FDC2DB',
  colorMagenta300: '#FB9DC7',
  colorMagenta400: '#F979B7',
  colorMagenta500: '#F754A8',
  colorMagenta600: '#F5319D',
  colorMagenta700: '#CB1E83',
  colorMagenta800: '#A11069',
  colorMagenta900: '#77064F',
  colorMagenta1000: '#4D0034',
});

// 语义化颜色 - 二级颜色
export const semanticColors = css.defineVars({
  // 默认颜色
  default: colors.colorWhite, // #FFFFFFF
  defaultText: colors.colorGray100, // #24231D
  defaultBorder: colors.colorGray880, // #D5D5D5
  defaultDisabled: colors.colorGray940, // #E4E4E4
  defaultTextDisabled: colors.colorGray100, // #24231D
  defaultBorderDisabled: colors.colorGray950, // #DCDCDC

  // 主要颜色
  primary: colors.colorGray100, // #24231D
  primaryText: colors.colorWhite,
  primaryDisabled: colors.colorGray600, // #989898
  primaryTextDisabled: colors.colorGray950, // #F0F0F0

  // 功能颜色
  danger: colors.colorRed500, // #FF3B30
  dangerText: colors.colorWhite,
  dangerDisabled: colors.colorRed300,
  dangerTextDisabled: colors.colorWhite,
  success: colors.colorGreen500, // #56961B
  successText: colors.colorWhite,
  successDisabled: colors.colorGreen300,
  successTextDisabled: colors.colorWhite,
  warning: colors.colorOrange500, // #F4A125
  warningText: colors.colorWhite,
  warningDisabled: colors.colorOrange300,
  warningTextDisabled: colors.colorWhite,
});
