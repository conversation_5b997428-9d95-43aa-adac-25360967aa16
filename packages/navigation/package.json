{"name": "@dp-frontend/navigation", "version": "0.1.0", "main": "./src/index.ts", "types": "./src/index.ts", "private": true, "scripts": {"lint": "eslint ."}, "dependencies": {"@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/native": "^7.1.6", "@react-navigation/native-stack": "^7.3.10", "@react-navigation/stack": "^7.2.10", "react-native-safe-area-context": "^4.12.0", "react-native-screens": "^4.4.0"}, "peerDependencies": {"react": ">=18.0.0", "react-dom": ">=18.0.0", "react-native": ">=0.70.0", "react-native-safe-area-context": ">=4.0.0", "react-native-screens": ">=4.0.0"}, "devDependencies": {"react": "^18.3.1", "react-dom": "^18.3.1", "react-native": "0.76.9"}}