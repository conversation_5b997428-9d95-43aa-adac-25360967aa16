import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import type { NavigationContainerRef } from '@react-navigation/native';
import type { LinkingOptions } from '@react-navigation/native';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import React from 'react';

// 导入类型和工具函数
import type { PageConfig, RootStackParamList } from './types';
import {
  getInitialRouteName,
  getLinkingConfig,
  getStackNavigatorOptions,
  getTabNavigatorOptions,
} from './utils';

// 创建堆栈导航器
const Stack = createNativeStackNavigator<RootStackParamList>();
const Tab = createBottomTabNavigator<RootStackParamList>();

// 主标签导航组件
interface MainTabsProps {
  pages: PageConfig[];
}

export const MainTabs: React.FC<MainTabsProps> = ({ pages }) => {
  // 获取Main页面的子页面（标签页）
  const mainPage = pages.find((page) => page.name === 'Main');
  const tabPages = mainPage?.children || [];

  return (
    <Tab.Navigator screenOptions={getTabNavigatorOptions()}>
      {tabPages.map((page: PageConfig) => (
        <Tab.Screen
          key={page.name}
          name={page.name}
          component={page.component}
          options={page.options}
        />
      ))}
    </Tab.Navigator>
  );
};

// 主导航容器组件
interface NavigationProps {
  pages: PageConfig[];
}

const Navigation: React.FC<NavigationProps> = ({ pages }) => {
  // 配置深层链接
  const linking: Partial<LinkingOptions<RootStackParamList>> = {
    config: getLinkingConfig(pages),
  };

  // 监听导航状态变化
  const navigationRef =
    React.useRef<NavigationContainerRef<RootStackParamList>>(null);
  const routeNameRef = React.useRef<string | undefined>();

  return (
    <NavigationContainer
      ref={navigationRef}
      linking={linking as LinkingOptions<RootStackParamList>}
      onReady={() => {
        const currentRoute = navigationRef.current?.getCurrentRoute();
        if (currentRoute) {
          // 使用状态来跟踪路由名称
          const currentRouteName = currentRoute.name;
          console.log('Initial Route:', currentRouteName);
          console.log('Initial URL:', window.location.href);
          // 存储初始路由名称
          routeNameRef.current = currentRouteName;
        }
      }}
      onStateChange={() => {
        const previousRouteName = routeNameRef.current;
        const currentRoute = navigationRef.current?.getCurrentRoute();

        if (currentRoute) {
          // 保存当前路由名称
          const currentRouteName = currentRoute.name;
          console.log('Current Route:', currentRouteName);

          // 如果路由发生变化，记录
          if (previousRouteName !== currentRouteName) {
            // 可以在这里添加分析跟踪代码
            console.log(
              'Route changed:',
              previousRouteName,
              '->',
              currentRouteName
            );
          }

          // 更新引用
          routeNameRef.current = currentRouteName;
        }
      }}
    >
      <Stack.Navigator
        initialRouteName={getInitialRouteName(pages)}
        screenOptions={getStackNavigatorOptions()}
      >
        <Stack.Screen
          name="Main"
          component={() => <MainTabs pages={pages} />}
          options={{ headerShown: false }}
        />
        {pages
          .filter((page) => page.name !== 'Main')
          .map((page: PageConfig) => (
            <Stack.Screen
              key={page.name}
              name={page.name}
              component={page.component}
              options={page.options}
            />
          ))}
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default Navigation;
