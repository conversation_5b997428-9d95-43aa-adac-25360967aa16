import type { RouteProp } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import type React from 'react';

// 定义导航参数类型
export type RootStackParamList = {
  [key: string]: undefined | object;
};

// 定义页面类型
export type PageType = 'stack' | 'tab';

// 定义页面配置接口
export interface PageConfig {
  name: keyof RootStackParamList;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  component: React.ComponentType<any>;
  type: PageType;
  options: {
    title: string;
    headerShown?: boolean;
    headerTitleAlign?: 'left' | 'center';
    tabBarLabel?: string;
    path?: string;
    // 可以添加更多选项
  };
  isInitial?: boolean;
  children?: PageConfig[];
}

// 定义页面导航属性类型
export type PageNavigationProp<T extends keyof RootStackParamList> =
  StackNavigationProp<RootStackParamList, T>;

// 定义页面路由属性类型
export type PageRouteProp<T extends keyof RootStackParamList> = RouteProp<
  RootStackParamList,
  T
>;

// 定义页面属性接口
export interface PageProps<T extends keyof RootStackParamList> {
  navigation: PageNavigationProp<T>;
  route: PageRouteProp<T>;
}
