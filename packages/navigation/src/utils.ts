import type { PageConfig, RootStackParamList } from './types';

// 获取初始路由名称
export const getInitialRouteName = (
  pages: PageConfig[] = []
): keyof RootStackParamList => {
  const initialPage = pages.find((page) => page.isInitial);
  if (initialPage) {
    return initialPage.name;
  }
  return 'Main';
};

// 获取深层链接配置
export const getLinkingConfig = (pages: PageConfig[] = []) => {
  // 定义屏幕配置类型
  type ScreenConfig =
    | string
    | { path: string; screens: Record<string, string> };
  const screens: Record<string, ScreenConfig> = {};

  // 处理所有页面
  pages.forEach((page) => {
    if (page.children && page.children.length > 0) {
      // 如果有子页面，创建嵌套配置
      const childScreens: Record<string, string> = {};
      page.children.forEach((child) => {
        childScreens[child.name] =
          child.options.path || child.name.toLowerCase();
      });

      screens[page.name] = {
        path: page.options.path || page.name.toLowerCase(),
        screens: childScreens,
      };
    } else {
      // 没有子页面，直接添加
      screens[page.name] = page.options.path || page.name.toLowerCase();
    }
  });

  return {
    screens,
  };
};

// 获取标签导航器选项
export const getTabNavigatorOptions = () => {
  return {
    tabBarActiveTintColor: '#007AFF',
    tabBarInactiveTintColor: '#8E8E93',
    tabBarStyle: {
      backgroundColor: '#FFFFFF',
      borderTopWidth: 1,
      borderTopColor: '#EEEEEE',
      height: 60,
      paddingBottom: 5,
      paddingTop: 5,
    },
    headerShown: true,
    headerStyle: {
      backgroundColor: '#FFFFFF',
    },
    headerTitleStyle: {
      fontWeight: 'bold' as const,
    },
    headerTintColor: '#000000',
    tabBarHideOnKeyboard: true,
  };
};

// 获取堆栈导航器选项
export const getStackNavigatorOptions = () => {
  return {
    headerStyle: {
      backgroundColor: '#FFFFFF',
    },
    headerTintColor: '#000000',
    headerTitleStyle: {
      fontWeight: 'bold' as const,
    },
  };
};
