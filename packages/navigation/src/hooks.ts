import {
  useNavigation as useReactNavigation,
  useRoute,
} from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';

import type { RootStackParamList } from './types';

/**
 * 自定义导航hook，提供类型安全的导航对象
 * 可以在任何组件中使用，无需通过props传递
 *
 * @returns 导航对象，可用于页面跳转等操作
 */
export const useNavigation = () => {
  return useReactNavigation<StackNavigationProp<RootStackParamList>>();
};

/**
 * 自定义hook，从URL获取参数
 * 适用于Web平台，从URL查询字符串中提取参数
 *
 * @returns 从URL查询字符串中提取的参数对象
 */
export const useUrlParams = <
  T extends Record<string, string | undefined>,
>(): T => {
  // 仅在客户端执行
  if (typeof window === 'undefined') {
    return {} as T;
  }

  const searchParams = new URLSearchParams(window.location.search);
  const params: Record<string, string> = {};

  // 将查询参数转换为对象
  for (const [key, value] of searchParams.entries()) {
    params[key] = value;
  }

  return params as T;
};

/**
 * 自定义hook，获取路由参数，优先从React Navigation获取，
 * 如果不存在则尝试从URL查询字符串获取
 *
 * @param ParamType 参数类型，由调用者提供
 * @returns 合并后的路由参数
 */
export const useRouteParams = <
  ParamType = Record<string, unknown>,
>(): ParamType => {
  const route = useRoute();
  const urlParams = useUrlParams<Record<string, string>>();

  // 合并路由参数和URL参数，路由参数优先级更高
  const params = route.params || {};
  return {
    ...urlParams,
    ...params,
  } as unknown as ParamType;
};
