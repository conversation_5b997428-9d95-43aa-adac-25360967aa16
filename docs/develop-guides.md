# React Strict DOM 开发指南

## 1. 简介

React Strict DOM (RSD) 是一个用于构建跨平台应用的库，它提供了一致的 API 来创建 Web 和移动应用。本项目使用 React Strict DOM 与 Tailwind CSS 集成，通过 `react-strict-dom-tailwind` 包提供统一的样式解决方案。

### 主要特点

- **跨平台一致性**：使用相同的代码库在 Web 和移动平台上构建应用
- **HTML 标签支持**：使用 `html.*` 命名空间提供 HTML 标签的跨平台实现
- **CSS 样式支持**：通过 `css` API 提供类似 CSS 的样式定义
- **Tailwind 集成**：通过 `tw()` 函数使用 Tailwind 类名定义样式

## 2. 项目设置

### 项目结构

本项目使用 pnpm workspace 结构，采用 monorepo 方式管理多个应用和共享包：

```
dp-frontend/
├── apps/                  # 应用目录
│   ├── dp-client/         # 用户端应用
│   ├── dp-bussiness/      # 商家端应用
│   └── dp-admin/          # 运营端应用
├── packages/              # 共享包目录
│   ├── request/           # 共享请求模块
│   ├── navigation/        # 共享导航模块
│   └── ui/                # 共享UI组件
├── pnpm-workspace.yaml    # workspace配置
└── package.json           # 根项目配置
```

### 依赖项

本项目使用以下主要依赖：

```json
{
  "dependencies": {
    "expo": "~52.0.46",
    "react": "^18.3.1",
    "react-dom": "^18.3.1",
    "react-native": "0.76.9",
    "react-strict-dom": "^0.0.34",
    "react-strict-dom-tailwind": "0.2.1-beta.0"
  }
}
```

## 3. 文件结构和命名约定

### 平台特定文件

React Strict DOM 使用文件命名约定来区分平台：

- `*.web.tsx` - 仅在 Web 平台使用的代码
- `*.native.tsx` - 仅在原生平台使用的代码
- `*.tsx` - 在两个平台共享的代码

### 应用结构

每个应用（dp-client、dp-bussiness、dp-admin）的结构如下：

```
apps/dp-client/
├── src/
│   ├── components/       # 应用特定组件
│   ├── configs/          # 应用配置
│   │   └── page-configs.tsx  # 页面配置
│   ├── pages/            # 应用页面
│   ├── styles/           # 应用样式
│   ├── templates/        # 模板文件
│   ├── utils/            # 工具函数
│   ├── App.tsx           # 应用入口组件
│   ├── index.js          # 应用注册
│   └── stylex.css        # StyleX 声明
├── package.json          # 应用依赖
└── tsconfig.json         # TypeScript配置
```

### 共享包结构

```
packages/
├── request/              # 共享HTTP请求模块
│   ├── src/
│   │   └── request.ts    # HttpRequest类实现
│   └── package.json
├── navigation/           # 共享导航模块
│   ├── src/
│   │   ├── Navigation.tsx  # 导航组件
│   │   └── hooks.ts        # 导航钩子
│   └── package.json
└── ui/                   # 共享UI组件
    ├── src/
    │   ├── Button.tsx    # 按钮组件
    │   └── index.ts      # 导出所有组件
    └── package.json
```

## 4. 使用 React Strict DOM 与 Tailwind

### 基本用法

```tsx
import React from 'react';
import { html } from 'react-strict-dom';
import { tw } from 'react-strict-dom-tailwind';

const MyComponent = () => {
  return (
    <html.div style={tw('flex-1 bg-gray-50 flex flex-col pt-5')}>
      <html.p style={tw('text-base text-center mb-2.5 text-gray-800')}>
        Hello, React Strict DOM with Tailwind!
      </html.p>
    </html.div>
  );
};
```

### 组合样式

可以使用数组组合多个样式：

```tsx
<html.div style={[
  tw('flex-1 bg-gray-50'),
  { customProp: 'value' }
]}>
  Content
</html.div>
```

### 预定义样式

可以在 `src/styles/common.ts` 中定义常用样式：

```tsx
import { tw } from 'react-strict-dom-tailwind';

export const commonTailwind = {
  container: tw('flex-1 p-5 flex flex-col items-center'),
  title: tw('text-2xl font-bold mb-5 text-center'),
  text: tw('text-base text-center mb-2.5 text-gray-800'),
};
```

## 5. 组件开发指南

### 组件结构

组件应遵循以下结构：

```tsx
import React from 'react';
import { html } from 'react-strict-dom';
import { tw } from 'react-strict-dom-tailwind';

// 定义组件属性
interface MyComponentProps {
  title: string;
  onPress: () => void;
}

/**
 * 组件描述
 */
const MyComponent: React.FC<MyComponentProps> = ({ title, onPress }) => {
  // 使用Tailwind样式
  const containerStyle = tw('flex-1 bg-gray-50');
  const titleStyle = tw('text-2xl font-bold');

  return (
    <html.div style={containerStyle}>
      <html.h1 style={titleStyle}>{title}</html.h1>
      <html.button style={tw('bg-blue-500 p-3 rounded-lg')} onClick={onPress}>
        Click me
      </html.button>
    </html.div>
  );
};

export default MyComponent;
```

### 按钮组件示例

```tsx
import React from 'react';
import { html } from 'react-strict-dom';
import { tw } from 'react-strict-dom-tailwind';

export type ButtonType = 'primary' | 'secondary';

export interface ButtonProps {
  title: string;
  onPress: () => void;
  type?: ButtonType;
}

const Button: React.FC<ButtonProps> = ({
  title,
  onPress,
  type = 'primary',
}) => {
  // 根据类型选择样式
  const buttonStyle = type === 'primary'
    ? tw('bg-blue-500 p-3 rounded-lg mt-5 min-w-[120px] text-center')
    : tw('bg-gray-500 p-3 rounded-lg mt-3 min-w-[120px] text-center');

  const textStyle = tw('text-white text-base font-bold text-center');

  return (
    <html.button style={buttonStyle} onClick={onPress}>
      <html.span style={textStyle}>{title}</html.span>
    </html.button>
  );
};

export default Button;
```

## 6. 页面开发

### 页面结构

使用导航钩子代替传递导航参数：

```tsx
import React from 'react';
import { html } from 'react-strict-dom';
import { tw } from 'react-strict-dom-tailwind';
import { useNavigation } from '@dp-frontend/navigation';

import Button from '../components/Button';

const Home = () => {
  // 使用共享导航钩子获取导航对象
  const navigation = useNavigation();

  return (
    <html.div style={tw('flex-1 bg-gray-50 flex flex-col pt-5')}>
      <html.p style={tw('text-base text-center mb-2.5 text-gray-800')}>
        欢迎来到我们的应用！
      </html.p>

      <html.div style={tw('flex flex-col items-center mt-5')}>
        <Button
          title="前往关于页面"
          onPress={() => navigation.navigate('About')}
          type="primary"
        />
      </html.div>
    </html.div>
  );
};

export default Home;
```

### 页面配置

在 `apps/dp-client/src/configs/page-configs.tsx` 中配置页面：

```tsx
export const pages: PageConfig[] = [
  {
    name: 'Main',
    component: Home,
    type: 'stack',
    options: {
      title: '主页',
      headerShown: false,
      path: '',
    },
    isInitial: true,
    children: [
      {
        name: 'Home',
        component: Home,
        type: 'tab',
        options: {
          title: '首页',
          tabBarLabel: '首页',
          headerTitleAlign: 'center',
          path: 'home',
        },
      },
      // 其他标签页...
    ],
  },
  {
    name: 'About',
    component: About,
    type: 'stack',
    options: {
      title: '关于我们',
      headerTitleAlign: 'center',
      path: 'about',
    },
  },
  // 其他堆栈页面...
];
```

每个应用（dp-client、dp-bussiness、dp-admin）都有自己的页面配置文件，但它们都使用共享的导航组件。

## 7. 导航与路由

本项目使用 React Navigation 进行导航，并与 React Strict DOM 集成。

### 导航结构

- 使用 `createBottomTabNavigator` 创建底部标签导航
- 使用 `createNativeStackNavigator` 创建堆栈导航
- 在 URL 中反映路由信息

### 共享导航模块

导航功能被提取到 `packages/navigation` 包中，以便在多个应用间共享：

```tsx
// packages/navigation/src/Navigation.tsx
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';

// 导航组件接收页面配置作为参数
export const Navigation = ({ pages, initialRouteName }) => {
  return (
    <NavigationContainer>
      <RootNavigator pages={pages} initialRouteName={initialRouteName} />
    </NavigationContainer>
  );
};

// 在应用中使用共享导航组件
// apps/dp-client/src/App.tsx
import { Navigation } from '@dp-frontend/navigation';
import { pages } from './configs/page-configs';

const App = () => {
  return <Navigation pages={pages} initialRouteName="Main" />;
};
```

### 导航钩子

提供了导航钩子以便在组件中访问导航参数：

```tsx
// packages/navigation/src/hooks.ts
import { useNavigation as useReactNavigation } from '@react-navigation/native';

export const useNavigation = () => {
  return useReactNavigation();
};

// 在页面组件中使用
import { useNavigation } from '@dp-frontend/navigation';

const HomePage = () => {
  const navigation = useNavigation();

  return (
    <html.div>
      <html.button onClick={() => navigation.navigate('About')}>
        前往关于页面
      </html.button>
    </html.div>
  );
};
```

### 页面间导航

```tsx
// 导航到其他页面
navigation.navigate('About');

// 返回上一页
navigation.goBack();
```

## 8. 平台特定代码

### 文件命名约定

- `Component.tsx` - 跨平台共享代码
- `Component.web.tsx` - 仅 Web 平台代码
- `Component.native.tsx` - 仅原生平台代码

### 条件渲染

使用 React Native 的 `Platform.select` 进行条件渲染：

```tsx
import { Platform } from 'react-native';

const styles = {
  container: Platform.select({
    web: tw('bg-blue-500'),
    native: tw('bg-red-500'),
    default: tw('bg-gray-500'),
  }),
};
```

### ESLint 规则

项目配置了特定的 ESLint 规则来强制执行平台特定代码的约定：

- `react-strict-dom-check/no-react-without-web` - 禁止在非 .web.tsx 文件中使用 React HTML 标签
- `react-strict-dom-check/no-react-native-in-web` - 禁止在 .web.tsx 文件中导入 react-native

## 9. 样式最佳实践

### 使用 Tailwind 类

优先使用 Tailwind 类定义样式：

```tsx
<html.div style={tw('flex-1 bg-gray-50 p-4')}>
  <html.p style={tw('text-lg font-bold text-blue-500')}>
    Hello World
  </html.p>
</html.div>
```

### 组合样式

使用数组组合多个样式源：

```tsx
<html.div style={[
  tw('flex-1'),
  isActive && tw('bg-blue-500'),
  !isActive && tw('bg-gray-200')
]}>
  Content
</html.div>
```

### 样式限制

React Strict DOM 不支持以下 CSS 特性：

- CSS 简写属性（如 `border`, `margin`, `padding`）
- `position: fixed`
- 某些伪类选择器

请使用完整属性名：

```tsx
// 错误
const styles = css.create({
  box: {
    margin: 10, // 简写属性不支持
  },
});

// 正确
const styles = css.create({
  box: {
    marginTop: 10,
    marginRight: 10,
    marginBottom: 10,
    marginLeft: 10,
  },
});
```

### 主题和变量

使用 CSS 变量和主题：

```tsx
// 定义变量
const tokens = css.defineVars({
  primaryColor: 'blue',
  textColor: 'black',
});

// 创建主题
const darkTheme = css.createTheme(tokens, {
  primaryColor: 'purple',
  textColor: 'white',
});

// 使用主题
<html.div style={darkTheme}>
  <html.p style={{ color: tokens.textColor }}>
    Themed text
  </html.p>
</html.div>
```

## 10. 性能优化

### 减小包体积

- 使用 React Strict DOM 替代 React Native Web
- 使用 Vite 进行 Web 打包
- 避免不必要的依赖

### 组件优化

- 使用 `React.memo` 减少不必要的重渲染
- 使用 `useCallback` 和 `useMemo` 缓存函数和计算值
- 避免在渲染函数中创建新对象和函数

```tsx
// 优化组件
const OptimizedComponent = React.memo(({ data }) => {
  const processedData = useMemo(() => {
    return expensiveCalculation(data);
  }, [data]);

  const handleClick = useCallback(() => {
    console.log('Clicked!');
  }, []);

  return (
    <html.div style={tw('p-4')} onClick={handleClick}>
      {processedData}
    </html.div>
  );
});
```

## 11. 调试和常见问题

### 调试工具

- 使用 React DevTools 调试组件树
- 使用浏览器开发者工具调试 Web 版本
- 使用 Expo 开发者工具调试原生版本

### 常见问题

1. **样式不生效**
   - 检查是否正确使用了 `tw()` 函数
   - 确保 PostCSS 配置正确
   - 检查 Tailwind 类名是否正确

2. **平台特定代码问题**
   - 确保遵循正确的文件命名约定
   - 使用 ESLint 检查平台特定代码规则

3. **导航问题**
   - 确保页面已在 `pages.tsx` 中正确配置
   - 检查导航参数类型是否正确

4. **性能问题**
   - 使用 React DevTools Profiler 分析性能瓶颈
   - 检查不必要的重渲染
   - 优化大型列表渲染

## 12. 共享模块

### 共享请求模块

项目使用 `@dp-frontend/request` 包提供统一的 HTTP 请求功能：

```tsx
// packages/request/src/request.ts
export class HttpRequest {
  private baseUrl: string;
  private headers: Record<string, string>;

  constructor(baseUrl: string, headers: Record<string, string> = {}) {
    this.baseUrl = baseUrl;
    this.headers = {
      'Content-Type': 'application/json',
      ...headers,
    };
  }

  async get<T>(url: string, params?: Record<string, any>): Promise<T> {
    // 实现GET请求
  }

  async post<T>(url: string, data?: any): Promise<T> {
    // 实现POST请求
  }

  // 其他方法...
}

// 在应用中使用
// apps/dp-client/src/services/api.ts
import { HttpRequest } from '@dp-frontend/request';

const apiClient = new HttpRequest('https://api.example.com');

export const fetchUserData = () => {
  return apiClient.get('/user');
};
```

### 包引用

应用通过 workspace 依赖引用共享包：

```json
// apps/dp-client/package.json (部分)
{
  "dependencies": {
    "@dp-frontend/request": "workspace:*",
    "@dp-frontend/navigation": "workspace:*",
    "@dp-frontend/ui": "workspace:*",
    // 其他依赖...
  }
}
```

## 13. Monorepo 工作流程

### 安装依赖

使用 pnpm 管理工作区依赖：

```bash
# 安装所有工作区依赖
pnpm install

# 为特定应用安装依赖
pnpm --filter dp-client add react-native-svg

# 为共享包安装依赖
pnpm --filter @dp-frontend/request add axios
```

### 构建和运行

```bash
# 构建所有包
pnpm build

# 运行特定应用
pnpm --filter dp-client dev

# 构建特定应用
pnpm --filter dp-client build
```

### 开发工作流

1. 修改共享包时，需要重新构建该包
2. 使用 `pnpm build` 构建所有包
3. 使用 `pnpm dev` 启动开发服务器

## 14. 参考资源

- [React Strict DOM 文档](https://github.com/facebook/react-strict-dom)
- [React Strict DOM Tailwind](https://github.com/FrederickTAT/react-strict-dom-tailwind)
- [Expo 文档](https://docs.expo.dev/)
- [React Navigation 文档](https://reactnavigation.org/docs/getting-started)
- [Tailwind CSS 文档](https://tailwindcss.com/docs)
- [pnpm Workspaces 文档](https://pnpm.io/workspaces)
