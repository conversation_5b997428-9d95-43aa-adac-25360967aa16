# 文档说明

本项目包含一个子模块 `docs/ddd-design-docs`，该子模块包含了项目的设计文档。

## 子模块使用说明

### 首次克隆项目

如果你是首次克隆项目，可以使用以下命令同时克隆项目及其子模块：

```bash
git clone --recurse-submodules http://gitlab.meichai.in/yuanchuan/dp-frontend.git
```

或者，如果你已经克隆了项目但没有子模块，可以使用以下命令初始化并更新子模块：

```bash
git submodule init
git submodule update
```

### 更新子模块

项目已配置为在执行 `git pull` 时自动更新子模块。如果你想手动更新子模块到最新版本，可以使用以下命令：

```bash
git submodule update --remote docs/ddd-design-docs
```

### 切换分支时更新子模块

当你切换分支时，如果子模块的版本在不同分支之间有变化，你可能需要更新子模块：

```bash
git checkout <branch>
git submodule update
```

### 提交子模块的更改

如果你需要在子模块中进行更改并提交，请先进入子模块目录：

```bash
cd docs/ddd-design-docs
git checkout develop  # 确保在正确的分支上
# 进行更改
git add .
git commit -m "你的提交信息"
git push
```

然后回到主项目，提交子模块的引用更新：

```bash
cd ../..
git add docs/ddd-design-docs
git commit -m "更新子模块引用"
git push
```

## 注意事项

- 子模块默认跟踪 `develop` 分支
- 项目配置了自动更新子模块，但在某些情况下可能需要手动更新
- 如果你在使用子模块时遇到问题，请参考上述命令或联系项目维护者
