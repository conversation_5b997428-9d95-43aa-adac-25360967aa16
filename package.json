{"name": "dp-frontend-monorepo", "version": "1.0.0", "private": true, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "scripts": {"prepare": "husky install", "lint-staged": "lint-staged", "dev:client": "pnpm --filter dp-client start", "dev:business": "pnpm --filter dp-business start", "dev:admin": "pnpm --filter dp-admin start", "android:client": "pnpm --filter dp-client android", "android:business": "pnpm --filter dp-business android", "android:admin": "pnpm --filter dp-admin android", "ios:client": "pnpm --filter dp-client ios", "ios:business": "pnpm --filter dp-business ios", "ios:admin": "pnpm --filter dp-admin ios", "web:client": "pnpm --filter dp-client web", "web:business": "pnpm --filter dp-business web", "web:admin": "pnpm --filter dp-admin web"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "git add"]}, "devDependencies": {"@babel/core": "^7.20.0", "@eslint/js": "^9.24.0", "@types/node": "^22.14.1", "@types/react": "~18.3.10", "autoprefixer": "^10.4.21", "eslint": "^9.24.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-react": "^7.37.5", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^16.0.0", "husky": "^9.1.7", "lint-staged": "^15.5.0", "prettier": "^3.5.3", "typescript": "^5.5.4", "typescript-eslint": "^8.29.1"}}