# DP Frontend Monorepo

这是一个使用pnpm workspace管理的前端monorepo项目。

## 项目结构

```
dp-frontend/
├── apps/                # 应用程序目录
│   ├── dp-client/       # 用户端应用
│   │   ├── assets/      # 资源文件
│   │   ├── src/         # 源代码
│   │   └── ...
│   ├── dp-business/     # 商家端应用
│   │   ├── assets/      # 资源文件
│   │   ├── src/         # 源代码
│   │   └── ...
│   └── dp-admin/        # 运营端应用
│       ├── assets/      # 资源文件
│       ├── src/         # 源代码
│       └── ...
├── packages/            # 共享包目录
│   ├── ui/              # UI组件库
│   │   └── src/         # 源代码
│   ├── navigation/      # 导航组件库
│   │   └── src/         # 源代码
│   └── request/         # HTTP请求工具库
│       └── src/         # 源代码
└── ...
```

## 安装依赖

```bash
pnpm install
```

## 开发

启动用户端应用：

```bash
pnpm dev:client
```

启动商家端应用：

```bash
pnpm dev:business
```

启动运营端应用：

```bash
pnpm dev:admin
```

启动iOS应用（用户端）：

```bash
pnpm ios:client
```

启动Android应用（用户端）：

```bash
pnpm android:client
```

启动Web应用（用户端）：

```bash
pnpm web:client
```

其他端口的iOS、Android和Web应用启动命令类似，只需替换后缀为`:business`或`:admin`即可。

## 添加依赖

向用户端应用添加依赖：

```bash
pnpm --filter dp-client add <package-name>
```

向商家端应用添加依赖：

```bash
pnpm --filter dp-business add <package-name>
```

向运营端应用添加依赖：

```bash
pnpm --filter dp-admin add <package-name>
```

向UI库添加依赖：

```bash
pnpm --filter @dp-frontend/ui add <package-name>
```

向HTTP请求库添加依赖：

```bash
pnpm --filter @dp-frontend/request add <package-name>
```

向导航库添加依赖：

```bash
pnpm --filter @dp-frontend/navigation add <package-name>
```

向根目录添加开发依赖：

```bash
pnpm add -D -w <package-name>
```

## 创建新应用或包

1. 在apps/或packages/目录下创建新目录
2. 添加package.json文件
3. 添加到pnpm-workspace.yaml（如果需要）

## 使用共享包

在应用中引用共享包：

```json
{
  "dependencies": {
    "@dp-frontend/ui": "workspace:*",
    "@dp-frontend/navigation": "workspace:*",
    "@dp-frontend/request": "workspace:*"
  }
}
```

然后在代码中导入：

```javascript
// 导入UI组件
import { Button } from '@dp-frontend/ui';

// 导入导航组件
import { Navigation } from '@dp-frontend/navigation';

// 导入HTTP请求工具
import { net, HttpRequest } from '@dp-frontend/request';

// 使用HTTP请求工具
const fetchData = async () => {
  try {
    const data = await net.get('/api/data');
    console.log(data);
  } catch (error) {
    console.error(error);
  }
};
```
