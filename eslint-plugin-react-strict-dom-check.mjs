import path from 'path';

export default {
  rules: {
    'no-react-without-web': {
      meta: {
        type: 'problem',
        docs: {
          description: 'Disallow React HTML tags in files without .web suffix',
          recommended: 'error',
        },
        messages: {
          noReactWithoutWeb:
            'React tags are only allowed in .web.jsx/.web.tsx files',
        },
      },
      create(context) {
        const filename = context.getFilename();
        const ext = path.extname(filename);
        const basename = path.basename(filename, ext);

        const reportedNodes = new WeakMap();

        const checkReactElement = (node) => {
          const isReactElement =
            node.name.type === 'JSXIdentifier' &&
            /^[a-z]/.test(node.name.name) &&
            node.type !== 'JSXMemberExpression';
          if (isReactElement && !reportedNodes.has(node)) {
            reportedNodes.set(node, true);
            context.report({
              node,
              messageId: 'noReactWithoutWeb',
            });
          }
        };

        if (!basename.endsWith('.web') && (ext === '.jsx' || ext === '.tsx')) {
          return {
            JSXElement(node) {
              checkReactElement(node.openingElement);
            },
            JSXOpeningElement(node) {
              checkReactElement(node);
            },
            JSXClosingElement(node) {
              checkReactElement(node);
            },
          };
        }
        return {};
      },
    },
    'no-react-native-in-web': {
      meta: {
        type: 'problem',
        docs: {
          description: 'Disallow react-native imports in files in .web suffix',
          recommended: 'error',
        },
        messages: {
          noRNWithoutNative:
            'react-native is not allowed in .web.js/.web.ts files',
        },
      },
      create(context) {
        const filename = context.getFilename();
        const ext = path.extname(filename);
        const basename = path.basename(filename, ext);

        if (basename.endsWith('.web')) {
          return {
            ImportDeclaration(node) {
              if (node.source.value === 'react-native') {
                context.report({
                  node,
                  messageId: 'noRNWithoutNative',
                });
              }
            },
          };
        }
        return {};
      },
    },
  },
};
