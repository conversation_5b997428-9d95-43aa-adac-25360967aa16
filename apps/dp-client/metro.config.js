/* eslint-disable @typescript-eslint/no-require-imports */
// Learn more https://docs.expo.dev/guides/monorepos
const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');

// Find the project and workspace directories
const projectRoot = __dirname;
const workspaceRoot = path.resolve(projectRoot, '../..');

const config = getDefaultConfig(projectRoot, {
  // 添加monorepo支持
  isCSSEnabled: true,
});

// 1. Enable Metro support for symlinks and package exports
config.resolver.unstable_enablePackageExports = true;
// 2. Watch all files within the monorepo
config.watchFolders = [workspaceRoot];
// 3. Let Metro know where to resolve packages
config.resolver.nodeModulesPaths = [
  path.resolve(projectRoot, 'node_modules'),
  path.resolve(workspaceRoot, 'node_modules'),
];
// 4. 添加路径别名
config.resolver.extraNodeModules = {
  '@': path.resolve(projectRoot),
};

config.resolver.resolveRequest = function packageExportsResolver(
  context,
  moduleImport,
  platform
) {
  // Use the browser version of the package for React Native
  if (moduleImport === 'axios' || moduleImport.startsWith('axios/')) {
    return context.resolveRequest(
      {
        ...context,
        unstable_conditionNames: ['browser'],
      },
      moduleImport,
      platform
    );
  }

  // Fall back to normal resolution
  return context.resolveRequest(context, moduleImport, platform);
};

module.exports = config;
