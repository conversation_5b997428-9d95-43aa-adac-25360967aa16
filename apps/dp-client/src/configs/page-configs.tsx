import type { PageConfig } from '@dp-frontend/navigation';

// 导入页面组件
import About from '../pages/About';
import Home from '../pages/Home';
import Profile from '../pages/Profile';
import Settings from '../pages/Settings';

// 定义页面配置
export const pageConfigs: PageConfig[] = [
  {
    name: 'Main',
    component: Home, // 临时使用Home组件，实际上这个组件不会被直接渲染
    type: 'stack',
    options: {
      title: '主页',
      headerShown: false,
      path: '',
    },
    isInitial: true,
    children: [
      {
        name: 'Home',
        component: Home,
        type: 'tab',
        options: {
          title: '首页',
          tabBarLabel: '首页',
          headerTitleAlign: 'center',
          path: 'home',
        },
      },
      {
        name: 'Settings',
        component: Settings,
        type: 'tab',
        options: {
          title: '设置',
          tabBarLabel: '设置',
          headerTitleAlign: 'center',
          path: 'settings',
        },
      },
    ],
  },
  {
    name: 'About',
    component: About,
    type: 'stack',
    options: {
      title: '关于我们',
      headerTitleAlign: 'center',
      path: 'about',
    },
  },
  {
    name: 'Profile',
    component: Profile,
    type: 'stack',
    options: {
      title: '个人资料',
      headerTitleAlign: 'center',
      path: 'profile',
    },
  },
];
