// 格式化手机号，隐藏中间部分
export const maskPhoneNumber = (phone: string) => {
  if (!phone) return '';

  if (phone.length <= 4) return phone;

  const prefix = phone.substring(0, 2);
  const suffix = phone.substring(phone.length - 2);

  return `${prefix}***${suffix}`;
};

// 格式化邮箱，隐藏用户名中间部分
export const maskEmail = (email: string) => {
  if (!email) return '';

  const parts = email.split('@');
  if (parts.length !== 2) return email;

  const [username, domain] = parts;

  if (username.length <= 2) {
    return `${username}@${domain}`;
  }

  const prefix = username.substring(0, 2);
  const maskedUsername = `${prefix}***`;

  return `${maskedUsername}@${domain}`;
};
