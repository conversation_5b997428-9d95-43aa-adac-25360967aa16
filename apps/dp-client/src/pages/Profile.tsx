import { useNavigation } from '@dp-frontend/navigation';
import { Button } from '@dp-frontend/ui';
import React from 'react';
import { html } from 'react-strict-dom';
import { tw } from 'react-strict-dom-tailwind';

const Profile = () => {
  const navigation = useNavigation();
  return (
    <html.div style={tw('flex-1 bg-gray-50 flex flex-col pt-5')}>
      <html.p style={tw('text-base text-center mb-2.5 text-gray-800')}>
        这是用户个人资料页面
      </html.p>
      <html.p style={tw('text-base text-center mb-2.5 text-gray-800')}>
        您可以在这里查看和编辑您的个人信息。
      </html.p>

      <html.div style={tw('flex flex-col items-center mt-5')}>
        <Button onClick={() => navigation.navigate('Home')} color="primary">
          返回首页
        </Button>

        <Button onClick={() => navigation.navigate('Settings')} color="default">
          返回设置
        </Button>
      </html.div>
    </html.div>
  );
};

export default Profile;
