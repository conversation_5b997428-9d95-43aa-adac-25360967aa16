import { useNavigation } from '@dp-frontend/navigation';
import React from 'react';
import { html } from 'react-strict-dom';
import { tw } from 'react-strict-dom-tailwind';

const Settings = () => {
  const navigation = useNavigation();
  return (
    <html.div style={tw('flex-1 bg-gray-50 flex flex-col pt-5')}>
      <html.div style={tw('w-full max-w-[400px] mt-2.5 mx-auto')}>
        <html.div
          style={tw(
            'w-full p-4 border-b border-gray-200 mb-2.5 bg-white rounded-lg'
          )}
          onClick={() => {}}
        >
          <html.span style={tw('text-base text-gray-800')}>账户设置</html.span>
        </html.div>

        <html.div
          style={tw(
            'w-full p-4 border-b border-gray-200 mb-2.5 bg-white rounded-lg'
          )}
          onClick={() => {}}
        >
          <html.span style={tw('text-base text-gray-800')}>通知设置</html.span>
        </html.div>

        <html.div
          style={tw(
            'w-full p-4 border-b border-gray-200 mb-2.5 bg-white rounded-lg'
          )}
          onClick={() => {}}
        >
          <html.span style={tw('text-base text-gray-800')}>隐私设置</html.span>
        </html.div>

        <html.div
          style={tw(
            'w-full p-4 border-b border-gray-200 mb-2.5 bg-white rounded-lg'
          )}
          onClick={() => navigation.navigate('Profile')}
        >
          <html.span style={tw('text-base text-gray-800')}>个人资料</html.span>
        </html.div>

        <html.div
          style={tw(
            'w-full p-4 border-b border-gray-200 mb-2.5 bg-white rounded-lg'
          )}
          onClick={() => navigation.navigate('About')}
        >
          <html.span style={tw('text-base text-gray-800')}>关于应用</html.span>
        </html.div>
      </html.div>
    </html.div>
  );
};

export default Settings;
