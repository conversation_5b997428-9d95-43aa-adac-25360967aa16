import { useNavigation } from '@dp-frontend/navigation';
import { Button } from '@dp-frontend/ui';
import React from 'react';
import { html } from 'react-strict-dom';
import { tw } from 'react-strict-dom-tailwind';

const Home = () => {
  const navigation = useNavigation();
  return (
    <html.div style={tw('flex-1 bg-gray-50 flex flex-col pt-5')}>
      <html.p style={tw('text-base text-center mb-2.5 text-gray-800')}>
        欢迎来到我们的应用！
      </html.p>
      <html.p style={tw('text-base text-center mb-2.5 text-gray-800')}>
        这是使用Expo和React Strict DOM构建的应用程序。
      </html.p>
      <html.div style={tw('flex flex-col items-center mt-5')}>
        <Button onClick={() => navigation.navigate('About')} color="primary">
          前往关于页面
        </Button>

        <Button onClick={() => navigation.navigate('Profile')}>
          前往个人资料
        </Button>

        <Button onClick={() => navigation.navigate('Settings')}>
          前往设置页面
        </Button>
      </html.div>
    </html.div>
  );
};

export default Home;
