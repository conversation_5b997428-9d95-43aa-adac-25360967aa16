import { useNavigation } from '@dp-frontend/navigation';
import { Button } from '@dp-frontend/ui';
import React from 'react';
import { html } from 'react-strict-dom';
import { tw } from 'react-strict-dom-tailwind';

const About = () => {
  const navigation = useNavigation();
  return (
    <html.div style={tw('flex-1 bg-gray-50 flex flex-col pt-5')}>
      <html.p style={tw('text-base text-center mb-2.5 text-gray-800')}>
        这是一个使用Expo和React Strict DOM构建的示例应用。
      </html.p>
      <html.p style={tw('text-base text-center mb-2.5 text-gray-800')}>
        React Strict
        DOM是一个用于构建跨平台应用的库，它提供了一致的API来创建Web和移动应用。
      </html.p>

      <html.div style={tw('flex flex-col items-center mt-5')}>
        <Button onClick={() => navigation.navigate('Home')} color="primary">
          返回首页
        </Button>

        <Button
          onClick={() => navigation.navigate('Settings')}
          color="secondary"
        >
          返回设置
        </Button>
      </html.div>
    </html.div>
  );
};

export default About;
