import { Navigation } from '@dp-frontend/navigation';
import { ConfigProvider, themes } from '@dp-frontend/ui';
import React from 'react';
import { SafeAreaProvider } from 'react-native-safe-area-context';

import { pageConfigs } from './configs/page-configs';

const App: React.FC = () => {
  return (
    <React.StrictMode>
      <SafeAreaProvider>
        <ConfigProvider theme={themes}>
          <Navigation pages={pageConfigs} />
        </ConfigProvider>
      </SafeAreaProvider>
    </React.StrictMode>
  );
};

export default App;
