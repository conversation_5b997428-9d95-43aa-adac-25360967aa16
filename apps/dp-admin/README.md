# 運營管理系統 (dp-admin)

## 專案概述

運營管理系統是一個基於 React 和 Expo 構建的現代化管理後台，提供了用戶管理、數據統計等功能。系統採用響應式設計，可以在不同設備上提供良好的用戶體驗。

## 技術棧

- **前端框架**: React 18
- **UI 組件庫**: Ant Design 5
- **樣式解決方案**: Tailwind CSS 4
- **路由管理**: React Router 6
- **跨平台支持**: Expo 52 / React Native Web
- **圖標庫**: Ant Design Icons

## 專案結構

```
/src
├── components/            # 全局共享組件
│   └── layout/            # 佈局相關組件
├── configs/               # 配置文件
├── pages/                 # 頁面組件
│   ├── home/              # 首頁
│   ├── login/             # 登錄頁面
│   ├── statistics/        # 數據統計頁面
│   └── users/             # 用戶管理頁面
├── router/                # 路由系統
├── styles/                # 全局樣式
├── App.web.tsx            # 應用入口組件
└── index.js               # 應用入口文件
```

## 核心模組

### 1. 佈局系統

佈局系統提供了一致的頁面結構，包括響應式側邊欄導航、頂部導航欄和主內容區域。

**主要組件**:
- `Layout`: 整體佈局容器
- `Sidebar`: 側邊欄導航
- `Header`: 頂部導航欄
- `Main`: 主內容區域

**詳細文檔**: [佈局組件文檔](./src/components/layout/index.md)

### 2. 路由系統

路由系統負責管理頁面導航和訪問控制，基於 React Router 6 實現。

**主要功能**:
- 集中式路由配置
- 路由保護機制
- 身份驗證集成
- 模組化結構

**詳細文檔**: [路由系統文檔](./src/router/README.md)

### 3. 頁面模組

#### 3.1 登錄頁面

提供用戶登錄功能，包含用戶名/密碼輸入和記住密碼選項。

**主要組件**:
- `LoginForm`: 登錄表單組件
- `DecorativeBackground`: 裝飾性背景組件

**詳細文檔**: [登錄頁面文檔](./src/pages/login/README.md)

#### 3.2 首頁

系統的主頁面，展示概覽信息和快速入口。

#### 3.3 用戶管理

提供用戶信息的查看、編輯、添加和刪除功能。

#### 3.4 數據統計

展示系統的各類統計數據和圖表。

## 開發指南

### 環境要求

- Node.js 18+
- pnpm 8+

### 安裝依賴

```bash
pnpm install
```

### 啟動開發服務器

```bash
# 啟動 Web 版本
pnpm web

# 啟動 iOS 版本
pnpm ios

# 啟動 Android 版本
pnpm android
```

### 構建 Web 版本

```bash
pnpm build:web
```

## 專案規範

### 文件命名規範

- 組件文件使用 PascalCase 命名，如 `LoginForm.web.tsx`
- Web 特定組件使用 `.web.tsx` 後綴
- 組件文檔使用與組件同名的 `.md` 文件
- 目錄使用 kebab-case 命名，如 `user-management`

### 代碼風格

- 使用 TypeScript 進行類型檢查
- 使用函數組件和 React Hooks
- 使用 Tailwind CSS 進行樣式設計
- 遵循組件化和模組化原則

### 組件設計原則

1. **一致性**: 所有組件遵循一致的設計語言和交互模式
2. **可復用性**: 組件設計為高度可復用
3. **響應式**: 組件支持響應式設計
4. **可訪問性**: 組件遵循可訪問性最佳實踐
5. **類型安全**: 所有組件使用 TypeScript 開發

## 目錄詳解

### `/src/components`

包含全局共享的 UI 組件，這些組件可以在多個頁面中重複使用。

**主要組件**:
- Layout 系列組件: 提供整體頁面佈局

**詳細文檔**: [組件庫文檔](./src/components/README.md)

### `/src/pages`

包含應用的所有頁面組件，每個頁面都有自己的目錄。

**頁面結構**:
- `index.web.tsx`: 頁面主組件
- `components/`: 頁面特定的組件
- `README.md`: 頁面文檔

### `/src/router`

包含路由相關的配置和組件。

**主要文件**:
- `routes.web.tsx`: 定義所有路由
- `ProtectedRoute.web.tsx`: 受保護路由組件
- `auth.ts`: 身份驗證相關函數

### `/src/configs`

包含應用的配置文件。

**主要文件**:
- `page-configs.tsx`: 頁面配置信息

### `/src/styles`

包含全局樣式定義。

**主要文件**:
- `global.css`: 全局 CSS 樣式
- `stylex.css`: Tailwind CSS 配置

## 常見問題

### 1. 如何添加新頁面？

1. 在 `/src/pages` 目錄下創建新的頁面目錄
2. 創建頁面主組件 `index.web.tsx`
3. 在 `/src/router/routes.web.tsx` 中添加新的路由配置
4. 如果需要在側邊欄顯示，更新 Sidebar 組件中的導航項

### 2. 如何添加新組件？

1. 在適當的目錄下創建新的組件文件，使用 `.web.tsx` 後綴
2. 為組件創建詳細的文檔，使用與組件同名的 `.md` 文件
3. 如果是全局組件，在 `/src/components/README.md` 中添加組件的簡要描述

### 3. 如何處理身份驗證？

身份驗證邏輯集中在 `/src/router/auth.ts` 文件中，包括:
- `isAuthenticated()`: 檢查用戶是否已登錄
- `login()`: 處理用戶登錄
- `logout()`: 處理用戶登出

## 未來計劃

- 添加更多基礎組件（按鈕、選擇器、複選框等）
- 實現表單組件庫
- 添加數據展示組件（表格、圖表等）
- 支持深色模式
- 實現組件測試
- 添加國際化支持
- 優化移動端體驗

## 貢獻指南

### 添加新功能

1. 創建新的功能分支
2. 實現功能並添加適當的文檔
3. 提交代碼並創建合併請求
4. 等待代碼審查和合併

### 報告問題

如發現問題，請在專案的 Issue 跟踪器中創建新的 Issue，並提供以下信息:
- 問題描述
- 復現步驟
- 預期行為
- 實際行為
- 環境信息
