# 运营管理系统 (dp-admin)

## 项目概述

运营管理系统是一个基于 React 和 Expo 构建的现代化管理后台，提供了用户管理、数据统计等功能。系统采用响应式设计，可以在不同设备上提供良好的用户体验。

## 技术栈

- **前端框架**: React 18
- **UI 组件库**: Ant Design 5
- **样式解决方案**: Tailwind CSS 4
- **路由管理**: React Router 6
- **跨平台支持**: Expo 52 / React Native Web
- **图标库**: Ant Design Icons

## 项目结构

```
/src
├── components/            # 全局共享组件
│   └── layout/            # 布局相关组件
├── configs/               # 配置文件
├── pages/                 # 页面组件
│   ├── home/              # 首页
│   ├── login/             # 登录页面
│   ├── statistics/        # 数据统计页面
│   └── users/             # 用户管理页面
├── router/                # 路由系统
├── styles/                # 全局样式
├── App.web.tsx            # 应用入口组件
└── index.js               # 应用入口文件
```

## 核心模块

### 1. 布局系统

布局系统提供了一致的页面结构，包括响应式侧边栏导航、顶部导航栏和主内容区域。

**主要组件**:
- `Layout`: 整体布局容器
- `Sidebar`: 侧边栏导航
- `Header`: 顶部导航栏
- `Main`: 主内容区域

**详细文档**: [布局组件文档](./src/components/layout/index.md)

### 2. 路由系统

路由系统负责管理页面导航和访问控制，基于 React Router 6 实现。

**主要功能**:
- 集中式路由配置
- 路由保护机制
- 身份验证集成
- 模块化结构

**详细文档**: [路由系统文档](./src/router/README.md)

### 3. 页面模块

#### 3.1 登录页面

提供用户登录功能，包含用户名/密码输入和记住密码选项。

**主要组件**:
- `LoginForm`: 登录表单组件
- `DecorativeBackground`: 装饰性背景组件

**详细文档**: [登录页面文档](./src/pages/login/README.md)

#### 3.2 首页

系统的主页面，展示概览信息和快速入口。

#### 3.3 用户管理

提供用户信息的查看、编辑、添加和删除功能。

#### 3.4 数据统计

展示系统的各类统计数据和图表。

## 开发指南

### 环境要求

- Node.js 18+
- pnpm 8+

### 安装依赖

```bash
pnpm install
```

### 启动开发服务器

```bash
# 启动 Web 版本
pnpm web

# 启动 iOS 版本
pnpm ios

# 启动 Android 版本
pnpm android
```

### 构建 Web 版本

```bash
pnpm build:web
```

## 项目规范

### 文件命名规范

- 组件文件使用 PascalCase 命名，如 `LoginForm.web.tsx`
- Web 特定组件使用 `.web.tsx` 后缀
- 组件文档使用与组件同名的 `.md` 文件
- 目录使用 kebab-case 命名，如 `user-management`

### 代码风格

- 使用 TypeScript 进行类型检查
- 使用函数组件和 React Hooks
- 使用 Tailwind CSS 进行样式设计
- 遵循组件化和模块化原则

### 组件设计原则

1. **一致性**: 所有组件遵循一致的设计语言和交互模式
2. **可复用性**: 组件设计为高度可复用
3. **响应式**: 组件支持响应式设计
4. **可访问性**: 组件遵循可访问性最佳实践
5. **类型安全**: 所有组件使用 TypeScript 开发

## 目录详解

### `/src/components`

包含全局共享的 UI 组件，这些组件可以在多个页面中重复使用。

**主要组件**:
- Layout 系列组件: 提供整体页面布局

**详细文档**: [组件库文档](./src/components/README.md)

### `/src/pages`

包含应用的所有页面组件，每个页面都有自己的目录。

**页面结构**:
- `index.web.tsx`: 页面主组件
- `components/`: 页面特定的组件
- `README.md`: 页面文档

### `/src/router`

包含路由相关的配置和组件。

**主要文件**:
- `routes.web.tsx`: 定义所有路由
- `ProtectedRoute.web.tsx`: 受保护路由组件
- `auth.ts`: 身份验证相关函数

### `/src/configs`

包含应用的配置文件。

**主要文件**:
- `page-configs.tsx`: 页面配置信息

### `/src/styles`

包含全局样式定义。

**主要文件**:
- `global.css`: 全局 CSS 样式
- `stylex.css`: Tailwind CSS 配置

## 常见问题

### 1. 如何添加新页面？

1. 在 `/src/pages` 目录下创建新的页面目录
2. 创建页面主组件 `index.web.tsx`
3. 在 `/src/router/routes.web.tsx` 中添加新的路由配置
4. 如果需要在侧边栏显示，更新 Sidebar 组件中的导航项

### 2. 如何添加新组件？

1. 在适当的目录下创建新的组件文件，使用 `.web.tsx` 后缀
2. 为组件创建详细的文档，使用与组件同名的 `.md` 文件
3. 如果是全局组件，在 `/src/components/README.md` 中添加组件的简要描述

### 3. 如何处理身份验证？

身份验证逻辑集中在 `/src/router/auth.ts` 文件中，包括:
- `isAuthenticated()`: 检查用户是否已登录
- `login()`: 处理用户登录
- `logout()`: 处理用户登出

## 未来计划

- 添加更多基础组件（按钮、选择器、复选框等）
- 实现表单组件库
- 添加数据展示组件（表格、图表等）
- 支持深色模式
- 实现组件测试
- 添加国际化支持
- 优化移动端体验

## 贡献指南

### 添加新功能

1. 创建新的功能分支
2. 实现功能并添加适当的文档
3. 提交代码并创建合并请求
4. 等待代码审查和合并

### 报告问题

如发现问题，请在项目的 Issue 跟踪器中创建新的 Issue，并提供以下信息:
- 问题描述
- 复现步骤
- 预期行为
- 实际行为
- 环境信息
