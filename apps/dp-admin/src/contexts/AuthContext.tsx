import React, {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useState,
} from 'react';

import { decrypt, encrypt } from '../utils/crypto';

// 认证状态接口
interface AuthState {
  isAuthenticated: boolean;
  username: string | null;
  token: string | null;
  loading: boolean;
}

// 保存的凭证接口
interface SavedCredentials {
  username: string;
  password?: string;
  remember: boolean;
}

// 认证上下文接口
interface AuthContextType extends AuthState {
  clearAuthState: () => void;
  saveAuthData: (username: string, password: string, remember: boolean) => void;
  getSavedCredentials: () => SavedCredentials | null;
}

// 创建认证上下文
const AuthContext = createContext<AuthContextType | null>(null);

/**
 * 清除认证数据的辅助函数
 */
const clearAuthDataHelper = () => {
  localStorage.removeItem('auth_token');
  localStorage.removeItem('auth_username');
  localStorage.removeItem('auth_password');
};

// 认证提供者组件
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  console.log('AuthProvider rendered');

  // 认证状态
  const [authState, setAuthState] = useState<AuthState>({
    isAuthenticated: false,
    username: null,
    token: null,
    loading: true,
  });

  // 清除认证状态（供登出功能使用）
  const clearAuthState = useCallback(() => {
    clearAuthDataHelper();
    setAuthState({
      isAuthenticated: false,
      username: null,
      token: null,
      loading: false,
    });
    console.log('Auth state cleared');
  }, []);

  // 保存认证数据
  const saveAuthData = useCallback(
    (username: string, password: string, remember: boolean) => {
      console.log('Saving auth data:', { username, remember });

      // 生成 token (在实际应用中，这应该由服务器生成)
      const token = `${username}_${Date.now()}`;
      const encryptedToken = encrypt(token);

      // 保存 token
      localStorage.setItem('auth_token', encryptedToken);

      if (remember) {
        // 如果选择记住密码，保存用户名和加密的密码
        localStorage.setItem('auth_username', username);
        const encryptedPassword = encrypt(password);
        localStorage.setItem('auth_password', encryptedPassword);
        console.log('Saved username and password');
      } else {
        // 如果不记住密码，只保存 token
        localStorage.removeItem('auth_username');
        localStorage.removeItem('auth_password');
        console.log('Removed saved username and password');
      }

      setAuthState({
        isAuthenticated: true,
        username,
        token,
        loading: false,
      });

      console.log('Auth state updated after login');
    },
    []
  );

  // 获取保存的登录信息
  const getSavedCredentials = useCallback((): SavedCredentials | null => {
    const username = localStorage.getItem('auth_username');
    const encryptedPassword = localStorage.getItem('auth_password');

    console.log('Getting saved credentials:', {
      hasUsername: !!username,
      hasPassword: !!encryptedPassword,
    });

    if (!username) return null;

    const credentials: SavedCredentials = {
      username,
      remember: !!encryptedPassword,
    };

    if (encryptedPassword) {
      try {
        credentials.password = decrypt(encryptedPassword);
        console.log('Password decrypted successfully');
      } catch (error) {
        console.error('密码解密失败:', error);
        localStorage.removeItem('auth_password');
      }
    }

    return credentials;
  }, []);

  // 初始化时检查认证状态
  useEffect(() => {
    console.log('AuthProvider useEffect executing');

    const checkAuth = () => {
      // 开发模式下，可以直接设置为已认证状态，方便开发
      const isDevelopment = process.env.NODE_ENV === 'development';

      if (isDevelopment) {
        console.log('Development mode: setting authenticated state');
        // 在开发模式下，直接设置为已认证状态
        setAuthState({
          isAuthenticated: true,
          username: 'admin',
          token: 'dev_token',
          loading: false,
        });
        return;
      }

      const token = localStorage.getItem('auth_token');
      const username = localStorage.getItem('auth_username');
      console.log('Checking auth state:', { token: !!token, username });

      if (token && username) {
        try {
          // 尝试解密 token 以验证其有效性
          const decryptedToken = decrypt(token);
          console.log('Token decrypted successfully');

          setAuthState({
            isAuthenticated: true,
            username,
            token: decryptedToken,
            loading: false,
          });
        } catch (error) {
          console.error('Token 解密失败:', error);
          clearAuthDataHelper();
          setAuthState({
            isAuthenticated: false,
            username: null,
            token: null,
            loading: false,
          });
        }
      } else {
        console.log('No token or username found');
        setAuthState({
          isAuthenticated: false,
          username: null,
          token: null,
          loading: false,
        });
      }
    };

    checkAuth();
  }, []);

  // 提供认证上下文
  return (
    <AuthContext.Provider
      value={{
        isAuthenticated: authState.isAuthenticated,
        username: authState.username,
        token: authState.token,
        loading: authState.loading,
        clearAuthState,
        saveAuthData,
        getSavedCredentials,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

// 使用认证上下文的自定义 Hook
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
