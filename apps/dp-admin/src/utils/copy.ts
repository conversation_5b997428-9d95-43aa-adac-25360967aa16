/**
 * 跨浏览器兼容的复制函数
 * @param {string} text 要复制的文本内容
 * @returns {Promise<boolean>} 复制成功返回true，失败返回false
 */
export function copyTextToClipboard(text: string) {
  // 优先使用现代Clipboard API（需要用户交互触发）
  if (navigator.clipboard) {
    return navigator.clipboard
      .writeText(text)
      .then(() => {
        console.log('Clipboard API复制成功');
        return true;
      })
      .catch((err) => {
        console.error('Clipboard API复制失败:', err);
        return tryFallbackCopy(text);
      });
  }

  // 回退到传统execCommand方法
  return Promise.resolve(tryFallbackCopy(text));
}

/**
 * 传统复制方法（兼容旧版浏览器）
 * @param {string} text 要复制的文本
 * @returns {boolean} 复制成功状态
 */
function tryFallbackCopy(text: string) {
  // 创建临时文本区域
  const textarea = document.createElement('textarea');
  textarea.value = text;

  // 隐藏元素（不影响布局）
  textarea.style.position = 'absolute';
  textarea.style.left = '-9999px';
  textarea.style.top = '-9999px';
  textarea.style.opacity = '0';

  document.body.appendChild(textarea);

  // 选中文本（处理iOS不支持select()的情况）
  const isiOS = /iP(hone|ad|od)/.test(navigator.userAgent);
  if (isiOS) {
    const range = document.createRange();
    range.selectNodeContents(textarea);
    const selection = window.getSelection();
    selection?.removeAllRanges();
    selection?.addRange(range);
    textarea.setSelectionRange(0, text.length);
  } else {
    textarea.select();
  }

  // 执行复制命令
  let success = false;
  try {
    success = document.execCommand('copy');
    console.log('execCommand复制成功');
  } catch (err) {
    console.error('execCommand复制失败:', err);
  }

  // 清理临时元素
  document.body.removeChild(textarea);
  return success;
}
