/**
 * 加密工具函数
 *
 * 提供密码加密和解密功能
 */
import CryptoJS from 'crypto-js';

// 加密密钥，在实际应用中应该从环境变量或配置文件中获取
// 这里为了演示，使用一个固定的密钥
const SECRET_KEY = 'dp-admin-secret-key-2025';

/**
 * 使用 AES 加密字符串
 *
 * @param text 要加密的文本
 * @returns 加密后的字符串
 */
export const encrypt = (text: string): string => {
  return CryptoJS.AES.encrypt(text, SECRET_KEY).toString();
};

/**
 * 解密 AES 加密的字符串
 *
 * @param ciphertext 加密的文本
 * @returns 解密后的字符串
 */
export const decrypt = (ciphertext: string): string => {
  const bytes = CryptoJS.AES.decrypt(ciphertext, SECRET_KEY);
  return bytes.toString(CryptoJS.enc.Utf8);
};

/**
 * 使用 SHA256 哈希算法生成密码哈希
 *
 * @param password 密码
 * @returns 哈希后的密码
 */
export const hashPassword = (password: string): string => {
  return CryptoJS.SHA256(password).toString();
};

/**
 * 使用 MD5 生成哈希值
 *
 * @param text 要哈希的文本
 * @returns MD5 哈希值
 */
export const md5Hash = (text: string): string => {
  return CryptoJS.MD5(text).toString();
};
