/**
 * Tailwind CSS 自定义工具类
 * 
 * 使用 @layer utilities 定义自定义工具类
 * 这些类会被自动添加到 Tailwind CSS 的工具类中
 */

@layer utilities {
  /* Flex 布局相关 */
  .flex-cc {
    @apply flex items-center justify-center;
  }
  
  .flex-cs {
    @apply flex items-center justify-start;
  }
  
  .flex-ce {
    @apply flex items-center justify-end;
  }
  
  .flex-cb {
    @apply flex items-center justify-between;
  }
  
  .flex-sc {
    @apply flex items-start justify-center;
  }
  
  .flex-ec {
    @apply flex items-end justify-center;
  }
  
  /* Flex 列布局相关 */
  .flex-col-cc {
    @apply flex flex-col items-center justify-center;
  }
  
  .flex-col-cs {
    @apply flex flex-col items-center justify-start;
  }
  
  .flex-col-ce {
    @apply flex flex-col items-center justify-end;
  }
  
  .flex-col-sc {
    @apply flex flex-col items-start justify-center;
  }
  
  .flex-col-ec {
    @apply flex flex-col items-end justify-center;
  }
  
  /* 绝对定位相关 */
  .absolute-center {
    @apply absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2;
  }
}
