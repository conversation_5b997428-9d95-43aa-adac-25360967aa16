# 主内容区组件 (Main)

## 组件概述

`Main` 是一个主内容区组件，作为布局系统的内容容器，用于展示页面的主要内容。它提供适当的内边距和滚动行为，确保内容在各种设备上都能正确显示。

## 文件位置

```
/components/layout/components/Main.web.tsx
```

## 组件接口

```typescript
interface MainProps {
  children: React.ReactNode; // 子组件，将被渲染在主内容区
}
```

## 功能特性

1. **内容容器**：
   - 提供适当的内边距，确保内容不会贴近边缘
   - 设置背景色，与其他布局元素形成视觉区分

2. **滚动行为**：
   - 启用垂直滚动，确保长内容可以完整浏览
   - 禁用水平滚动，避免页面布局问题
   - 保持内容区域占满可用空间

3. **响应式设计**：
   - 在不同屏幕尺寸下自动调整内边距
   - 确保在移动设备和桌面设备上都有良好的显示效果

## 实现细节

组件使用 Flexbox 布局和 Tailwind CSS 类实现响应式设计：

```jsx
const Main: React.FC<MainProps> = ({ children }) => {
  return (
    <main className="flex-1 p-4 sm:p-6 md:p-8 overflow-x-hidden overflow-y-auto bg-gray-100">
      {children}
    </main>
  );
};
```

- `flex-1`：确保主内容区占用所有可用空间
- `p-4 sm:p-6 md:p-8`：响应式内边距，在不同屏幕尺寸下自动调整
- `overflow-x-hidden`：禁用水平滚动
- `overflow-y-auto`：启用垂直滚动
- `bg-gray-100`：设置浅灰色背景，与其他布局元素形成视觉区分

## 使用示例

### 基础用法

```jsx
import Main from '../components/layout/components/Main.web';

const Content = () => {
  return (
    <Main>
      <div className="bg-white p-6 rounded-lg shadow-sm">
        <h1 className="text-2xl font-bold mb-4">控制面板</h1>
        <p>欢迎使用运营管理系统</p>
      </div>
    </Main>
  );
};

export default Content;
```

### 在布局中使用

```jsx
import Header from '../components/layout/components/Header.web';
import Main from '../components/layout/components/Main.web';
import Sidebar from '../components/layout/components/Sidebar.web';
import { appRoutes } from '../../router/routesConfig.web';

const Layout = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  return (
    <div className="flex h-screen">
      {/* 侧边栏 */}
      <Sidebar
        sidebarOpen={sidebarOpen}
        toggleSidebar={toggleSidebar}
        routes={appRoutes}
      />

      <div className="flex-1 flex flex-col">
        {/* 顶部导航栏 */}
        <Header toggleSidebar={toggleSidebar} routes={appRoutes} />

        {/* 内容区域 */}
        <Main>{children}</Main>
      </div>
    </div>
  );
};

export default Layout;
```

## 最佳实践

1. 在 `Main` 组件内部使用卡片式布局，提高内容的可读性
2. 避免在 `Main` 组件内部设置固定高度，以确保内容可以正常滚动
3. 使用 Tailwind CSS 的响应式类，确保内容在不同设备上都有良好的显示效果
