# 面包屑导航组件 (Breadcrumb)

## 组件概述

`Breadcrumb` 是一个面包屑导航组件，根据当前路径和路由配置自动生成面包屑导航。它使用递归方式处理任意深度的路由嵌套，支持动态路由参数和查询参数，为用户提供清晰的位置指示和便捷的导航体验。

## 文件位置

```
/components/layout/components/Breadcrumb.web.tsx
```

## 组件接口

```typescript
interface BreadcrumbProps {
  routes: AppRouteConfig[]; // 路由配置数组
}
```

## 功能特性

1. **递归处理路由嵌套**：
   - 支持任意深度的路由嵌套，不限于两层
   - 正确处理相对路径和绝对路径
   - 保留父子关系，用于构建面包屑链

2. **动态路由参数支持**：
   - 支持 `/path/:id` 形式的动态路由参数
   - 自动从当前 URL 中提取参数值
   - 支持任意数量的动态参数

3. **查询参数处理**：
   - 保留查询参数，确保导航链接完整
   - 在适当的导航项上添加查询参数

4. **父路径关联**：
   - 支持通过 `parentPath` 属性显式指定父路径
   - 自动构建完整的面包屑链

## 实现细节

### 面包屑配置生成

组件使用 `generateBreadcrumbConfigs` 函数从路由配置生成面包屑配置：

```jsx
const breadcrumbConfigs = useMemo(() => generateBreadcrumbConfigs(routes), [routes]);
```

### 面包屑项生成

组件使用 `generateBreadcrumbItems` 函数根据当前路径和面包屑配置生成面包屑项：

```jsx
const breadcrumbItems = useMemo(
  () => generateBreadcrumbItems(location.pathname, breadcrumbConfigs),
  [location.pathname, breadcrumbConfigs]
);
```

### 路径匹配

组件使用 `findMatchingConfig` 函数查找匹配当前路径的配置：

```jsx
const matchConfig = findMatchingConfig(pathname, breadcrumbConfigs);
```

### 面包屑链构建

组件使用 `buildBreadcrumbChain` 函数递归查找父路由，构建完整的面包屑链：

```jsx
const breadcrumbChain = buildBreadcrumbChain(matchConfig, breadcrumbConfigs);
```

## 使用示例

### 基础用法

```jsx
import Breadcrumb from '../components/layout/components/Breadcrumb.web';
import { appRoutes } from '../../router/routesConfig.web';

const Header = () => {
  return (
    <header className="bg-white shadow-sm">
      <div className="flex items-center h-16 px-4">
        <Breadcrumb routes={appRoutes} />
      </div>
    </header>
  );
};

export default Header;
```

## 注意事项

1. 确保路由配置中的 `path` 属性正确设置
2. 对于需要在面包屑中隐藏的路由，设置 `hideInBreadcrumb: true`
3. 对于需要显式指定父路径的路由，设置 `parentPath` 属性
4. 组件会自动处理动态路由参数，无需额外配置
