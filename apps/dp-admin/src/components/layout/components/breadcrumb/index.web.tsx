import { Breadcrumb as AntBreadcrumb } from 'antd';
import type {
  BreadcrumbItemType,
  BreadcrumbSeparatorType,
} from 'antd/es/breadcrumb/Breadcrumb';
import React, { useMemo } from 'react';
import { useLocation } from 'react-router-dom';

import type {
  AppRouteConfig,
  BreadcrumbConfig,
} from '../../../../router/core/types';

interface BreadcrumbProps {
  routes: AppRouteConfig[];
}

/**
 * 面包屑导航组件
 *
 * 根据当前路径和路由配置生成面包屑导航
 */
const Breadcrumb: React.FC<BreadcrumbProps> = ({ routes }) => {
  const location = useLocation();

  // 生成面包屑配置
  const breadcrumbConfigs = useMemo(
    () => generateBreadcrumbConfigs(routes),
    [routes]
  );

  // 生成面包屑项
  const breadcrumbItems = useMemo(
    () => generateBreadcrumbItems(location.pathname, breadcrumbConfigs),
    [location.pathname, breadcrumbConfigs]
  );

  // 如果没有面包屑项，则不渲染
  if (breadcrumbItems.length === 0) {
    return null;
  }

  return <AntBreadcrumb items={breadcrumbItems} />;
};

/**
 * 从路由配置生成面包屑配置
 * 使用递归方式处理任意深度的路由嵌套
 */
export const generateBreadcrumbConfigs = (
  routeConfigs: AppRouteConfig[]
): BreadcrumbConfig[] => {
  const breadcrumbConfigs: BreadcrumbConfig[] = [];

  // 递归处理路由配置
  const processRouteConfig = (
    config: AppRouteConfig,
    parentPath?: string,
    parentConfig?: AppRouteConfig
  ) => {
    // 完整路径
    const fullPath = config.path.startsWith('/')
      ? config.path
      : parentPath
        ? `${parentPath}/${config.path}`
        : `/${config.path}`;

    // 添加面包屑配置
    if (!config.hideInBreadcrumb) {
      breadcrumbConfigs.push({
        path: fullPath,
        title: config.title!,
        icon: config.icon,
        parentPath:
          config.parentPath || (parentConfig ? parentConfig.path : undefined),
      });
    }

    // 递归处理子路由
    if (config.children) {
      for (const child of config.children) {
        processRouteConfig(child, fullPath, config);
      }
    }
  };

  // 处理所有顶级路由
  for (const config of routeConfigs) {
    processRouteConfig(config);
  }

  return breadcrumbConfigs;
};

/**
 * 根据当前路径生成面包屑导航项目
 * 使用递归方式构建面包屑链
 */
export const generateBreadcrumbItems = (
  pathname: string,
  breadcrumbConfigs: BreadcrumbConfig[]
): Partial<BreadcrumbItemType & BreadcrumbSeparatorType>[] => {
  const items: Partial<BreadcrumbItemType & BreadcrumbSeparatorType>[] = [];

  // 查找匹配当前路径的配置
  const matchConfig = findMatchingConfig(pathname, breadcrumbConfigs);

  if (matchConfig) {
    // 构建面包屑路径链
    const breadcrumbChain = buildBreadcrumbChain(
      matchConfig,
      breadcrumbConfigs
    );

    // 将面包屑链转换为面包屑项
    for (let i = 0; i < breadcrumbChain.length; i++) {
      const config = breadcrumbChain[i];
      const isLast = i === breadcrumbChain.length - 1;

      // 处理链接
      let href: string | undefined;
      if (!isLast) {
        // 获取查询参数部分
        const queryString = pathname.includes('?')
          ? pathname.split('?')[1]
          : '';

        if (config.path.includes(':')) {
          // 如果路径包含动态参数，需要替换为实际值
          href = replacePathParams(config.path, pathname);
        } else {
          href = config.path;
        }

        // 如果有查询参数且当前配置是最后一个面包屑项的父项，则添加查询参数
        if (queryString && i === breadcrumbChain.length - 2) {
          href = `${href}?${queryString}`;
        }
      }

      // 添加面包屑项
      items.push({
        href,
        title: config.icon ? (
          <>
            {config.icon}
            <span className="ml-1">{config.title}</span>
          </>
        ) : (
          config.title
        ),
      });
    }
  }

  return items;
};

/**
 * 查找匹配当前路径的配置
 */
function findMatchingConfig(
  pathname: string,
  configs: BreadcrumbConfig[]
): BreadcrumbConfig | undefined {
  // 移除查询参数部分
  const pathWithoutQuery = pathname.split('?')[0];

  return configs.find((config) => {
    // 精确匹配
    if (config.path === pathWithoutQuery) {
      return true;
    }

    // 处理动态路由参数
    const configPathSegments = config.path.split('/').filter(Boolean);
    const pathSegments = pathWithoutQuery.split('/').filter(Boolean);

    // 路径段数量必须相同
    if (configPathSegments.length !== pathSegments.length) {
      return false;
    }

    // 逐段比较，支持动态参数
    return configPathSegments.every((segment, index) => {
      return segment.startsWith(':') || segment === pathSegments[index];
    });
  });
}

/**
 * 构建面包屑链
 * 递归查找父路由，构建完整的面包屑链
 */
function buildBreadcrumbChain(
  config: BreadcrumbConfig,
  configs: BreadcrumbConfig[]
): BreadcrumbConfig[] {
  const chain: BreadcrumbConfig[] = [config];

  // 递归查找父路由
  let currentConfig = config;
  while (currentConfig.parentPath) {
    const parentConfig = configs.find(
      (c) => c.path === currentConfig.parentPath
    );
    if (parentConfig) {
      chain.unshift(parentConfig);
      currentConfig = parentConfig;
    } else {
      break;
    }
  }

  return chain;
}

/**
 * 替换路径中的动态参数
 */
function replacePathParams(configPath: string, actualPath: string): string {
  const configSegments = configPath.split('/').filter(Boolean);
  const actualSegments = actualPath.split('/').filter(Boolean);

  if (configSegments.length !== actualSegments.length) {
    return configPath;
  }

  const resultSegments = configSegments.map((segment, index) => {
    return segment.startsWith(':') ? actualSegments[index] : segment;
  });

  return '/' + resultSegments.join('/');
}

export default Breadcrumb;
