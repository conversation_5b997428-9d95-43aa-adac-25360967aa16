import {
  LogoutOutlined,
  MenuUnfoldOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { Avatar, Dropdown } from 'antd';
import React from 'react';

import type { AppRouteConfig } from '../../../../router/core/types';
import { useHeaderActions } from '../../services/hooks';
import Breadcrumb from '../breadcrumb/index.web';

interface HeaderProps {
  toggleSidebar: () => void;
  routes: AppRouteConfig[]; // 使用路由配置代替面包屑项
}

/**
 * 顶部导航栏组件
 *
 * 提供侧边栏切换按钮、面包屑导航和用户信息
 */
const Header: React.FC<HeaderProps> = ({ toggleSidebar, routes }) => {
  // 使用自定义 hook 处理头部组件的操作
  const { handleLogout } = useHeaderActions();

  // 用户菜单项
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '個人資料',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登錄',
      onClick: handleLogout,
    },
  ];

  return (
    <header className="bg-white shadow-sm z-10">
      <div className="flex items-center h-14 sm:h-16 md:h-18 px-3 sm:px-4 md:px-5">
        {/* 侧边栏切换按钮 */}
        <button
          className="p-1 rounded-md text-gray-500 lg:hidden focus:outline-none focus:ring-2 focus:ring-gray-200 mr-3"
          onClick={toggleSidebar}
        >
          <MenuUnfoldOutlined className="text-base sm:text-lg md:text-xl" />
        </button>

        {/* 面包屑导航 */}
        <Breadcrumb routes={routes} />

        {/* 用户信息 - 使用 ml-auto 将其推到最右侧 */}
        <div className="flex items-center ml-auto">
          <Dropdown
            menu={{ items: userMenuItems }}
            placement="bottomRight"
            arrow
            trigger={['click']}
          >
            <div className="relative cursor-pointer">
              <div className="flex items-center focus:outline-none">
                <span className="mr-2 text-xs sm:text-sm font-medium text-gray-700 hidden md:block">
                  管理員
                </span>
                <Avatar
                  src="https://randomuser.me/api/portraits/men/32.jpg"
                  alt="用戶頭像"
                  className="w-6 h-6 sm:w-8 sm:h-8"
                  size="small"
                />
              </div>
            </div>
          </Dropdown>
        </div>
      </div>
    </header>
  );
};

export default Header;
