# 顶部导航栏组件 (Header)

## 组件概述

`Header` 是一个顶部导航栏组件，提供侧边栏切换按钮、面包屑导航和用户信息展示。它是布局系统的重要组成部分，为用户提供全局导航和快捷操作。

## 文件位置

```
/components/layout/components/Header.web.tsx
```

## 组件接口

```typescript
interface HeaderProps {
  toggleSidebar: () => void; // 侧边栏切换函数
  routes: AppRouteConfig[]; // 路由配置数组，用于面包屑导航
}
```

## 功能特性

1. **侧边栏切换**：
   - 在移动设备上提供侧边栏切换按钮
   - 点击按钮可展开或收起侧边栏
   - 在桌面设备上自动隐藏切换按钮

2. **面包屑导航**：
   - 集成面包屑导航组件
   - 根据当前路径自动生成导航链
   - 提供清晰的位置指示和快速返回功能

3. **用户信息展示**：
   - 显示用户头像和用户名
   - 提供下拉菜单，包含个人资料和退出登录选项
   - 在小屏幕上自动隐藏用户名，只显示头像

## 实现细节

### 侧边栏切换

组件接收 `toggleSidebar` 函数作为属性，用于切换侧边栏的显示状态：

```jsx
<button
  className="p-1 rounded-md text-gray-500 lg:hidden focus:outline-none focus:ring-2 focus:ring-gray-200 mr-3"
  onClick={toggleSidebar}
>
  <MenuUnfoldOutlined className="text-base sm:text-lg md:text-xl" />
</button>
```

### 面包屑导航

组件使用 `Breadcrumb` 组件显示面包屑导航：

```jsx
<Breadcrumb routes={routes} />
```

### 用户信息和下拉菜单

组件使用 Ant Design 的 `Dropdown` 和 `Avatar` 组件显示用户信息和下拉菜单：

```jsx
<Dropdown
  menu={{ items: userMenuItems }}
  placement="bottomRight"
  arrow
  trigger={['click']}
>
  <div className="relative cursor-pointer">
    <div className="flex items-center focus:outline-none">
      <span className="mr-2 text-xs sm:text-sm font-medium text-gray-700 hidden md:block">
        管理員
      </span>
      <Avatar
        src="https://randomuser.me/api/portraits/men/32.jpg"
        alt="用戶頭像"
        className="w-6 h-6 sm:w-8 sm:h-8"
        size="small"
      />
    </div>
  </div>
</Dropdown>
```

## 使用示例

### 基础用法

```jsx
import Header from '../components/layout/components/Header.web';
import { appRoutes } from '../../router/routesConfig.web';

const Layout = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  return (
    <div className="flex h-screen">
      {/* 侧边栏 */}
      <Sidebar sidebarOpen={sidebarOpen} toggleSidebar={toggleSidebar} routes={appRoutes} />

      <div className="flex-1 flex flex-col">
        {/* 顶部导航栏 */}
        <Header toggleSidebar={toggleSidebar} routes={appRoutes} />

        {/* 内容区域 */}
        <Main>{children}</Main>
      </div>
    </div>
  );
};

export default Layout;
```

## 响应式设计

组件在不同屏幕尺寸下的表现：

- **移动端 (< 1024px)**：
  - 显示侧边栏切换按钮
  - 隐藏用户名称，只显示头像

- **桌面端 (>= 1024px)**：
  - 隐藏侧边栏切换按钮
  - 显示完整的用户信息
