import { CloseOutlined } from '@ant-design/icons';
import type { MenuProps } from 'antd';
import { Menu } from 'antd';
import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

// 导入 AppRouteConfig 类型，但不导入实际的路由配置
import type { AppRouteConfig } from '@/router/core/types';

interface SidebarProps {
  sidebarOpen: boolean;
  toggleSidebar: () => void;
  routes: AppRouteConfig[]; // 通过属性接收路由配置
}

type MenuItem = Required<MenuProps>['items'][number];

/**
 * 侧边栏组件
 *
 * 提供导航菜单和品牌展示区域
 */
const Sidebar: React.FC<SidebarProps> = ({
  sidebarOpen,
  toggleSidebar,
  routes,
}) => {
  const location = useLocation();
  const navigate = useNavigate();
  // 初始化为空数组，稍后在 useEffect 中设置
  const [openKeys, setOpenKeys] = useState<string[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);

  // 根据当前路径设置选中的菜单项和展开的菜单
  useEffect(() => {
    const path = location.pathname;
    const highlightPath = findHighlightPath(routes, path);
    if (highlightPath) {
      setSelectedKeys([highlightPath]);
    } else {
      // 设置选中的菜单项
      setSelectedKeys([path]);
    }
    // 根据当前路径自动设置展开的菜单
    // 例如，如果路径是 /merchant/check-in，则展开 /merchant 菜单
    const pathSegments = path.split('/').filter(Boolean);
    if (pathSegments.length > 0) {
      // 构建父菜单路径
      const parentPath = `/${pathSegments[0]}`;
      setOpenKeys([parentPath]);
    }
  }, [location.pathname]);

  // 处理菜单项点击
  const handleMenuClick: MenuProps['onClick'] = (info) => {
    // 直接使用 key 作为路径，不进行转换
    navigate(info.key);

    // 移动端点击导航项后自动关闭侧边栏
    if (window.innerWidth < 1024) {
      toggleSidebar();
    }
  };

  //递归找到对应的heightMenu
  const findHighlightPath = (
    routes: AppRouteConfig[],
    path: string
  ): string | undefined => {
    for (const route of routes) {
      if (route.highlightMenu === path) {
        return route.highlightMenu;
      }
      if (route.children) {
        const childPath = findHighlightPath(route.children, path);
        if (childPath) {
          return childPath;
        }
      }
    }
    return undefined;
  };

  // 处理子菜单展开/收起
  const handleOpenChange: MenuProps['onOpenChange'] = (keys) => {
    setOpenKeys(keys);
  };

  // 构建菜单项
  const getMenuItem = (
    label: React.ReactNode,
    key: React.Key,
    icon?: React.ReactNode,
    children?: MenuItem[],
    type?: 'group'
  ): MenuItem => {
    return {
      key,
      icon,
      children,
      label,
      type,
    } as MenuItem;
  };

  /**
   * 判断路由是否应该显示在菜单中
   * @param route 路由配置
   * @returns 是否应该显示在菜单中
   */
  const shouldShowInMenu = (route: AppRouteConfig): boolean => {
    // 排除特定路径
    const excludePaths = ['/login', '*', '/'];
    if (excludePaths.includes(route.path)) {
      return false;
    }

    // 排除设置了 hideInBreadcrumb 或 hideInMenu 的路由
    if (route.hideInBreadcrumb || route.hideInMenu) {
      return false;
    }

    return true;
  };

  /**
   * 递归处理路由配置，生成菜单项
   * @param routeConfigs 路由配置数组
   * @returns 菜单项数组
   */
  const processRoutes = (routeConfigs: AppRouteConfig[]): MenuItem[] => {
    // 过滤掉不应该显示在菜单中的路由
    const filteredRoutes = routeConfigs.filter(shouldShowInMenu);

    // 递归处理每个路由，生成菜单项
    return filteredRoutes.map(createMenuItem);
  };

  /**
   * 根据路由配置创建菜单项
   * @param route 路由配置
   * @returns 菜单项
   */
  const createMenuItem = (route: AppRouteConfig): MenuItem => {
    // 使用路径作为 key
    const key = route.key || route.path;

    // 处理子路由
    let children: MenuItem[] | undefined;
    if (route.children && route.children.length > 0) {
      // 递归处理子路由
      const childItems = processRoutes(route.children);

      // 只有当有可见的子路由时，才设置 children
      if (childItems.length > 0) {
        children = childItems;
      }
    }

    // 创建并返回菜单项
    return getMenuItem(route.title, key, route.icon, children);
  };

  /**
   * 从路由配置生成菜单项
   * 这是整个菜单生成过程的入口函数
   * @returns 菜单项数组
   */
  const generateMenuItems = (): MenuItem[] => {
    // 处理顶级路由
    return processRoutes(routes);
  };

  // 菜单项配置
  const items: MenuItem[] = generateMenuItems();

  return (
    <div
      className={`fixed inset-y-0 left-0 z-30 w-64 bg-gray-900 shadow-lg transform transition-transform duration-300 ease-in-out lg:relative lg:translate-x-0 ${
        sidebarOpen ? 'translate-x-0' : '-translate-x-full'
      }`}
    >
      {/* 品牌区域 */}
      <div className="flex items-center justify-between h-14 sm:h-16 md:h-18 px-3 sm:px-4 md:px-5 bg-gray-800 text-white">
        <div className="flex items-center">
          <span className="text-base sm:text-lg md:text-xl font-semibold">
            運營管理系統
          </span>
        </div>
        <button
          className="p-1 rounded-md lg:hidden focus:outline-none focus:ring-2 focus:ring-white"
          onClick={toggleSidebar}
        >
          <CloseOutlined className="text-base sm:text-lg md:text-xl" />
        </button>
      </div>

      {/* 导航菜单 */}
      <div className="mt-3 sm:mt-4 md:mt-5">
        <Menu
          mode="inline"
          theme="dark"
          openKeys={openKeys}
          selectedKeys={selectedKeys}
          onClick={handleMenuClick}
          onOpenChange={handleOpenChange}
          items={items}
          style={{
            borderRight: 'none',
          }}
        />
      </div>
    </div>
  );
};

export default Sidebar;
