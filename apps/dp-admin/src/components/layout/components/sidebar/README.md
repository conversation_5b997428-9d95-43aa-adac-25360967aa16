# 侧边栏导航组件 (Sidebar)

## 组件概述

`Sidebar` 是一个侧边栏导航组件，提供深色主题的导航菜单和品牌展示区域。它根据路由配置自动生成菜单项，支持多级菜单结构，并提供当前页面高亮显示功能。

## 文件位置

```
/components/layout/components/Sidebar.web.tsx
```

## 组件接口

```typescript
interface SidebarProps {
  sidebarOpen: boolean; // 侧边栏是否打开
  toggleSidebar: () => void; // 侧边栏切换函数
  routes: AppRouteConfig[]; // 路由配置数组
}
```

## 功能特性

1. **自动生成菜单**：
   - 根据路由配置自动生成菜单项
   - 支持多级菜单结构
   - 过滤掉不需要显示的路由（如登录页、404页面）

2. **当前页面高亮**：
   - 根据当前路径自动高亮显示对应的菜单项
   - 自动展开包含当前页面的父菜单

3. **响应式设计**：
   - 在移动设备上可折叠显示
   - 在桌面设备上常驻显示
   - 提供关闭按钮，方便移动端操作

4. **品牌展示**：
   - 顶部显示系统名称
   - 可扩展添加Logo

## 实现细节

### 状态管理

组件使用 React 的 `useState` 钩子管理菜单的展开和选中状态：

```jsx
const [openKeys, setOpenKeys] = useState<string[]>([]);
const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
```

### 路径监听

组件使用 React Router 的 `useLocation` 钩子监听路径变化，自动更新菜单状态：

```jsx
useEffect(() => {
  const path = location.pathname;

  // 设置选中的菜单项
  setSelectedKeys([path]);

  // 根据当前路径自动设置展开的菜单
  const pathSegments = path.split('/').filter(Boolean);
  if (pathSegments.length > 0) {
    const parentPath = `/${pathSegments[0]}`;
    setOpenKeys([parentPath]);
  }
}, [location.pathname]);
```

### 菜单项生成

组件使用 `generateMenuItems` 函数从路由配置生成菜单项：

```jsx
const generateMenuItems = (): MenuItem[] => {
  const items: MenuItem[] = [];

  // 过滤掉登录页、通配符路由和设置了 hideInMenu 的路由
  const menuRoutes = routes.filter(
    (route: AppRouteConfig) =>
      route.path !== '/login' && route.path !== '*' && !route.hideInBreadcrumb && !route.hideInMenu
  );

  // 生成菜单项...
};
```

### 菜单交互

组件处理菜单项点击和子菜单展开/收起：

```jsx
const handleMenuClick: MenuProps['onClick'] = (info) => {
  navigate(info.key);

  // 移动端点击导航项后自动关闭侧边栏
  if (window.innerWidth < 1024) {
    toggleSidebar();
  }
};

const handleOpenChange: MenuProps['onOpenChange'] = (keys) => {
  setOpenKeys(keys);
};
```

## 使用示例

### 基础用法

```jsx
import Sidebar from '../components/layout/components/Sidebar.web';
import { appRoutes } from '../../router/routesConfig.web';

const Layout = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  return (
    <div className="flex h-screen">
      {/* 侧边栏 */}
      <Sidebar
        sidebarOpen={sidebarOpen}
        toggleSidebar={toggleSidebar}
        routes={appRoutes}
      />

      {/* 主内容区 */}
      <div className="flex-1">
        {/* 内容... */}
      </div>
    </div>
  );
};

export default Layout;
```

## 响应式设计

组件在不同屏幕尺寸下的表现：

- **移动端 (< 1024px)**：
  - 侧边栏默认隐藏，可通过按钮切换显示
  - 点击导航项后自动关闭侧边栏
  - 显示关闭按钮

- **桌面端 (>= 1024px)**：
  - 侧边栏常驻显示
  - 隐藏关闭按钮
