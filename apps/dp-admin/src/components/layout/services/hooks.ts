import { useCallback } from 'react';
import { useNavigate } from 'react-router-dom';

import { useAuth } from '../../../contexts/AuthContext';

/**
 * Header 组件的操作 Hook
 *
 * 提供头部组件需要的各种操作，如退出登录
 */
export const useHeaderActions = () => {
  const navigate = useNavigate();
  const { clearAuthState } = useAuth();

  /**
   * 处理退出登录
   *
   * 清除认证状态并跳转到登录页面
   */
  const handleLogout = useCallback(() => {
    // 清除认证状态
    clearAuthState();

    // 导航到登录页面
    navigate('/login');

    // 可以添加退出成功的提示
    // message.success('已成功退出登录');
  }, [clearAuthState, navigate]);

  return {
    handleLogout,
  };
};
