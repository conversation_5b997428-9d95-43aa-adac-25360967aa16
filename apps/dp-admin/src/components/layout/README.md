# 现代化管理后台布局组件 (Layout)

## 组件概述

`Layout` 是一个现代化的管理后台布局组件，为应用提供专业、美观且一致的页面结构。它包含深色主题的侧边栏导航、功能丰富的顶部导航栏和灵活的主内容区域，打造出符合当代设计趋势的管理系统界面。该组件完全响应式，在移动设备和桌面设备上均能提供出色的用户体验。

## 文件结构

```
/components/layout/
├── components/
│   ├── Breadcrumb.web.tsx  // 面包屑导航组件
│   ├── Header.web.tsx      // 顶部导航栏组件
│   ├── Main.web.tsx        // 主内容区域组件
│   └── Sidebar.web.tsx     // 侧边栏导航组件
└── index.web.tsx           // 主布局组件
```

## 组件接口

```typescript
interface LayoutProps {
  children: React.ReactNode;  // 子组件，将被渲染在主内容区
}
```

## 功能特性

1. **现代化深色侧边栏**：
   - 优雅的深色主题设计，减少视觉疲劳
   - 品牌标识区域，展示系统名称和Logo
   - 分类清晰的导航菜单，支持图标和文字组合
   - 当前页面高亮显示，提供直观的位置指示

2. **功能丰富的顶部导航栏**：
   - 简洁的搜索功能入口
   - 通知中心，显示系统消息和提醒
   - 用户信息展示，包含头像和下拉菜单
   - 快捷功能按钮，如全屏切换、设置等

3. **智能响应式布局**：
   - 桌面端：侧边栏常驻显示，提供完整导航体验
   - 平板端：可折叠侧边栏，最大化内容显示区域
   - 移动端：侧边栏默认隐藏，通过手势或按钮调出

4. **流畅交互体验**：
   - 平滑的侧边栏切换动画，提升用户体验
   - 智能导航行为，移动端自动关闭侧边栏
   - 遮罩层交互，点击即可关闭弹出菜单
   - 页面切换过渡效果，减少视觉跳跃感

5. **灵活的内容区域**：
   - 卡片式内容布局，界面整洁有序
   - 自适应内容容器，优化不同设备的显示效果
   - 滚动优化，确保长内容浏览流畅

## 实现细节

### 状态管理

组件使用 React 的 `useState` 钩子管理侧边栏的显示状态：

```jsx
const [sidebarOpen, setSidebarOpen] = React.useState(false);

const toggleSidebar = () => {
  setSidebarOpen(!sidebarOpen);
};
```

### 导航项配置

导航菜单项通过数组配置，便于扩展和修改：

```jsx
const navItems = [
  { path: '/', label: '首页', icon: '📊' },
  { path: '/users', label: '用户管理', icon: '👥' },
  { path: '/statistics', label: '数据统计', icon: '📈' },
];
```

### 当前页面高亮

使用 React Router 的 `useLocation` 钩子获取当前路径，用于高亮显示当前页面的导航项：

```jsx
const location = useLocation();
const currentPath = location.pathname;

// 在渲染导航项时使用
<Link
  to={item.path}
  className={`flex items-center p-x-4 p-y-2 text-gray-700 ${
    currentPath === item.path
      ? 'bg-blue-50 text-blue-600 border-r-4 border-blue-600'
      : 'hover:bg-gray-50'
  }`}
>
```

### 响应式侧边栏

侧边栏在移动端和桌面端有不同的行为：

```jsx
<div
  className={`fixed inset-y-0 left-0 z-30 w-64 bg-white shadow-md transform transition-transform duration-300 ease-in-out lg:relative lg:translate-x-0 ${
    sidebarOpen ? 'translate-x-0' : '-translate-x-full'
  }`}
>
```

- 使用 CSS transform 实现侧边栏的滑入滑出效果
- 使用 Tailwind 的 `lg:` 前缀实现响应式行为
- 在大屏幕上（lg 及以上），侧边栏始终显示
- 在小屏幕上，侧边栏默认隐藏，可通过按钮切换显示

### 移动端遮罩层

当侧边栏在移动端打开时，添加一个遮罩层，点击可关闭侧边栏：

```jsx
{sidebarOpen && (
  <div
    className="fixed inset-0 z-20 bg-black bg-opacity-50 lg:hidden"
    onClick={toggleSidebar}
  ></div>
)}
```

### 主内容区

主内容区使用 Flexbox 布局，确保内容可以滚动：

```jsx
<main className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100">
  {children}
</main>
```

## 布局结构

组件采用现代化的分区布局，提供清晰的视觉层次和直观的导航体验：

```
Layout (现代化管理后台布局)
├── 侧边栏遮罩层 (移动端专用)
│   └── 半透明黑色背景，点击关闭侧边栏
│
├── 深色主题侧边栏
│   ├── 品牌区域 (Logo + 系统名称)
│   ├── 导航菜单
│   │   ├── 一级导航项 (图标 + 文字)
│   │   └── 当前页面高亮指示
│   └── 侧边栏关闭按钮 (移动端)
│
└── 主内容容器
    ├── 顶部导航栏
    │   ├── 侧边栏切换按钮 (移动端)
    │   ├── 搜索区域
    │   ├── 通知中心
    │   └── 用户信息 (头像 + 下拉菜单)
    │
    └── 内容区域 (children)
        └── 卡片式内容布局
```

## 使用示例

### 基础用法

```jsx
import Layout from '../components/layout/index.web';

const HomePage = () => {
  return (
    <Layout>
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-4">控制面板</h1>
        <p>欢迎使用运营管理系统</p>
      </div>
    </Layout>
  );
};

export default HomePage;
```

### 卡片式内容布局

```jsx
import Layout from '../components/layout/index.web';
import { Card, Row, Col } from 'antd';

const DashboardPage = () => {
  return (
    <Layout>
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-6">控制面板</h1>

        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} lg={6}>
            <Card title="总用户数" bordered={false} className="shadow-sm">
              <p className="text-3xl font-semibold">8,846</p>
              <p className="text-green-500">↑ 12% 较上月</p>
            </Card>
          </Col>

          <Col xs={24} sm={12} lg={6}>
            <Card title="活跃用户" bordered={false} className="shadow-sm">
              <p className="text-3xl font-semibold">2,317</p>
              <p className="text-green-500">↑ 5% 较上月</p>
            </Card>
          </Col>

          <Col xs={24} sm={12} lg={6}>
            <Card title="订单总数" bordered={false} className="shadow-sm">
              <p className="text-3xl font-semibold">1,203</p>
              <p className="text-red-500">↓ 2% 较上月</p>
            </Card>
          </Col>

          <Col xs={24} sm={12} lg={6}>
            <Card title="总收入" bordered={false} className="shadow-sm">
              <p className="text-3xl font-semibold">¥93,438</p>
              <p className="text-green-500">↑ 8% 较上月</p>
            </Card>
          </Col>
        </Row>
      </div>
    </Layout>
  );
};
```

## 样式特点

- 使用 Tailwind CSS 实现响应式设计
- 使用 Flexbox 布局实现灵活的页面结构
- 使用 CSS transform 和 transition 实现平滑的动画效果
- 使用 z-index 管理元素的层叠顺序

## 响应式设计

组件在不同屏幕尺寸下的表现：

- **移动端 (< 1024px)**：
  - 侧边栏默认隐藏，可通过按钮切换显示
  - 点击导航项后自动关闭侧边栏
  - 顶部导航栏显示侧边栏切换按钮
  - 用户名称在小屏幕上隐藏，只显示头像

- **桌面端 (>= 1024px)**：
  - 侧边栏常驻显示
  - 不显示侧边栏切换按钮和遮罩层
  - 顶部导航栏显示完整的用户信息

## 可访问性

- 使用语义化的 HTML 元素
- 按钮元素可通过键盘访问
- 导航菜单使用 `<nav>` 和 `<ul>/<li>` 元素，符合 HTML 语义

## 未来扩展计划

### 主题与个性化

- **深色模式支持**：提供完整的深色主题，减少夜间使用时的视觉疲劳
- **主题定制**：允许用户选择主题色和界面风格
- **布局个性化**：支持用户自定义布局偏好和保存设置

### 导航增强

- **可折叠侧边栏**：支持完全展开、仅显示图标和完全折叠三种模式
- **多级导航菜单**：支持无限层级的导航结构，适应复杂系统需求
- **面包屑导航**：提供清晰的位置指示和快速返回功能
- **快捷导航**：支持用户自定义常用功能的快捷入口

### 交互优化

- **拖拽调整**：允许用户通过拖拽调整侧边栏宽度
- **手势支持**：在触摸设备上支持滑动手势操作侧边栏
- **键盘导航增强**：提供更完善的键盘快捷键支持
- **动画自定义**：允许用户调整或关闭界面动画效果

### 性能与可访问性

- **代码分割**：优化组件加载性能，减少初始加载时间
- **可访问性增强**：完全符合 WCAG 2.1 AA 级标准
- **屏幕阅读器优化**：提供更好的屏幕阅读器支持和语音导航
- **高对比度模式**：为视力障碍用户提供高对比度界面选项
