import React, { useState } from 'react';
import { Outlet } from 'react-router-dom';

import { appRoutes } from '../../router/config/routes.web';
import Header from './components/header/index.web';
import Main from './components/main/index.web';
import Sidebar from './components/sidebar/index.web';

/**
 * 现代化管理后台布局组件
 *
 * 提供深色侧边栏、顶部导航栏和主内容区域的整体布局
 * 支持响应式设计，在移动设备上自动调整布局
 * 使用 Outlet 组件渲染子路由
 */
const Layout: React.FC = () => {
  // 状态管理
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // 切换侧边栏显示状态
  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  return (
    <div className="w-full flex h-screen bg-gray-100">
      {/* 移动端侧边栏遮罩层 */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-20 bg-black bg-opacity-50 lg:hidden"
          onClick={toggleSidebar}
        ></div>
      )}

      {/* 侧边栏 */}
      <Sidebar
        sidebarOpen={sidebarOpen}
        toggleSidebar={toggleSidebar}
        routes={appRoutes}
      />

      {/* 主内容区 */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* 顶部导航栏 */}
        <Header toggleSidebar={toggleSidebar} routes={appRoutes} />

        {/* 内容区域 - 使用 Outlet 渲染子路由 */}
        <Main>
          <Outlet />
        </Main>
      </div>
    </div>
  );
};

export default Layout;
