# 组件库文档

本文档提供了运营管理系统中全局共享组件的概述和使用指南。这些组件被设计为可复用的 UI 元素，可以在整个应用程序中使用。

## 目录结构

```
/components
├── Layout.web.tsx       # 应用布局组件
├── Layout.md            # 布局组件文档
└── README.md            # 本文档
```

## 组件概述

### 布局组件 (Layout)

`Layout` 是一个页面布局组件，为应用提供一致的页面结构，包括响应式侧边栏导航、顶部导航栏和主内容区域。

**主要特性**：

- 响应式设计，适配移动端和桌面端
- 侧边栏导航菜单，支持多个导航项
- 当前页面高亮显示
- 移动端侧边栏切换功能

**详细文档**：[Layout.md](./Layout.md)

## 使用指南

### 组件命名约定

- 所有组件文件使用 PascalCase 命名
- Web 特定组件使用 `.web.tsx` 后缀
- 组件文档使用与组件同名的 `.md` 文件

### 导入组件

```jsx
// 导入布局组件
import Layout from '../components/Layout.web';

```

### 组件使用示例

#### 布局组件

```jsx
import Layout from '../components/Layout.web';

const HomePage = () => {
  return (
    <Layout>
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-4">首页</h1>
        <p>欢迎使用运营管理系统</p>
      </div>
    </Layout>
  );
};
```
## 设计原则

### 1. 一致性

所有组件遵循一致的设计语言和交互模式，确保用户体验的一致性。

### 2. 可复用性

组件设计为高度可复用，可以在应用的不同部分使用相同的组件。

### 3. 响应式

组件支持响应式设计，可以在不同屏幕尺寸上提供良好的用户体验。

### 4. 可访问性

组件遵循可访问性最佳实践，确保所有用户都能使用应用。

### 5. 类型安全

所有组件使用 TypeScript 开发，提供类型安全和更好的开发体验。

## 技术栈

- **React**: 用于构建用户界面
- **TypeScript**: 提供类型安全
- **Tailwind CSS**: 用于样式和响应式设计
- **React Strict DOM**: 用于创建跨平台兼容的组件

## 贡献指南

### 添加新组件

1. 在 `/components` 目录下创建新的组件文件，使用 `.web.tsx` 后缀
2. 为组件创建详细的文档，使用与组件同名的 `.md` 文件
3. 在本 README 文件中添加组件的简要描述

### 修改现有组件

1. 确保保持向后兼容性，避免破坏现有使用
2. 更新组件的文档，反映最新的变化
3. 如果有重大变更，请在本 README 文件中注明

## 未来计划

- 添加更多基础组件（按钮、选择器、复选框等）
- 实现表单组件库
- 添加数据展示组件（表格、图表等）
- 支持深色模式
- 实现组件测试
