import type { PageConfig } from '@dp-frontend/navigation';

// 导入页面组件
import MerchantCheckIn from '../pages/merchant/check-in/index.web';

// 定义页面配置
export const pageConfigs: PageConfig[] = [
  {
    name: 'Main',
    component: MerchantCheckIn, // 临时使用MerchantCheckIn组件，实际上这个组件不会被直接渲染
    type: 'stack',
    options: {
      title: '运营端',
      headerShown: false,
      path: '',
    },
    isInitial: true,
    children: [
      {
        name: 'MerchantCheckIn',
        component: MerchantCheckIn,
        type: 'tab',
        options: {
          title: '商户管理',
          tabBarLabel: '商户管理',
          headerTitleAlign: 'center',
          path: 'merchant/check-in',
        },
      },
    ],
  },
];
