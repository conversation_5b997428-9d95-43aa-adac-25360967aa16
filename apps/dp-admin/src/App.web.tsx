// import 'antd/dist/reset.css';
import 'dayjs/locale/zh-tw';

import { ConfigProvider } from 'antd';
import zhTW from 'antd/locale/zh_TW';
import dayjs from 'dayjs';
import React from 'react';
import { BrowserRouter as Router } from 'react-router-dom';

import { AuthProvider } from './contexts/AuthContext';
import { AppRouter } from './router';
dayjs.locale('zh-tw');

/**
 * 应用主组件
 */
const App: React.FC = () => {
  return (
    <ConfigProvider locale={zhTW}>
      <Router
        future={{
          v7_startTransition: true,
          v7_relativeSplatPath: true,
        }}
      >
        <AuthProvider>
          <AppRouter />
        </AuthProvider>
      </Router>
    </ConfigProvider>
  );
};

export default App;
