import {
  AuditOutlined,
  BankOutlined,
  FileSearchOutlined,
  HomeOutlined,
  SafetyOutlined,
  ShopOutlined,
  TeamOutlined,
  UnorderedListOutlined,
  UsergroupAddOutlined,
  UserOutlined,
} from '@ant-design/icons';
import React from 'react';

// import React, { lazy, Suspense } from 'react';
// import type { RouteObject } from 'react-router-dom';
// import { Navigate } from 'react-router-dom';
// import Layout from '../../components/layout/index.web';
// import { RequireAuth } from '../core/RequireAuth';
import type { AppRouteConfig } from '../core/types';

// // 使用懒加载导入组件
// const Login = lazy(() => import('../../pages/login/index.web'));
// const AuditDetail = lazy(
//   () => import('../../pages/merchant/audit-detail/index.web')
// );
// const MerchantCheckIn = lazy(
//   () => import('../../pages/merchant/check-in/index.web')
// );
// const NotFound = lazy(() => import('../../pages/error/index.web'));

// // 加载指示器组件
// const LoadingComponent = () => (
//   <div className="flex-cc w-full h-full">loading...</div>
// );

export const appRoutes: AppRouteConfig[] = [
  // 登录页面
  {
    path: '/login',
    element: 'Login',
    title: '登錄',
    hideInBreadcrumb: true,
  },

  // 首页（重定向到商户入住审核页面）
  {
    path: '/',
    isRedirect: true,
    redirectTo: '/merchant/check-in',
    title: '首頁',
    icon: <HomeOutlined />,
  },

  // 商户管理
  {
    path: '/merchant',
    isRedirect: true,
    redirectTo: '/merchant/check-in',
    title: '商戶管理',
    icon: <ShopOutlined />,
    element: 'Layout',
    children: [
      {
        path: 'check-in',
        element: 'MerchantCheckIn',
        title: '入住審核',
        icon: <AuditOutlined />,
        protected: true,
        key: '/merchant/check-in',
      },
      {
        path: 'task-list',
        element: 'TaskList',
        title: '任務管理',
        icon: <UnorderedListOutlined />,
        protected: true,
        key: '/merchant/task-list',
      },
      {
        path: 'merchant-list',
        element: 'MerchantList',
        title: '任務管理',
        icon: <BankOutlined />,
        protected: true,
        key: '/merchant/merchant-list',
      },
      // 审核详情页面（路径参数版本）
      {
        path: 'audit-detail/:id',
        element: 'AuditDetail',
        title: '審核詳情',
        icon: <FileSearchOutlined />,
        protected: true,
        hideInMenu: true,
        parentPath: '/merchant/check-in', // 用于面包屑导航
        highlightMenu: '/merchant/check-in', // 用于侧边栏菜单高亮
        key: '/merchant/audit-detail',
      },
    ],
  },

  // 用户管理
  {
    path: '/users',
    title: '用戶管理',
    icon: <TeamOutlined />,
    element: 'Layout',
    children: [
      {
        path: 'roles',
        element: 'RoleManagement',
        title: '角色管理',
        icon: <UserOutlined />,
        protected: true,
        key: '/users/roles',
      },
      {
        path: 'user-tasks',
        element: 'UserAccountManagement',
        title: '帳號管理',
        icon: <UsergroupAddOutlined />,
        protected: true,
        key: '/users/user-tasks',
      },
      {
        path: 'permissions',
        element: 'PermissionManagement',
        title: '權限管理',
        icon: <SafetyOutlined />,
        protected: true,
        key: '/users/permissions',
      },
    ],
  },

  // 404 页面
  {
    path: '*',
    element: 'NotFound',
    title: '404',
    hideInBreadcrumb: true,
  },
];

/**
 * 应用路由配置
 *
 * 这是唯一需要修改的文件，当需要添加或修改路由时
 * 系统会自动处理路由生成、面包屑导航和权限控制
 */
// 原始路由配置，用于菜单和面包屑导航
// export const appRoutes: AppRouteConfig[] = [
//   // 登录页面
//   {
//     path: '/login',
//     element: (
//       <Suspense fallback={<LoadingComponent />}>
//         <Login />
//       </Suspense>
//     ),
//     title: '登錄',
//     hideInBreadcrumb: true,
//   },

//   // 首页（重定向到商户入住审核页面）
//   {
//     path: '/',
//     isRedirect: true,
//     redirectTo: '/merchant/check-in',
//     title: '首頁',
//     icon: <HomeOutlined />,
//   },

//   // 商户管理
//   {
//     path: '/merchant',
//     isRedirect: true,
//     redirectTo: '/merchant/check-in',
//     title: '商戶管理',
//     icon: <ShopOutlined />,
//     element: (
//       <RequireAuth>
//         <Layout />
//       </RequireAuth>
//     ),
//     children: [
//       {
//         path: '/merchant/check-in',
//         element: (
//           <Suspense fallback={<LoadingComponent />}>
//             <MerchantCheckIn />
//           </Suspense>
//         ),
//         title: '入住審核',
//         icon: <AuditOutlined />,
//         protected: true,
//       },
//       // 审核详情页面（路径参数版本）
//       {
//         path: '/merchant/audit-detail/:id',
//         element: (
//           <Suspense fallback={<LoadingComponent />}>
//             <AuditDetail />
//           </Suspense>
//         ),
//         title: '審核詳情',
//         icon: <FileSearchOutlined />,
//         protected: true,
//         hideInMenu: true,
//         parentPath: '/merchant/check-in', // 用于面包屑导航
//         highlightMenu: '/merchant/check-in', // 用于侧边栏菜单高亮
//       },
//     ],
//   },

//   // 404 页面
//   {
//     path: '*',
//     element: (
//       <Suspense fallback={<LoadingComponent />}>
//         <NotFound />
//       </Suspense>
//     ),
//     title: '404',
//     hideInBreadcrumb: true,
//   },
// ];

// 直接提供给 React Router 使用的路由配置，使用嵌套结构
// export const routerRoutes: RouteObject[] = [
//   // 登录页面
//   {
//     path: '/login',
//     element: (
//       <Suspense fallback={<LoadingComponent />}>
//         <Login />
//       </Suspense>
//     ),
//   },

//   // 首页（重定向到商户入住审核页面）
//   {
//     path: '/',
//     element: <Navigate to="/merchant/check-in" replace />,
//   },

//   // 商户管理（父路由）
//   {
//     path: '/merchant',
//     element: (
//       <RequireAuth>
//         <Layout />
//       </RequireAuth>
//     ),
//     children: [
//       // 索引路由，当访问 /merchant 时重定向到 /merchant/check-in
//       {
//         index: true,
//         element: <Navigate to="/merchant/check-in" replace />,
//       },
//       // 入住审核（子路由）
//       {
//         path: 'check-in', // 相对路径，最终为 /merchant/check-in
//         element: (
//           <Suspense fallback={<LoadingComponent />}>
//             <MerchantCheckIn />
//           </Suspense>
//         ),
//       },
//       // 审核详情（子路由）
//       {
//         path: 'audit-detail/:id', // 相对路径，最终为 /merchant/audit-detail/:id
//         element: (
//           <Suspense fallback={<LoadingComponent />}>
//             <AuditDetail />
//           </Suspense>
//         ),
//       },
//     ],
//   },

//   // 404 页面
//   {
//     path: '*',
//     element: (
//       <Suspense fallback={<LoadingComponent />}>
//         <NotFound />
//       </Suspense>
//     ),
//   },
// ];
