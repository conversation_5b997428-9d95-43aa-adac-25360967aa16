import {
  AuditOutlined,
  BankOutlined,
  FileSearchOutlined,
  HomeOutlined,
  SafetyOutlined,
  ShopOutlined,
  TeamOutlined,
  UnorderedListOutlined,
  UsergroupAddOutlined,
  UserOutlined,
} from '@ant-design/icons';
import React from 'react';

import type { AppRouteConfig } from '../core/types';

export const appRoutes: AppRouteConfig[] = [
  // 登录页面
  {
    path: '/login',
    element: 'Login',
    title: '登錄',
    hideInBreadcrumb: true,
  },

  // 首页（重定向到商户入住审核页面）
  {
    path: '/',
    isRedirect: true,
    redirectTo: '/merchant/check-in',
    title: '首頁',
    icon: <HomeOutlined />,
  },

  // 商户管理
  {
    path: '/merchant',
    isRedirect: true,
    redirectTo: '/merchant/check-in',
    title: '商戶管理',
    icon: <ShopOutlined />,
    element: 'Layout',
    children: [
      {
        path: 'check-in',
        element: 'MerchantCheckIn',
        title: '入住審核',
        icon: <AuditOutlined />,
        protected: true,
        key: '/merchant/check-in',
      },
      {
        path: 'task-list',
        element: 'TaskList',
        title: '任務管理',
        icon: <UnorderedListOutlined />,
        protected: true,
        key: '/merchant/task-list',
      },
      {
        path: 'merchant-list',
        element: 'MerchantList',
        title: '任務管理',
        icon: <BankOutlined />,
        protected: true,
        key: '/merchant/merchant-list',
      },
      // 审核详情页面（路径参数版本）
      {
        path: 'audit-detail/:id',
        element: 'AuditDetail',
        title: '審核詳情',
        icon: <FileSearchOutlined />,
        protected: true,
        hideInMenu: true,
        parentPath: '/merchant/check-in', // 用于面包屑导航
        highlightMenu: '/merchant/check-in', // 用于侧边栏菜单高亮
        key: '/merchant/audit-detail',
      },
    ],
  },

  // 用户管理
  {
    path: '/users',
    title: '用戶管理',
    icon: <TeamOutlined />,
    element: 'Layout',
    children: [
      {
        path: 'roles',
        element: 'RoleManagement',
        title: '角色管理',
        icon: <UserOutlined />,
        protected: true,
        key: '/users/roles',
      },
      {
        path: 'user-tasks',
        element: 'UserAccountManagement',
        title: '帳號管理',
        icon: <UsergroupAddOutlined />,
        protected: true,
        key: '/users/user-tasks',
      },
      {
        path: 'permissions',
        element: 'PermissionManagement',
        title: '權限管理',
        icon: <SafetyOutlined />,
        protected: true,
        key: '/users/permissions',
      },
    ],
  },

  // 404 页面
  {
    path: '*',
    element: 'NotFound',
    title: '404',
    hideInBreadcrumb: true,
  },
];

/**
 * 應用路由配置
 *
 * 這是唯一需要修改的文件，當需要添加或修改路由時
 * 系統會自動處理路由生成、麵包屑導航和權限控制
 */
