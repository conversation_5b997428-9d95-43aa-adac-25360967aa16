import Layout from '@components/layout/index.web';
import React, { lazy, Suspense } from 'react';
import type { RouteObject } from 'react-router-dom';
import { Navigate, useRoutes } from 'react-router-dom';

import { useAuth } from '@/contexts/AuthContext';

import { appRoutes } from '../config';
import { RequireAuth } from './RequireAuth';
import type { AppRouteConfig } from './types';
// 使用懒加载导入组件
const Login = lazy(() => import('@pages/login/index.web'));
const AuditDetail = lazy(
  () => import('@pages/merchant/audit-detail/index.web')
);
const MerchantCheckIn = lazy(
  () => import('@pages/merchant/check-in/index.web')
);
const TaskList = lazy(() => import('@pages/merchant/task-list/index.web'));
const RoleManagement = lazy(() => import('@pages/users/roles/index.web'));
const UserAccountManagement = lazy(
  () => import('@pages/users/user-tasks/index.web')
);
const PermissionManagement = lazy(
  () => import('@pages/users/permissions/index.web')
);

const MerchantList = lazy(
  () => import('@pages/merchant/merchant-list/index.web')
);

const NotFound = lazy(() => import('@pages/error/index.web'));

// 加载指示器组件
const LoadingComponent = () => (
  <div className="flex-cc w-full h-full">loading...</div>
);

const routerMap: Record<string, React.ReactNode> = {
  Login: (
    <Suspense fallback={<LoadingComponent />}>
      <Login />
    </Suspense>
  ),
  AuditDetail: (
    <Suspense fallback={<LoadingComponent />}>
      <AuditDetail />
    </Suspense>
  ),
  MerchantCheckIn: (
    <Suspense fallback={<LoadingComponent />}>
      <MerchantCheckIn />
    </Suspense>
  ),
  TaskList: (
    <Suspense fallback={<LoadingComponent />}>
      <TaskList />
    </Suspense>
  ),
  MerchantList: (
    <Suspense fallback={<LoadingComponent />}>
      <MerchantList />
    </Suspense>
  ),
  RoleManagement: (
    <Suspense fallback={<LoadingComponent />}>
      <RoleManagement />
    </Suspense>
  ),
  UserAccountManagement: (
    <Suspense fallback={<LoadingComponent />}>
      <UserAccountManagement />
    </Suspense>
  ),
  PermissionManagement: (
    <Suspense fallback={<LoadingComponent />}>
      <PermissionManagement />
    </Suspense>
  ),
  NotFound: (
    <Suspense fallback={<LoadingComponent />}>
      <NotFound />
    </Suspense>
  ),
};

/**
 * 处理路由-比如是根据后端返回的路由格式进行的处理
 */
function transformRoutes(appRoutes: AppRouteConfig[]): RouteObject[] {
  return appRoutes.map((route) => {
    // 处理重定向路由
    if (route.isRedirect && route.redirectTo && !route.children) {
      return {
        path: route.path,
        element: <Navigate to={route.redirectTo} replace />,
      };
    }
    // 处理普通路由
    const baseRoute: RouteObject = {
      path: route.path,
      element: routerMap[route.element as string] ? (
        routerMap[route.element as string]
      ) : (
        <RequireAuth>
          <Layout />
        </RequireAuth>
      ),
    };

    // 处理嵌套子路由
    if (route.children && route.children.length > 0) {
      // 添加索引路由（当访问父路径时的默认重定向）
      const indexRoute: RouteObject = {
        index: true,
        element: <Navigate to={route.children[0].path} replace />,
      };

      // 递归处理子路由
      const childRoutes = route.children.map((child) => {
        // 移除父路径前缀，使子路径变为相对路径
        const relativePath = child.path
          .replace(route.path, '')
          .replace(/^\//, '');
        return {
          path: relativePath,
          element: routerMap[child.element as string],
          // 可以在这里添加其他需要的属性
        };
      });

      baseRoute.children = [indexRoute, ...childRoutes];
    }
    return baseRoute;
  });
}

export const AppRouter: React.FC = () => {
  // 获取认证状态，用于全局加载状态处理
  const auth = useAuth();
  const routers = transformRoutes(appRoutes);
  const element = useRoutes(routers);

  // 如果认证状态正在加载，不显示任何内容
  if (auth.loading) {
    return null;
  }
  return element;
};
