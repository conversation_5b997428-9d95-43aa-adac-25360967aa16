import type { ReactNode } from 'react';

/**
 * 统一路由配置类型
 * 只需配置这一个对象，系统会自动处理路由生成、面包屑导航和权限控制
 */
export interface AppRouteConfig {
  // 路由路径
  path: string;
  // 路由元素
  element?: ReactNode;
  // 页面标题
  title?: ReactNode;
  // 图标（可选）
  icon?: ReactNode;
  // 是否在面包屑中隐藏
  hideInBreadcrumb?: boolean;
  // 是否在侧边栏菜单中隐藏
  hideInMenu?: boolean;
  // 是否是重定向路由
  isRedirect?: boolean;
  // 重定向目标
  redirectTo?: string;
  // 是否需要保护（需要登录才能访问）
  protected?: boolean;
  // 显式指定父路径（用于面包屑导航）
  parentPath?: string;
  // 高亮菜单路径（当前路由应该高亮哪个菜单项）
  highlightMenu?: string;
  // 子路由
  children?: AppRouteConfig[];
  key?: string;
}

/**
 * 面包屑配置类型
 */
export interface BreadcrumbConfig {
  path: string;
  title: ReactNode;
  icon?: ReactNode;
  parentPath?: string; // 父路由路径，用于构建面包屑链
}

/**
 * 受保护的路由组件属性
 */
export interface ProtectedRouteProps {
  element: ReactNode;
}
