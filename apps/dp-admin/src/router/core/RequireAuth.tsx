import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';

import { useAuth } from '../../contexts/AuthContext';

interface RequireAuthProps {
  children: React.ReactNode;
}

/**
 * 需要认证的路由组件
 *
 * 如果用户已认证，则渲染子组件
 * 如果用户未认证，则重定向到登录页面
 * 如果认证状态正在加载，则显示加载指示器
 */
export const RequireAuth: React.FC<RequireAuthProps> = ({ children }) => {
  const auth = useAuth();
  const location = useLocation();

  // 如果认证状态正在加载，不显示任何内容
  // 这样可以避免在认证状态加载过程中显示加载提示
  if (auth.loading) {
    return null;
  }

  // 如果用户未认证，重定向到登录页面
  if (!auth.isAuthenticated) {
    // 保存当前位置，登录后可以重定向回来
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // 如果用户已认证，渲染子组件
  return <>{children}</>;
};
