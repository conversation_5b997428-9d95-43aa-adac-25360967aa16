# 路由系统文档

本文档详细说明了运营管理系统的路由系统架构、配置方法和使用指南。

## 目录结构

```
/src/router/
├── config/                # 路由配置相关
│   └── routes.web.tsx     # 路由配置定义（唯一需要修改的文件）
├── core/                  # 核心功能
│   ├── AppRouter.web.tsx  # 应用路由组件，处理路由生成和保护
│   ├── index.ts           # 导出核心功能
│   └── types.ts           # 类型定义
├── index.ts               # 主入口文件，整合并导出所有功能
└── README.md              # 本文档
```

## 路由系统概述

本项目使用 React Router v6 实现路由管理，主要特点包括：

1. **集中式路由配置**：所有路由定义集中在 `config/routes.web.tsx` 文件中，只需修改这一个文件
2. **自动化处理**：系统自动处理路由生成、面包屑导航和权限控制
3. **身份验证集成**：路由系统与身份验证系统紧密集成，确保用户访问权限控制
4. **简洁的文件结构**：将相关功能整合到少量文件中，减少冗余和维护成本

## 核心组件

### 1. 路由配置 (config/routes.web.tsx)

`config/routes.web.tsx` 文件是唯一需要修改的文件，包含所有路由配置：

```tsx
export const appRoutes: AppRouteConfig[] = [
  // 登录页面
  {
    path: '/login',
    element: <Login />,
    title: '登录',
    hideInBreadcrumb: true,
  },

  // 首页（重定向到商户入住审核页面）
  {
    path: '/',
    isRedirect: true,
    redirectTo: '/merchant/check-in',
    title: '首页',
    icon: <HomeOutlined />,
  },
  // 更多路由...
```

每个路由对象包含以下属性：
- `path`: 路由路径
- `element`: 要渲染的组件
- 可选的 `children`: 嵌套路由

### 2. 路由类型 (core/types.ts)

`core/types.ts` 文件定义了路由配置的类型：

```ts
export interface AppRouteConfig {
  // 路由路径
  path: string;
  // 路由元素
  element?: React.ReactNode;
  // 页面标题
  title?: React.ReactNode;
  // 图标（可选）
  icon?: React.ReactNode;
  // 是否在面包屑中隐藏
  hideInBreadcrumb?: boolean;
  // 是否是重定向路由
  isRedirect?: boolean;
  // 重定向目标
  redirectTo?: string;
  // 是否需要保护（需要登录才能访问）
  protected?: boolean;
  // 子路由
  children?: AppRouteConfig[];
}
```

### 3. 应用路由组件 (core/AppRouter.web.tsx)

`core/AppRouter.web.tsx` 文件负责生成路由和处理权限控制：

```tsx
export const AppRouter: React.FC = () => {
  const routes = useMemo(() => generateRoutes(appRoutes), []);
  const element = useRoutes(routes);

  return element;
};
```

### 4. 身份验证

身份验证逻辑已移至全局 hooks 目录下的 `useAuth` hook，提供了更完整的认证状态管理功能。

## 使用指南

### 添加新路由

要添加新路由，只需在 `config/routes.web.tsx` 文件中的 `appRoutes` 数组中添加新的配置对象：

```tsx
export const appRoutes: AppRouteConfig[] = [
  // 现有路由...

  // 添加新路由
  {
    path: '/new-page',
    element: <NewPage />,
    title: '新页面',
    icon: <FileOutlined />,
    protected: true, // 需要登录才能访问
  },
];
```

### 添加公开路由

对于不需要登录就可以访问的页面（如注册页面），直接使用组件作为 `element`：

```tsx
{
  path: '/register',
  element: <Register />,
},
```

### 添加嵌套路由

React Router v6 支持嵌套路由，可以通过 `children` 属性定义：

```tsx
{
  path: '/settings',
  element: <ProtectedRoute element={<Settings />} />,
  children: [
    {
      path: 'profile',
      element: <Profile />,
    },
    {
      path: 'account',
      element: <Account />,
    },
  ],
},
```

在这个例子中，`/settings/profile` 和 `/settings/account` 都是有效的路由。

### 使用路由参数

可以在路由路径中定义参数：

```tsx
{
  path: '/users/:userId',
  element: <ProtectedRoute element={<UserDetail />} />,
},
```

然后在组件中使用 `useParams` 钩子获取参数：

```tsx
import { useParams } from 'react-router-dom';

const UserDetail: React.FC = () => {
  const { userId } = useParams();
  // 使用 userId 参数...
};
```

### 编程式导航

在组件中可以使用 `useNavigate` 钩子进行编程式导航：

```tsx
import { useNavigate } from 'react-router-dom';

const MyComponent: React.FC = () => {
  const navigate = useNavigate();

  const handleClick = () => {
    navigate('/some-path');
  };

  // ...
};
```

## 身份验证集成

### 登录流程

1. 用户访问需要登录的页面
2. `ProtectedRoute` 组件检查用户是否已登录
3. 如果未登录，重定向到登录页面
4. 用户在登录页面输入凭据
5. 调用 `useLogin` hook 中的 login 方法进行登录
6. 登录成功后，重定向到原来要访问的页面或首页

### 登出流程

1. 用户点击登出按钮
2. 调用组件特定的登出逻辑，清除认证状态
3. 重定向到登录页面

## 最佳实践

1. **保持路由配置简洁**：每个路由只负责定义路径和要渲染的组件
2. **使用懒加载**：对于大型应用，考虑使用 React.lazy 和 Suspense 进行代码分割
3. **集中管理路由常量**：将路由路径定义为常量，避免硬编码
4. **使用路由守卫**：对于需要特定权限的页面，可以扩展 ProtectedRoute 组件
5. **保持路由结构扁平**：尽量避免过深的嵌套路由，以提高可维护性

## 扩展与定制

### 添加路由守卫

可以扩展 `ProtectedRoute` 组件，添加更细粒度的权限控制：

```tsx
interface ProtectedRouteProps {
  element: React.ReactNode;
  requiredRole?: string;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  element,
  requiredRole
}) => {
  const userRole = getUserRole(); // 获取用户角色

  if (!isAuthenticated()) {
    return <Navigate to="/login" replace />;
  }

  if (requiredRole && userRole !== requiredRole) {
    return <Navigate to="/unauthorized" replace />;
  }

  return <Layout>{element}</Layout>;
};
```

### 添加路由过渡动画

可以结合 React Transition Group 添加路由切换动画：

```tsx
import { CSSTransition, TransitionGroup } from 'react-transition-group';
import { useLocation } from 'react-router-dom';

const AnimatedRoutes: React.FC = () => {
  const location = useLocation();
  const element = useRoutes(routes, location);

  return (
    <TransitionGroup>
      <CSSTransition key={location.key} timeout={300} classNames="fade">
        {element}
      </CSSTransition>
    </TransitionGroup>
  );
};
```

## 常见问题

### 1. 如何处理 404 页面？

在路由配置的最后添加一个通配符路由：

```tsx
{
  path: '*',
  element: <NotFound />,
},
```

### 2. 如何实现路由懒加载？

本项目已经实现了路由懒加载，使用 React.lazy 和 Suspense：

```tsx
import React, { lazy, Suspense } from 'react';

// 使用懒加载导入组件
const Login = lazy(() => import('../../pages/login/index.web'));
const AuditDetail = lazy(() => import('../../pages/merchant/audit-detail/index.web'));

// 加载指示器组件
const LoadingComponent = () => <div>加載中...</div>;

// 在路由中使用
export const appRoutes: AppRouteConfig[] = [
  {
    path: '/login',
    element: <Suspense fallback={<LoadingComponent />}><Login /></Suspense>,
    title: '登錄',
    hideInBreadcrumb: true,
  },
  // 其他路由...
];
```

这种方式可以显著提高应用的加载性能，特别是对于大型应用。

### 3. 如何在路由变化时执行某些操作？

使用 `useEffect` 和 `useLocation`：

```tsx
import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

const MyComponent: React.FC = () => {
  const location = useLocation();

  useEffect(() => {
    // 路由变化时执行的操作
    console.log('Route changed to:', location.pathname);
  }, [location]);

  // ...
};
```
