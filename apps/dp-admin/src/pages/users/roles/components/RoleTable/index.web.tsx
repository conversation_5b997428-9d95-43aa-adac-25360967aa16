import { Pagination, Table } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import React from 'react';

import type { RoleData, RoleTableProps } from '../../types';
import ActionButtons from '../ActionButtons/index.web';
import StatusTag from '../StatusTag/index.web';

/**
 * 角色表格組件
 *
 * 顯示角色列表及分頁
 */
const RoleTable: React.FC<RoleTableProps> = ({
  loading,
  data,
  currentPage,
  pageSize,
  total,
  onPageChange,
  onStatusChange,
  onEdit,
}) => {
  // 定義表格列
  const columns: ColumnsType<RoleData> = [
    {
      title: '角色名稱',
      dataIndex: 'name',
      key: 'name',
      className: 'text-sm',
      width: 150,
    },
    {
      title: '包含權限',
      dataIndex: 'permissions',
      key: 'permissions',
      className: 'text-sm',
      width: 400,
      render: (permissions) => {
        // 將權限數組轉換為可讀文本
        const permissionMap: Record<string, string> = {
          'user:view': '用戶管理 - 查看用戶信息',
          'user:edit': '用戶管理 - 編輯用戶信息',
          'role:view': '角色管理 - 查看角色信息',
          'role:edit': '角色管理 - 編輯角色信息',
        };

        const permissionTexts = permissions.map(
          (p: string) => permissionMap[p] || p
        );

        return (
          <div className="max-h-20 overflow-y-auto text-xs">
            {permissionTexts.map((text: string, index: number) => (
              <div key={index} className="mb-1">
                {text}
              </div>
            ))}
          </div>
        );
      },
    },
    {
      title: '角色狀態',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => <StatusTag status={status} />,
      className: 'text-sm',
    },
    {
      title: '更新人',
      dataIndex: 'updatedBy',
      key: 'updatedBy',
      className: 'text-sm',
      width: 150,
    },
    {
      title: '更新時間',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      className: 'text-sm',
      width: 150,
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      width: 120,
      render: (_, record) => (
        <ActionButtons
          record={record}
          onEdit={onEdit}
          onStatusChange={onStatusChange}
        />
      ),
      className: 'text-sm',
    },
  ];

  return (
    <div>
      <div className="overflow-x-auto">
        <Table
          columns={columns}
          dataSource={data}
          rowKey="id"
          loading={loading}
          pagination={false}
          className="w-full"
          size="middle"
          scroll={{ x: 1200 }}
          bordered
        />
      </div>

      <div className="flex justify-between items-center mt-4 flex-wrap">
        {total > 0 && (
          <div className="text-sm text-gray-500 mb-2 sm:mb-0">
            顯示 {(currentPage - 1) * pageSize + 1} 到{' '}
            {Math.min(currentPage * pageSize, total)} 條，共 {total} 條記錄
          </div>
        )}

        <Pagination
          hideOnSinglePage
          current={currentPage}
          pageSize={pageSize}
          total={total}
          onChange={onPageChange}
          showSizeChanger
          showQuickJumper
          showTotal={(total) => `共 ${total} 條`}
          pageSizeOptions={['10', '20', '50']}
          className="text-sm"
        />
      </div>
    </div>
  );
};

export default RoleTable;
