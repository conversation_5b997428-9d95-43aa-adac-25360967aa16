import React from 'react';

import type { StatusTagProps } from '../../types';

/**
 * 狀態標籤組件
 *
 * 根據角色狀態顯示不同樣式的標籤
 */
const StatusTag: React.FC<StatusTagProps> = ({ status }) => {
  // 根據狀態設置不同的樣式類
  const getTagClass = () => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'INACTIVE':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // 根據狀態顯示不同的文本
  const getStatusText = () => {
    switch (status) {
      case 'ACTIVE':
        return '有效';
      case 'INACTIVE':
        return '無效';
      default:
        return '未知';
    }
  };

  return (
    <span
      className={`px-2 py-1 text-xs font-medium rounded-md border ${getTagClass()}`}
    >
      {getStatusText()}
    </span>
  );
};

export default StatusTag;
