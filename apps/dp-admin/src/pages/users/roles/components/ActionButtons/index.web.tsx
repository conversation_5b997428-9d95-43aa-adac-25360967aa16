import { EditOutlined } from '@ant-design/icons';
import { Button, Modal, Space, Tooltip } from 'antd';
import React, { useState } from 'react';

import type { ActionButtonsProps } from '../../types';

/**
 * 操作按鈕組件
 *
 * 提供編輯和狀態切換按鈕
 */
const ActionButtons: React.FC<ActionButtonsProps> = ({
  record,
  onEdit,
  onStatusChange,
}) => {
  const [confirmVisible, setConfirmVisible] = useState(false);

  // 處理編輯按鈕點擊
  const handleEdit = () => {
    onEdit(record);
  };

  // 處理狀態切換按鈕點擊
  const handleStatusClick = () => {
    setConfirmVisible(true);
  };

  // 處理確認狀態變更
  const handleConfirm = () => {
    const newStatus = record.status === 'ACTIVE' ? 'INACTIVE' : 'ACTIVE';
    onStatusChange(record.id, newStatus);
    setConfirmVisible(false);
  };

  // 處理取消狀態變更
  const handleCancel = () => {
    setConfirmVisible(false);
  };

  // 獲取狀態切換按鈕文本
  const getStatusButtonText = () => {
    return record.status === 'ACTIVE' ? '禁用' : '啟用';
  };

  // 獲取狀態切換按鈕類型
  const getStatusButtonType = () => {
    return record.status === 'ACTIVE';
  };

  // 獲取確認彈窗標題
  const getConfirmTitle = () => {
    return `確認${record.status === 'ACTIVE' ? '禁用' : '啟用'}角色`;
  };

  // 獲取確認彈窗內容
  const getConfirmContent = () => {
    return `您確定要${
      record.status === 'ACTIVE' ? '禁用' : '啟用'
    }角色"${record.name}"嗎？`;
  };

  return (
    <>
      <Space size="small">
        <Tooltip title="编辑">
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={handleEdit}
            className="text-blue-500 hover:text-blue-600"
          />
        </Tooltip>
        <Button
          danger={getStatusButtonType()}
          type={getStatusButtonType() ? 'default' : 'primary'}
          size="small"
          onClick={handleStatusClick}
          className="ml-2"
        >
          {getStatusButtonText()}
        </Button>
      </Space>

      <Modal
        title={getConfirmTitle()}
        open={confirmVisible}
        onOk={handleConfirm}
        onCancel={handleCancel}
        okText="確認"
        cancelText="取消"
      >
        <p>{getConfirmContent()}</p>
        <p className="text-gray-500 text-sm mt-2">
          此操作將記錄在系統日誌中，請謹慎操作。
        </p>
      </Modal>
    </>
  );
};

export default ActionButtons;
