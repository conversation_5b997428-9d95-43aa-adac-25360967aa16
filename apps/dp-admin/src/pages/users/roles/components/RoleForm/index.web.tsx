import { Form, Input, Modal, Tree } from 'antd';
import type { DataNode } from 'antd/es/tree';
import React, { useEffect, useState } from 'react';

import type { RoleFormProps } from '../../types';

/**
 * 角色表單組件
 *
 * 用於新增和編輯角色
 */
const RoleForm: React.FC<RoleFormProps> = ({
  visible,
  title,
  initialValues,
  onCancel,
  onSubmit,
  confirmLoading,
}) => {
  const [form] = Form.useForm();
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [checkedKeys, setCheckedKeys] = useState<React.Key[]>([]);
  const [autoExpandParent, setAutoExpandParent] = useState<boolean>(true);

  // 權限樹數據
  const treeData: DataNode[] = [
    {
      title: '用戶管理',
      key: 'user',
      children: [
        {
          title: '查看用戶信息',
          key: 'user:view',
        },
        {
          title: '編輯用戶信息',
          key: 'user:edit',
        },
      ],
    },
    {
      title: '角色管理',
      key: 'role',
      children: [
        {
          title: '查看角色信息',
          key: 'role:view',
        },
        {
          title: '編輯角色信息',
          key: 'role:edit',
        },
      ],
    },
  ];

  // 當初始值變化時，更新表單和權限樹
  useEffect(() => {
    if (visible) {
      form.setFieldsValue({
        name: initialValues?.name || '',
      });

      // 設置權限樹選中狀態
      if (initialValues?.permissions) {
        setCheckedKeys(initialValues.permissions);

        // 自動展開父節點
        const parentKeys = ['user', 'role'];
        setExpandedKeys(parentKeys);
      } else {
        setCheckedKeys([]);
        setExpandedKeys([]);
      }
    }
  }, [visible, initialValues, form]);

  // 處理表單提交
  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      onSubmit({
        ...initialValues,
        ...values,
        permissions: checkedKeys as string[],
      });
    } catch (error) {
      console.error('表單驗證失敗:', error);
    }
  };

  // 處理權限樹展開/收起
  const onExpand = (expandedKeysValue: React.Key[]) => {
    setExpandedKeys(expandedKeysValue);
    setAutoExpandParent(false);
  };

  // 處理權限樹選中/取消選中
  const onCheck = (
    checkedKeysValue:
      | React.Key[]
      | { checked: React.Key[]; halfChecked: React.Key[] }
  ) => {
    setCheckedKeys(checkedKeysValue as React.Key[]);
  };

  return (
    <Modal
      title={title}
      open={visible}
      onOk={handleOk}
      onCancel={onCancel}
      confirmLoading={confirmLoading}
      width={600}
      okText="確定"
      cancelText="取消"
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{ name: initialValues?.name || '' }}
      >
        <Form.Item
          name="name"
          label="角色名稱"
          rules={[{ required: true, message: '請輸入角色名稱' }]}
        >
          <Input placeholder="請輸入角色名稱" />
        </Form.Item>

        <Form.Item
          label="包含權限"
          required
          help="請選擇該角色包含的權限，可多選"
        >
          <div className="max-h-60 overflow-y-auto border rounded-md p-2">
            <Tree
              checkable
              onExpand={onExpand}
              expandedKeys={expandedKeys}
              autoExpandParent={autoExpandParent}
              onCheck={onCheck}
              checkedKeys={checkedKeys}
              treeData={treeData}
            />
          </div>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default RoleForm;
