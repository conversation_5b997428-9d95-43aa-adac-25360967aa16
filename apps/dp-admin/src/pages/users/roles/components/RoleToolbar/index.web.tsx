import { PlusOutlined, SearchOutlined } from '@ant-design/icons';
import { Button, Col, Input, Row, Select, Space } from 'antd';
import React from 'react';

import type { RoleToolbarProps } from '../../types';

const { Option } = Select;

/**
 * 角色工具欄組件
 *
 * 提供搜索、篩選和操作按鈕
 */
const RoleToolbar: React.FC<RoleToolbarProps> = ({
  searchText,
  roleStatus,
  onSearchTextChange,
  onRoleStatusChange,
  onSearch,
  onReset,
  onAdd,
}) => {
  // 處理搜索文本變化
  const handleSearchTextChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onSearchTextChange(e.target.value);
  };

  // 處理角色狀態變化
  const handleRoleStatusChange = (value: string) => {
    onRoleStatusChange(value);
  };

  // 處理搜索按鈕點擊
  const handleSearch = () => {
    onSearch();
  };

  // 處理重置按鈕點擊
  const handleReset = () => {
    onReset();
  };

  // 處理新增按鈕點擊
  const handleAdd = () => {
    onAdd();
  };

  return (
    <div className="bg-white p-4 rounded-lg shadow-sm mb-6">
      <Row gutter={[16, 16]} align="middle">
        {/* 搜索框 */}
        <Col xs={24} sm={12} md={8} lg={6}>
          <Input
            placeholder="角色名稱"
            value={searchText}
            onChange={handleSearchTextChange}
            prefix={<SearchOutlined className="text-gray-400" />}
            allowClear
          />
        </Col>

        {/* 角色狀態篩選 */}
        <Col xs={24} sm={12} md={8} lg={6}>
          <Select
            placeholder="角色狀態"
            value={roleStatus}
            onChange={handleRoleStatusChange}
            className="w-full"
          >
            <Option value="ALL">全部狀態</Option>
            <Option value="ACTIVE">有效</Option>
            <Option value="INACTIVE">無效</Option>
          </Select>
        </Col>
      </Row>
      <Row className="mt-4">
        <div className="flex justify-end">
          <Space>
            <Button onClick={handleReset}>重置</Button>
            <Button type="primary" onClick={handleSearch}>
              搜索
            </Button>
            <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
              新增角色
            </Button>
          </Space>
        </div>
      </Row>
    </div>
  );
};

export default RoleToolbar;
