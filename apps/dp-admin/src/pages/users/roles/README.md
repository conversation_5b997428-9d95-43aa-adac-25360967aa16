# 角色管理页面

## 页面概述

角色管理页面用于管理系统角色，包括查看、新增、编辑和删除角色，以及设置角色权限。运营人员可以在此页面管理不同角色的权限范围，控制用户对系统功能的访问权限。

## 组件结构

```
/roles
├── components/
│   ├── ActionButtons/        # 操作按钮组件
│   │   └── index.web.tsx
│   ├── RoleForm/             # 角色表单组件
│   │   └── index.web.tsx
│   ├── RoleTable/            # 角色表格组件
│   │   └── index.web.tsx
│   ├── RoleToolbar/          # 角色工具栏组件
│   │   └── index.web.tsx
│   └── StatusTag/            # 状态标签组件
│       └── index.web.tsx
├── services/
│   ├── api.ts                # API请求函数
│   └── hooks.ts              # 自定义Hooks
├── index.web.tsx             # 页面主组件
├── types.ts                  # 类型定义
└── README.md                 # 本文档
```

## 数据权限

- **查看权限**：有"查看角色"权限的人可见角色列表
- **操作权限**：有"管理角色"权限的人可以进行新增、修改、删除角色操作

## 页面默认显示

- 进入页面默认显示所有角色
- 数据按照更新时间降序排列

## 搜索与筛选功能

搜索表单提供以下筛选条件：

### 搜索项
- **角色名称**：支持模糊搜索角色名称
- **角色状态**：下拉框，包含全部、有效、无效选项

### 操作按钮
- **重置**：清空所有搜索条件
- **搜索**：根据设置的条件进行搜索
- **新增角色**：打开新增角色表单

## 列表字段说明

角色列表包含以下字段：
- **角色名称**：角色的名称
- **包含权限**：角色包含的权限列表
- **角色状态**：有效或无效
- **更新人**：最后更新角色的用户
- **更新时间**：最后更新角色的时间
- **操作**：编辑和状态切换按钮

## 组件详解

### 1. RoleManagement 组件

角色管理页面的主组件，负责整合所有子组件和管理页面状态。

**主要功能**：
- 集成角色工具栏、角色表格和角色表单
- 通过自定义Hook获取和管理数据

### 2. RoleToolbar 组件

提供搜索、筛选和操作按钮的工具栏组件。

**主要功能**：
- 角色名称搜索框
- 角色状态筛选下拉框
- 重置、搜索和新增角色按钮

### 3. RoleTable 组件

显示角色列表的表格组件。

**主要功能**：
- 展示角色数据
- 分页控制
- 集成状态标签和操作按钮

### 4. RoleForm 组件

用于新增和编辑角色的表单组件。

**主要功能**：
- 角色名称输入
- 权限树选择
- 表单验证

### 5. StatusTag 组件

根据角色状态显示不同样式的标签。

**主要功能**：
- 有效状态显示绿色标签
- 无效状态显示红色标签

### 6. ActionButtons 组件

提供角色操作按钮。

**主要功能**：
- 编辑按钮
- 状态切换按钮（启用/禁用）
- 状态变更确认弹窗

## 数据流

1. **数据获取流程**：
   - 页面加载时，通过`useRoleManagement` Hook获取角色列表
   - 搜索或筛选条件变化时，重新获取数据
   - 分页变化时，获取对应页的数据

2. **角色操作流程**：
   - 新增角色：打开表单弹窗 -> 填写信息 -> 提交 -> 刷新列表
   - 编辑角色：打开表单弹窗 -> 修改信息 -> 提交 -> 刷新列表
   - 状态变更：点击状态按钮 -> 确认操作 -> 更新状态 -> 刷新列表

## 响应式设计

页面使用Tailwind CSS实现响应式设计，确保在不同设备上都能提供良好的用户体验：

- **移动设备**：工具栏控件垂直排列，表格横向滚动
- **平板设备**：工具栏控件部分并排，表格可能需要横向滚动
- **桌面设备**：工具栏控件完全并排，表格完整显示

## 开发注意事项

1. 确保角色状态变更操作有二次确认
2. 记录角色变更日志，包含修改人、修改内容、修改时间
3. 权限树应支持全选/取消全选功能
4. 角色名称不允许重复
5. 只有有效状态的角色才能被分配给用户
