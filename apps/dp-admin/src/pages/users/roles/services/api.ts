import type { RoleData } from '../types';

/**
 * 获取角色列表
 * @param params 查询参数
 * @returns [success, data] 成功状态和数据
 */
export const getRoleList = async (params: {
  searchText?: string;
  status?: string;
  pageNum?: number;
  pageSize?: number;
}) => {
  try {
    // 模拟API调用
    // 实际项目中应该调用真实的API
    // const data = await request.get('/admin-api/roles', params);

    // 模拟数据
    const mockData = {
      total: 4,
      roles: [
        {
          id: '1',
          name: '超级管理员',
          permissions: ['user:view', 'user:edit', 'role:view', 'role:edit'],
          status: 'ACTIVE',
          updatedBy: '杨玲(<PERSON><PERSON>.<PERSON>)',
          updatedAt: '2025-3-20 14:09',
        },
        {
          id: '2',
          name: '运营主管',
          permissions: ['user:view', 'user:edit', 'role:view'],
          status: 'ACTIVE',
          updatedBy: '杨玲(<PERSON><PERSON><PERSON>)',
          updatedAt: '2025-3-20 14:09',
        },
        {
          id: '3',
          name: '商務主管',
          permissions: ['user:view', 'user:edit'],
          status: 'INACTIVE',
          updatedBy: '杨玲(Chara.<PERSON>)',
          updatedAt: '2025-3-20 14:09',
        },
        {
          id: '4',
          name: '商務',
          permissions: ['user:view'],
          status: 'ACTIVE',
          updatedBy: '杨玲(Chara.Yang)',
          updatedAt: '2025-3-20 14:09',
        },
      ],
    };

    // 模拟搜索和筛选
    let filteredRoles = [...mockData.roles];

    if (params.searchText) {
      filteredRoles = filteredRoles.filter((role) =>
        role.name.toLowerCase().includes(params.searchText!.toLowerCase())
      );
    }

    if (params.status && params.status !== 'ALL') {
      filteredRoles = filteredRoles.filter(
        (role) => role.status === params.status
      );
    }

    // 模拟分页
    const pageNum = params.pageNum || 1;
    const pageSize = params.pageSize || 10;
    const start = (pageNum - 1) * pageSize;
    const end = start + pageSize;
    const pagedRoles = filteredRoles.slice(start, end);

    return [
      true,
      {
        total: filteredRoles.length,
        roles: pagedRoles,
      },
    ];
  } catch (error) {
    console.error('获取角色列表失败:', error);
    return [false, error];
  }
};

/**
 * 更新角色状态
 * @param id 角色ID
 * @param status 新状态
 * @returns [success, data] 成功状态和数据
 */
export const updateRoleStatus = async (
  id: string | number,
  status: 'ACTIVE' | 'INACTIVE'
) => {
  try {
    // 模拟API调用
    // const data = await request.post('/admin-api/roles/status', { id, status });

    // 模拟成功响应
    return [true, { success: true }];
  } catch (error) {
    console.error('更新角色状态失败:', error);
    return [false, error];
  }
};

/**
 * 创建或更新角色
 * @param role 角色数据
 * @returns [success, data] 成功状态和数据
 */
export const saveRole = async (role: Partial<RoleData>) => {
  try {
    // 模拟API调用
    // const url = role.id ? '/admin-api/roles/update' : '/admin-api/roles/create';
    // const data = await request.post(url, role);

    // 模拟成功响应
    return [true, { success: true, id: role.id || '5' }];
  } catch (error) {
    console.error('保存角色失败:', error);
    return [false, error];
  }
};
