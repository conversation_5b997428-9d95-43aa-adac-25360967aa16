import { message } from 'antd';
import { useCallback, useEffect, useState } from 'react';

import type { RoleData } from '../types';
import { getRoleList, saveRole, updateRoleStatus } from './api';

/**
 * 角色管理相关的自定义 Hook
 *
 * 提供角色列表数据和操作方法
 */
export const useRoleManagement = () => {
  // 角色列表数据
  const [roles, setRoles] = useState<RoleData[]>([]);
  // 总数
  const [total, setTotal] = useState(0);
  // 加载状态
  const [loading, setLoading] = useState(false);
  // 当前页码
  const [currentPage, setCurrentPage] = useState(1);
  // 每页条数
  const [pageSize, setPageSize] = useState(10);
  // 搜索文本
  const [searchText, setSearchText] = useState('');
  // 角色状态筛选
  const [roleStatus, setRoleStatus] = useState('ALL');
  // 角色表单可见性
  const [formVisible, setFormVisible] = useState(false);
  // 表单标题
  const [formTitle, setFormTitle] = useState('新增角色');
  // 表单初始值
  const [initialValues, setInitialValues] = useState<Partial<RoleData>>({});
  // 表单提交加载状态
  const [confirmLoading, setConfirmLoading] = useState(false);

  // 获取角色列表数据
  const fetchRoleList = useCallback(async () => {
    setLoading(true);
    try {
      const params = {
        searchText,
        status: roleStatus,
        pageNum: currentPage,
        pageSize,
      };
      const [success, res] = await getRoleList(params);
      if (success) {
        setRoles(
          res &&
            typeof res === 'object' &&
            'roles' in res &&
            Array.isArray(res.roles)
            ? res.roles
            : []
        );
        setTotal(
          res && typeof res === 'object' && 'total' in res
            ? Number(res.total)
            : 0
        );
      } else {
        message.error('获取角色列表失败');
      }
    } catch (error) {
      console.error('获取角色列表错误:', error);
      message.error('获取角色列表出错');
    } finally {
      setLoading(false);
    }
  }, [searchText, roleStatus, currentPage, pageSize]);

  // 首次加载和依赖变化时获取数据
  useEffect(() => {
    fetchRoleList();
  }, [fetchRoleList]);

  // 处理搜索
  const handleSearch = useCallback(() => {
    setCurrentPage(1);
    fetchRoleList();
  }, [fetchRoleList]);

  // 处理重置
  const handleReset = useCallback(() => {
    setSearchText('');
    setRoleStatus('ALL');
    setCurrentPage(1);
  }, []);

  // 处理页码变化
  const handlePageChange = useCallback((page: number, size?: number) => {
    setCurrentPage(page);
    if (size) {
      setPageSize(size);
    }
  }, []);

  // 处理搜索文本变化
  const handleSearchTextChange = useCallback((value: string) => {
    setSearchText(value);
  }, []);

  // 处理角色状态筛选变化
  const handleRoleStatusChange = useCallback((value: string) => {
    setRoleStatus(value);
  }, []);

  // 处理角色状态变更
  const handleStatusChange = useCallback(
    async (id: string | number, status: 'ACTIVE' | 'INACTIVE') => {
      try {
        const [success] = await updateRoleStatus(id, status);
        if (success) {
          message.success('角色状态更新成功');
          fetchRoleList();
        } else {
          message.error('角色状态更新失败');
        }
      } catch (error) {
        console.error('更新角色状态错误:', error);
        message.error('更新角色状态出错');
      }
    },
    [fetchRoleList]
  );

  // 处理编辑角色
  const handleEdit = useCallback((role: RoleData) => {
    setFormTitle('编辑角色');
    setInitialValues(role);
    setFormVisible(true);
  }, []);

  // 处理新增角色
  const handleAdd = useCallback(() => {
    setFormTitle('新增角色');
    setInitialValues({});
    setFormVisible(true);
  }, []);

  // 处理表单取消
  const handleFormCancel = useCallback(() => {
    setFormVisible(false);
  }, []);

  // 处理表单提交
  const handleFormSubmit = useCallback(
    async (values: Partial<RoleData>) => {
      setConfirmLoading(true);
      try {
        const [success] = await saveRole(values);
        if (success) {
          message.success(`${values.id ? '更新' : '创建'}角色成功`);
          setFormVisible(false);
          fetchRoleList();
        } else {
          message.error(`${values.id ? '更新' : '创建'}角色失败`);
        }
      } catch (error) {
        console.error('保存角色错误:', error);
        message.error(`${values.id ? '更新' : '创建'}角色出错`);
      } finally {
        setConfirmLoading(false);
      }
    },
    [fetchRoleList]
  );

  return {
    roles,
    total,
    loading,
    currentPage,
    pageSize,
    searchText,
    roleStatus,
    formVisible,
    formTitle,
    initialValues,
    confirmLoading,
    handleSearch,
    handleReset,
    handlePageChange,
    handleSearchTextChange,
    handleRoleStatusChange,
    handleStatusChange,
    handleEdit,
    handleAdd,
    handleFormCancel,
    handleFormSubmit,
  };
};
