/**
 * 角色数据类型
 */
export interface RoleData {
  id: string | number; // 角色ID
  name: string; // 角色名称
  permissions: string[]; // 角色包含的权限
  status: 'ACTIVE' | 'INACTIVE'; // 角色状态：有效/无效
  updatedBy: string; // 更新人
  updatedAt: string; // 更新时间
}

/**
 * 角色表格属性
 */
export interface RoleTableProps {
  loading: boolean;
  data: RoleData[];
  currentPage: number;
  pageSize: number;
  total: number;
  onPageChange: (page: number, pageSize?: number) => void;
  onStatusChange: (id: string | number, status: 'ACTIVE' | 'INACTIVE') => void;
  onEdit: (role: RoleData) => void;
}

/**
 * 角色工具栏属性
 */
export interface RoleToolbarProps {
  searchText: string;
  roleStatus: string;
  onSearchTextChange: (value: string) => void;
  onRoleStatusChange: (value: string) => void;
  onSearch: () => void;
  onReset: () => void;
  onAdd: () => void;
}

/**
 * 角色表单属性
 */
export interface RoleFormProps {
  visible: boolean;
  title: string;
  initialValues?: Partial<RoleData>;
  onCancel: () => void;
  onSubmit: (values: Partial<RoleData>) => void;
  confirmLoading: boolean;
}

/**
 * 权限树节点类型
 */
export interface PermissionTreeNode {
  key: string;
  title: string;
  children?: PermissionTreeNode[];
}

/**
 * 状态标签属性
 */
export interface StatusTagProps {
  status: 'ACTIVE' | 'INACTIVE';
}

/**
 * 操作按钮属性
 */
export interface ActionButtonsProps {
  record: RoleData;
  onEdit: (role: RoleData) => void;
  onStatusChange: (id: string | number, status: 'ACTIVE' | 'INACTIVE') => void;
}

/**
 * 角色状态变更日志
 */
export interface RoleStatusLog {
  id: string | number;
  roleId: string | number;
  oldStatus: 'ACTIVE' | 'INACTIVE';
  newStatus: 'ACTIVE' | 'INACTIVE';
  updatedBy: string;
  updatedAt: string;
}

/**
 * 角色权限变更日志
 */
export interface RolePermissionLog {
  id: string | number;
  roleId: string | number;
  oldPermissions: string[];
  newPermissions: string[];
  updatedBy: string;
  updatedAt: string;
}
