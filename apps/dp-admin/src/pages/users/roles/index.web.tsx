import React from 'react';

import RoleForm from './components/RoleForm/index.web';
import RoleTable from './components/RoleTable/index.web';
import RoleToolbar from './components/RoleToolbar/index.web';
import { useRoleManagement } from './services/hooks';

/**
 * 角色管理頁面
 *
 * 用於管理系統角色，包括查看、新增、編輯和刪除角色，以及設置角色權限
 */
const RoleManagement: React.FC = () => {
  // 使用自定義 hooks 獲取數據和狀態
  const {
    loading,
    roles,
    total,
    currentPage,
    pageSize,
    searchText,
    roleStatus,
    formVisible,
    formTitle,
    initialValues,
    confirmLoading,
    handleSearch,
    handleReset,
    handlePageChange,
    handleSearchTextChange,
    handleRoleStatusChange,
    handleStatusChange,
    handleEdit,
    handleAdd,
    handleFormCancel,
    handleFormSubmit,
  } = useRoleManagement();

  return (
    <div className="bg-white p-4 sm:p-6 md:p-8 rounded-lg shadow-sm">
      <div className="mb-6">
        <h1 className="text-xl sm:text-2xl font-medium mb-6">角色管理</h1>

        {/* 工具欄：搜索、篩選和操作按鈕 */}
        <div className="mb-6">
          <RoleToolbar
            searchText={searchText}
            roleStatus={roleStatus}
            onSearchTextChange={handleSearchTextChange}
            onRoleStatusChange={handleRoleStatusChange}
            onSearch={handleSearch}
            onReset={handleReset}
            onAdd={handleAdd}
          />
        </div>

        {/* 角色列表表格 */}
        <RoleTable
          loading={loading}
          data={roles}
          currentPage={currentPage}
          pageSize={pageSize}
          total={total}
          onPageChange={handlePageChange}
          onStatusChange={handleStatusChange}
          onEdit={handleEdit}
        />
      </div>

      {/* 角色表單彈窗 */}
      <RoleForm
        visible={formVisible}
        title={formTitle}
        initialValues={initialValues}
        onCancel={handleFormCancel}
        onSubmit={handleFormSubmit}
        confirmLoading={confirmLoading}
      />
    </div>
  );
};

export default RoleManagement;
