/**
 * 账号数据类型定义
 */

/**
 * 账号状态枚举
 */
export enum AccountStatus {
  /** 有效 */
  ACTIVE = 'ACTIVE',
  /** 无效 */
  INACTIVE = 'INACTIVE',
}

/**
 * 账号数据接口
 */
export interface UserAccountData {
  /** 账号ID */
  id: string | number;
  /** 姓名 */
  name: string;
  /** 员工编码 */
  employeeCode: string;
  /** 邮箱 */
  email: string;
  /** 手机号码 */
  phone: string;
  /** 关联角色ID列表 */
  roles: string[];
  /** 账号状态 */
  status: AccountStatus;
  /** 更新人 */
  updatedBy: string;
  /** 更新时间 */
  updatedAt: string;
  /** 创建人（可选） */
  createdBy?: string;
  /** 创建时间（可选） */
  createdAt?: string;
  /** 部门（可选） */
  department?: string;
  /** 职位（可选） */
  position?: string;
}

/**
 * 角色選項接口
 */
export interface RoleOption {
  /** 角色ID */
  id: string | number;
  /** 角色名稱 */
  name: string;
  /** 角色狀態 */
  status: AccountStatus;
  /** 角色描述（可選） */
  description?: string;
  /** 權限列表（可選） */
  permissions?: string[];
}

/**
 * 帳號表格組件屬性接口
 */
export interface UserAccountTableProps {
  /** 加載狀態 */
  loading: boolean;
  /** 帳號數據列表 */
  data: UserAccountData[];
  /** 當前頁碼 */
  currentPage: number;
  /** 每頁條數 */
  pageSize: number;
  /** 總條數 */
  total: number;
  /** 頁碼變更回調 */
  onPageChange: (page: number, pageSize?: number) => void;
  /** 狀態變更回調 */
  onStatusChange: (id: string | number, status: AccountStatus) => void;
  /** 編輯回調 */
  onEdit: (account: UserAccountData) => void;
}

/**
 * 帳號工具欄組件屬性接口
 */
export interface UserAccountToolbarProps {
  /** 搜索文本 */
  searchText: string;
  /** 角色篩選 */
  roleFilter: string;
  /** 帳號狀態篩選 */
  accountStatus: string;
  /** 角色選項列表 */
  roleOptions: RoleOption[];
  /** 搜索文本變更回調 */
  onSearchTextChange: (value: string) => void;
  /** 角色篩選變更回調 */
  onRoleFilterChange: (value: string) => void;
  /** 帳號狀態變更回調 */
  onAccountStatusChange: (value: string) => void;
  /** 搜索回調 */
  onSearch: () => void;
  /** 重置回調 */
  onReset: () => void;
  /** 添加回調 */
  onAdd: () => void;
}

/**
 * 帳號表單組件屬性接口
 */
export interface UserAccountFormProps {
  /** 是否可見 */
  visible: boolean;
  /** 表單標題 */
  title: string;
  /** 初始值（可選） */
  initialValues?: Partial<UserAccountData>;
  /** 角色選項列表 */
  roleOptions: RoleOption[];
  /** 取消回調 */
  onCancel: () => void;
  /** 提交回調 */
  onSubmit: (values: Partial<UserAccountData>) => void;
  /** 確認加載狀態 */
  confirmLoading: boolean;
}

/**
 * 狀態標籤組件屬性接口
 */
export interface StatusTagProps {
  /** 帳號狀態 */
  status: AccountStatus;
  /** 自定義樣式（可選） */
  className?: string;
}

/**
 * 操作按鈕組件屬性接口
 */
export interface ActionButtonsProps {
  /** 帳號記錄 */
  record: UserAccountData;
  /** 編輯回調 */
  onEdit: (account: UserAccountData) => void;
  /** 狀態變更回調 */
  onStatusChange: (id: string | number, status: AccountStatus) => void;
}

/**
 * 帳號狀態變更日誌接口
 */
export interface UserAccountStatusLog {
  /** 日誌ID */
  id: string | number;
  /** 帳號ID */
  accountId: string | number;
  /** 舊狀態 */
  oldStatus: AccountStatus;
  /** 新狀態 */
  newStatus: AccountStatus;
  /** 更新人 */
  updatedBy: string;
  /** 更新時間 */
  updatedAt: string;
  /** 變更原因（可選） */
  reason?: string;
}

/**
 * 帳號角色變更日誌接口
 */
export interface UserAccountRoleLog {
  /** 日誌ID */
  id: string | number;
  /** 帳號ID */
  accountId: string | number;
  /** 舊角色列表 */
  oldRoles: string[];
  /** 新角色列表 */
  newRoles: string[];
  /** 更新人 */
  updatedBy: string;
  /** 更新時間 */
  updatedAt: string;
  /** 變更原因（可選） */
  reason?: string;
}

/**
 * 帳號信息變更字段枚舉
 */
export enum AccountInfoField {
  /** 姓名 */
  NAME = 'name',
  /** 郵箱 */
  EMAIL = 'email',
  /** 手機號碼 */
  PHONE = 'phone',
  /** 員工編碼 */
  EMPLOYEE_CODE = 'employeeCode',
  /** 部門 */
  DEPARTMENT = 'department',
  /** 職位 */
  POSITION = 'position',
}

/**
 * 帳號信息變更日誌接口
 */
export interface UserAccountInfoLog {
  /** 日誌ID */
  id: string | number;
  /** 帳號ID */
  accountId: string | number;
  /** 變更字段 */
  field: AccountInfoField;
  /** 舊值 */
  oldValue: string;
  /** 新值 */
  newValue: string;
  /** 更新人 */
  updatedBy: string;
  /** 更新時間 */
  updatedAt: string;
  /** 變更原因（可選） */
  reason?: string;
}

/**
 * 表單驗證規則接口
 */
export interface FormValidationRules {
  /** 姓名驗證規則 */
  name: {
    /** 是否必填 */
    required: boolean;
    /** 最大長度 */
    max: number;
    /** 錯誤信息 */
    message: string;
  };
  /** 員工編碼驗證規則 */
  employeeCode: {
    /** 是否必填 */
    required: boolean;
    /** 錯誤信息 */
    message: string;
  };
  /** 郵箱驗證規則 */
  email: {
    /** 是否必填 */
    required: boolean;
    /** 驗證類型 */
    type: 'email';
    /** 錯誤信息 */
    message: string;
  };
  /** 手機號碼驗證規則 */
  phone: {
    /** 是否必填 */
    required: boolean;
    /** 驗證正則表達式 */
    pattern: RegExp;
    /** 錯誤信息 */
    message: string;
  };
  /** 角色驗證規則 */
  roles: {
    /** 是否必填 */
    required: boolean;
    /** 錯誤信息 */
    message: string;
  };
}

/**
 * API 查詢參數接口
 */
export interface UserAccountQueryParams {
  /** 頁碼 */
  pageNum: number;
  /** 每頁條數 */
  pageSize: number;
  /** 搜索關鍵字（可選） */
  keyword?: string;
  /** 角色篩選（可選） */
  roleId?: string;
  /** 狀態篩選（可選） */
  status?: AccountStatus;
}

/**
 * API 響應數據接口
 */
export interface UserAccountListResponse {
  /** 帳號列表 */
  list: UserAccountData[];
  /** 總條數 */
  total: number;
  /** 頁碼 */
  pageNum: number;
  /** 每頁條數 */
  pageSize: number;
}
