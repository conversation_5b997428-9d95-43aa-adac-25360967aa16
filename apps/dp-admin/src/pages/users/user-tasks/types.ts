/**
 * 帐号数据类型
 */
export interface UserAccountData {
  id: string | number; // 帐号ID
  name: string; // 姓名
  employeeCode: string; // 员工编码
  email: string; // 邮箱
  phone: string; // 手机号码
  roles: string[]; // 关联角色
  status: 'ACTIVE' | 'INACTIVE'; // 帐号状态：有效/无效
  updatedBy: string; // 更新人
  updatedAt: string; // 更新时间
}

/**
 * 角色选项类型
 */
export interface RoleOption {
  id: string | number;
  name: string;
  status: 'ACTIVE' | 'INACTIVE';
}

/**
 * 帐号表格属性
 */
export interface UserAccountTableProps {
  loading: boolean;
  data: UserAccountData[];
  currentPage: number;
  pageSize: number;
  total: number;
  onPageChange: (page: number, pageSize?: number) => void;
  onStatusChange: (id: string | number, status: 'ACTIVE' | 'INACTIVE') => void;
  onEdit: (account: UserAccountData) => void;
}

/**
 * 帐号工具栏属性
 */
export interface UserAccountToolbarProps {
  searchText: string;
  roleFilter: string;
  accountStatus: string;
  roleOptions: RoleOption[];
  onSearchTextChange: (value: string) => void;
  onRoleFilterChange: (value: string) => void;
  onAccountStatusChange: (value: string) => void;
  onSearch: () => void;
  onReset: () => void;
  onAdd: () => void;
}

/**
 * 帐号表单属性
 */
export interface UserAccountFormProps {
  visible: boolean;
  title: string;
  initialValues?: Partial<UserAccountData>;
  roleOptions: RoleOption[];
  onCancel: () => void;
  onSubmit: (values: Partial<UserAccountData>) => void;
  confirmLoading: boolean;
}

/**
 * 状态标签属性
 */
export interface StatusTagProps {
  status: 'ACTIVE' | 'INACTIVE';
}

/**
 * 操作按钮属性
 */
export interface ActionButtonsProps {
  record: UserAccountData;
  onEdit: (account: UserAccountData) => void;
  onStatusChange: (id: string | number, status: 'ACTIVE' | 'INACTIVE') => void;
}

/**
 * 帐号状态变更日志
 */
export interface UserAccountStatusLog {
  id: string | number;
  accountId: string | number;
  oldStatus: 'ACTIVE' | 'INACTIVE';
  newStatus: 'ACTIVE' | 'INACTIVE';
  updatedBy: string;
  updatedAt: string;
}

/**
 * 帐号角色变更日志
 */
export interface UserAccountRoleLog {
  id: string | number;
  accountId: string | number;
  oldRoles: string[];
  newRoles: string[];
  updatedBy: string;
  updatedAt: string;
}

/**
 * 帐号信息变更日志
 */
export interface UserAccountInfoLog {
  id: string | number;
  accountId: string | number;
  field: 'name' | 'email' | 'phone' | 'employeeCode';
  oldValue: string;
  newValue: string;
  updatedBy: string;
  updatedAt: string;
}

/**
 * 表单验证规则
 */
export interface FormValidationRules {
  name: {
    required: boolean;
    max: number;
    message: string;
  };
  employeeCode: {
    required: boolean;
    message: string;
  };
  email: {
    required: boolean;
    type: 'email';
    message: string;
  };
  phone: {
    required: boolean;
    pattern: RegExp;
    message: string;
  };
  roles: {
    required: boolean;
    message: string;
  };
}
