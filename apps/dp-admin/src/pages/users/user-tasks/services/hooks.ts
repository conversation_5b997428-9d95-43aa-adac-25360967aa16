import { message } from 'antd';
import { useEffect, useState } from 'react';

import type { RoleOption, UserAccountData } from '../types';
import {
  createUserAccount,
  getRoleOptions,
  getUserAccountList,
  logUserAccountChange,
  updateUserAccount,
  updateUserAccountStatus,
} from './api';

/**
 * 帐号管理 Hook
 * 处理帐号列表的获取、分页和状态管理
 */
export const useUserAccountManagement = () => {
  // 筛选状态
  interface FilterState {
    currentPage: number;
    pageSize: number;
    searchText: string;
    roleFilter: string;
    accountStatus: string;
  }

  const [filterState, setFilterState] = useState<FilterState>({
    currentPage: 1,
    pageSize: 10,
    searchText: '',
    roleFilter: 'ALL',
    accountStatus: 'ALL',
  });

  // 列表状态
  const [loading, setLoading] = useState(false);
  const [accounts, setAccounts] = useState<UserAccountData[]>([]);
  const [total, setTotal] = useState(0);
  const [roleOptions, setRoleOptions] = useState<RoleOption[]>([]);

  // 表单状态
  const [formVisible, setFormVisible] = useState(false);
  const [formTitle, setFormTitle] = useState('');
  const [initialValues, setInitialValues] =
    useState<Partial<UserAccountData>>();
  const [confirmLoading, setConfirmLoading] = useState(false);

  // 获取帐号列表数据
  const fetchUserAccountList = async () => {
    setLoading(true);
    try {
      const params = {
        currentPage: filterState.currentPage,
        pageSize: filterState.pageSize,
        searchText: filterState.searchText,
        roleFilter:
          filterState.roleFilter === 'ALL' ? '' : filterState.roleFilter,
        accountStatus:
          filterState.accountStatus === 'ALL' ? '' : filterState.accountStatus,
      };

      const [success, res] = await getUserAccountList(params);
      if (success && res) {
        setAccounts(res.accounts || []);
        setTotal(res.total || 0);
      } else {
        setAccounts([]);
        setTotal(0);
        message.error('獲取帳號列表失敗');
      }
    } catch (error) {
      console.error('获取帐号列表失败:', error);
      message.error('獲取帳號列表失敗');
    } finally {
      setLoading(false);
    }
  };

  // 获取角色选项
  const fetchRoleOptions = async () => {
    const [success, data] = await getRoleOptions();
    if (success && data) {
      setRoleOptions(data);
    } else {
      setRoleOptions([]);
    }
  };

  // 处理页码变更
  const handlePageChange = (page: number, pageSize?: number) => {
    setFilterState({
      ...filterState,
      currentPage: page,
      pageSize: pageSize || filterState.pageSize,
    });
    setTimeout(() => fetchUserAccountList(), 0);
  };

  // 处理搜索文本变更
  const handleSearchTextChange = (value: string) => {
    setFilterState({
      ...filterState,
      searchText: value,
    });
  };

  // 处理角色筛选变更
  const handleRoleFilterChange = (value: string) => {
    setFilterState({
      ...filterState,
      roleFilter: value,
    });
  };

  // 处理帐号状态筛选变更
  const handleAccountStatusChange = (value: string) => {
    setFilterState({
      ...filterState,
      accountStatus: value,
    });
  };

  // 处理搜索
  const handleSearch = () => {
    setFilterState({
      ...filterState,
      currentPage: 1,
    });
    setTimeout(() => fetchUserAccountList(), 0);
  };

  // 重置搜索条件
  const handleReset = () => {
    setFilterState({
      currentPage: 1,
      pageSize: 10,
      searchText: '',
      roleFilter: 'ALL',
      accountStatus: 'ALL',
    });
    setTimeout(() => fetchUserAccountList(), 0);
  };

  // 处理新增帐号
  const handleAdd = () => {
    setFormTitle('新增帳號');
    setInitialValues(undefined);
    setFormVisible(true);
  };

  // 处理编辑帐号
  const handleEdit = (account: UserAccountData) => {
    setFormTitle('修改帳號');
    setInitialValues(account);
    setFormVisible(true);
  };

  // 处理表单取消
  const handleFormCancel = () => {
    setFormVisible(false);
    setInitialValues(undefined);
  };

  // 处理表单提交
  const handleFormSubmit = async (values: Partial<UserAccountData>) => {
    setConfirmLoading(true);
    try {
      let success = false;

      if (initialValues?.id) {
        // 编辑模式
        [success] = await updateUserAccount(initialValues.id, values);
        if (success) {
          message.success('修改帳號成功');
          // 记录变更日志
          await logUserAccountChange({
            accountId: initialValues.id,
            changeType: 'info',
            oldValue: initialValues,
            newValue: values,
          });
        } else {
          message.error('修改帳號失敗');
        }
      } else {
        // 新增模式
        [success] = await createUserAccount(values);
        if (success) {
          message.success('新增帳號成功');
        } else {
          message.error('新增帳號失敗');
        }
      }

      if (success) {
        setFormVisible(false);
        setInitialValues(undefined);
        fetchUserAccountList();
      }
    } catch (error) {
      console.error('表单提交失败:', error);
      message.error('操作失敗');
    } finally {
      setConfirmLoading(false);
    }
  };

  // 处理状态变更
  const handleStatusChange = async (
    id: string | number,
    status: 'ACTIVE' | 'INACTIVE'
  ) => {
    try {
      const [success] = await updateUserAccountStatus(id, status);
      if (success) {
        message.success('帳號狀態更新成功');
        // 记录变更日志
        await logUserAccountChange({
          accountId: id,
          changeType: 'status',
          oldValue: status === 'ACTIVE' ? 'INACTIVE' : 'ACTIVE',
          newValue: status,
        });
        fetchUserAccountList();
      } else {
        message.error('帳號狀態更新失敗');
      }
    } catch (error) {
      console.error('状态变更失败:', error);
      message.error('帳號狀態更新失敗');
    }
  };

  // 初始加载
  useEffect(() => {
    fetchRoleOptions();
    fetchUserAccountList();
  }, []);

  return {
    loading,
    accounts,
    total,
    currentPage: filterState.currentPage,
    pageSize: filterState.pageSize,
    searchText: filterState.searchText,
    roleFilter: filterState.roleFilter,
    accountStatus: filterState.accountStatus,
    roleOptions,
    formVisible,
    formTitle,
    initialValues,
    confirmLoading,
    handlePageChange,
    handleSearchTextChange,
    handleRoleFilterChange,
    handleAccountStatusChange,
    handleSearch,
    handleReset,
    handleAdd,
    handleEdit,
    handleFormCancel,
    handleFormSubmit,
    handleStatusChange,
  };
};
