import type { RoleOption, UserAccountData } from '../types';

// API 响应类型定义
interface UserAccountListResponse {
  accounts: UserAccountData[];
  total: number;
}

interface UserAccountResponse {
  id: string | number;
  [key: string]: unknown;
}

interface UserAccountStatusResponse {
  id: string | number;
  status: 'ACTIVE' | 'INACTIVE';
}

interface UserAccountLogResponse {
  id: string | number;
  accountId: string | number;
  changeType: 'status' | 'role' | 'info';
  oldValue: unknown;
  newValue: unknown;
  field?: string;
}

/**
 * 获取帐号列表
 */
export const getUserAccountList = async (_params: {
  currentPage: number;
  pageSize: number;
  searchText?: string;
  roleFilter?: string;
  accountStatus?: string;
}): Promise<[boolean, UserAccountListResponse | null]> => {
  try {
    // 模拟API调用
    const mockData: UserAccountListResponse = {
      accounts: [
        {
          id: 1,
          name: '張佩恩',
          employeeCode: 'wuji.zhang',
          email: '****<EMAIL>',
          phone: '+886 778***565',
          roles: ['運營主管', '商務'],
          status: 'ACTIVE' as const,
          updatedBy: '楊云(Chara.Yang)',
          updatedAt: '2025-3-20 14:09',
        },
        {
          id: 2,
          name: '張佩恩',
          employeeCode: 'wuji.zhang',
          email: '****<EMAIL>',
          phone: '+886 778***565',
          roles: ['超級管理員', '商務主管'],
          status: 'ACTIVE' as const,
          updatedBy: '楊云(Chara.Yang)',
          updatedAt: '2025-3-20 14:09',
        },
        {
          id: 3,
          name: '張佩恩',
          employeeCode: 'wuji.zhang',
          email: '****<EMAIL>',
          phone: '+886 778***565',
          roles: ['超級管理員', '商務主管'],
          status: 'INACTIVE' as const,
          updatedBy: '楊云(Chara.Yang)',
          updatedAt: '2025-3-20 14:09',
        },
        {
          id: 4,
          name: '張佩恩',
          employeeCode: 'wuji.zhang',
          email: '****<EMAIL>',
          phone: '+886 778***565',
          roles: ['超級管理員', '商務主管'],
          status: 'ACTIVE' as const,
          updatedBy: '楊云(Chara.Yang)',
          updatedAt: '2025-3-20 14:09',
        },
      ],
      total: 4,
    };

    // 模拟网络延迟
    await new Promise((resolve) => setTimeout(resolve, 500));

    return [true, mockData];
  } catch (error) {
    console.error('获取帐号列表失败:', error);
    return [false, null];
  }
};

/**
 * 获取角色选项列表
 */
export const getRoleOptions = async (): Promise<
  [boolean, RoleOption[] | null]
> => {
  try {
    // 模拟API调用
    const mockData: RoleOption[] = [
      { id: 1, name: '超級管理員', status: 'ACTIVE' },
      { id: 2, name: '運營主管', status: 'ACTIVE' },
      { id: 3, name: '商務主管', status: 'ACTIVE' },
      { id: 4, name: '商務', status: 'ACTIVE' },
      { id: 5, name: '客服', status: 'ACTIVE' },
      { id: 6, name: '財務', status: 'INACTIVE' },
    ];

    // 模拟网络延迟
    await new Promise((resolve) => setTimeout(resolve, 300));

    return [true, mockData];
  } catch (error) {
    console.error('获取角色选项失败:', error);
    return [false, null];
  }
};

/**
 * 创建帐号
 */
export const createUserAccount = async (
  data: Partial<UserAccountData>
): Promise<[boolean, UserAccountResponse | null]> => {
  try {
    // 模拟API调用
    console.log('创建帐号:', data);

    // 模拟网络延迟
    await new Promise((resolve) => setTimeout(resolve, 1000));

    const response: UserAccountResponse = { id: Date.now(), ...data };
    return [true, response];
  } catch (error) {
    console.error('创建帐号失败:', error);
    return [false, null];
  }
};

/**
 * 更新帐号信息
 */
export const updateUserAccount = async (
  id: string | number,
  data: Partial<UserAccountData>
): Promise<[boolean, UserAccountResponse | null]> => {
  try {
    // 模拟API调用
    console.log('更新帐号:', id, data);

    // 模拟网络延迟
    await new Promise((resolve) => setTimeout(resolve, 1000));

    const response: UserAccountResponse = { id, ...data };
    return [true, response];
  } catch (error) {
    console.error('更新帐号失败:', error);
    return [false, null];
  }
};

/**
 * 更新帐号状态
 */
export const updateUserAccountStatus = async (
  id: string | number,
  status: 'ACTIVE' | 'INACTIVE'
): Promise<[boolean, UserAccountStatusResponse | null]> => {
  try {
    // 模拟API调用
    console.log('更新帐号状态:', id, status);

    // 模拟网络延迟
    await new Promise((resolve) => setTimeout(resolve, 800));

    const response: UserAccountStatusResponse = { id, status };
    return [true, response];
  } catch (error) {
    console.error('更新帐号状态失败:', error);
    return [false, null];
  }
};

/**
 * 记录帐号变更日志
 */
export const logUserAccountChange = async (logData: {
  accountId: string | number;
  changeType: 'status' | 'role' | 'info';
  oldValue: unknown;
  newValue: unknown;
  field?: string;
}): Promise<[boolean, UserAccountLogResponse | null]> => {
  try {
    // 模拟API调用
    console.log('记录帐号变更日志:', logData);

    // 模拟网络延迟
    await new Promise((resolve) => setTimeout(resolve, 200));

    const response: UserAccountLogResponse = { id: Date.now(), ...logData };
    return [true, response];
  } catch (error) {
    console.error('记录帐号变更日志失败:', error);
    return [false, null];
  }
};
