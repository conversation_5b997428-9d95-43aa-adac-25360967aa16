import React from 'react';

import UserAccountForm from './components/UserAccountForm/index.web';
import UserAccountTable from './components/UserAccountTable/index.web';
import UserAccountToolbar from './components/UserAccountToolbar/index.web';
import { useUserAccountManagement } from './services/hooks';

/**
 * 帳號管理頁面
 *
 * 用於管理系統帳號，包括查看、新增、編輯和刪除帳號，以及設置帳號角色
 */
const UserAccountManagement: React.FC = () => {
  // 使用自定義 hooks 獲取數據和狀態
  const {
    loading,
    accounts,
    total,
    currentPage,
    pageSize,
    searchText,
    roleFilter,
    accountStatus,
    roleOptions,
    formVisible,
    formTitle,
    initialValues,
    confirmLoading,
    handlePageChange,
    handleSearchTextChange,
    handleRoleFilterChange,
    handleAccountStatusChange,
    handleSearch,
    handleReset,
    handleAdd,
    handleEdit,
    handleFormCancel,
    handleFormSubmit,
    handleStatusChange,
  } = useUserAccountManagement();

  return (
    <div className="bg-white p-4 sm:p-6 md:p-8 rounded-lg shadow-sm">
      <div className="mb-6">
        <h1 className="text-xl sm:text-2xl font-medium mb-6">帳號管理</h1>

        {/* 工具欄：搜索、篩選和操作按鈕 */}
        <div className="mb-6">
          <UserAccountToolbar
            searchText={searchText}
            roleFilter={roleFilter}
            accountStatus={accountStatus}
            roleOptions={roleOptions}
            onSearchTextChange={handleSearchTextChange}
            onRoleFilterChange={handleRoleFilterChange}
            onAccountStatusChange={handleAccountStatusChange}
            onSearch={handleSearch}
            onReset={handleReset}
            onAdd={handleAdd}
          />
        </div>

        {/* 帳號列表表格 */}
        <UserAccountTable
          loading={loading}
          data={accounts}
          currentPage={currentPage}
          pageSize={pageSize}
          total={total}
          onPageChange={handlePageChange}
          onStatusChange={handleStatusChange}
          onEdit={handleEdit}
        />
      </div>

      {/* 帳號表單彈窗 */}
      <UserAccountForm
        visible={formVisible}
        title={formTitle}
        initialValues={initialValues}
        roleOptions={roleOptions}
        onCancel={handleFormCancel}
        onSubmit={handleFormSubmit}
        confirmLoading={confirmLoading}
      />
    </div>
  );
};

export default UserAccountManagement;
