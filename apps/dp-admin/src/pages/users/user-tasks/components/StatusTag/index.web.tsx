import { Tag } from 'antd';
import React from 'react';

import type { StatusTagProps } from '../../types';

/**
 * 状态标签组件
 *
 * 用于显示帐号的状态（有效/无效）
 */
const StatusTag: React.FC<StatusTagProps> = ({ status }) => {
  const getStatusConfig = () => {
    switch (status) {
      case 'ACTIVE':
        return {
          color: 'green',
          text: '有效',
        };
      case 'INACTIVE':
        return {
          color: 'red',
          text: '無效',
        };
      default:
        return {
          color: 'default',
          text: '未知',
        };
    }
  };

  const config = getStatusConfig();

  return (
    <Tag color={config.color} className="text-xs sm:text-sm">
      {config.text}
    </Tag>
  );
};

export default StatusTag;
