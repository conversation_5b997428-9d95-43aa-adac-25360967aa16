import {
  PlusOutlined,
  ReloadOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import { Button, Col, Input, Row, Select, Space } from 'antd';
import React from 'react';

import type { UserAccountToolbarProps } from '../../types';

const { Option } = Select;

/**
 * 帐号工具栏组件
 *
 * 提供搜索、筛选和新增功能
 */
const UserAccountToolbar: React.FC<UserAccountToolbarProps> = ({
  searchText,
  roleFilter,
  accountStatus,
  roleOptions,
  onSearchTextChange,
  onRoleFilterChange,
  onAccountStatusChange,
  onSearch,
  onReset,
  onAdd,
}) => {
  return (
    <div className="bg-white p-4 rounded-lg border border-gray-200 mb-4">
      <Row gutter={[16, 16]} align="middle">
        {/* 搜索框 */}
        <Col xs={24} sm={12} md={8} lg={6}>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              搜尋
            </label>
            <Input
              placeholder="請輸入姓名、員工編碼、郵箱"
              value={searchText}
              onChange={(e) => onSearchTextChange(e.target.value)}
              allowClear
              className="w-full"
            />
          </div>
        </Col>

        {/* 角色筛选 */}
        <Col xs={24} sm={12} md={8} lg={6}>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              角色篩選
            </label>
            <Select
              placeholder="請選擇角色"
              value={roleFilter}
              onChange={onRoleFilterChange}
              allowClear
              className="w-full"
            >
              <Option value="ALL">全部角色</Option>
              {roleOptions
                .filter((role) => role.status === 'ACTIVE')
                .map((role) => (
                  <Option key={role.id} value={role.name}>
                    {role.name}
                  </Option>
                ))}
            </Select>
          </div>
        </Col>

        {/* 状态筛选 */}
        <Col xs={24} sm={12} md={8} lg={6}>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              狀態
            </label>
            <Select
              value={accountStatus}
              onChange={onAccountStatusChange}
              className="w-full"
            >
              <Option value="ALL">全部狀態</Option>
              <Option value="ACTIVE">有效</Option>
              <Option value="INACTIVE">無效</Option>
            </Select>
          </div>
        </Col>
      </Row>

      <Row className="mt-4">
        {/* 操作按钮 */}
        <div className="flex flex-row gap-2">
          <Space size="small" className="flex-wrap">
            <Button
              icon={<ReloadOutlined />}
              onClick={onReset}
              className="flex-1 sm:flex-none"
            >
              重置
            </Button>
            <Button
              type="primary"
              icon={<SearchOutlined />}
              onClick={onSearch}
              className="flex-1 sm:flex-none"
            >
              搜尋
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={onAdd}
              className="bg-blue-600 hover:bg-blue-700"
            >
              新增帳號
            </Button>
          </Space>
        </div>
      </Row>
    </div>
  );
};

export default UserAccountToolbar;
