import { Table, Tag } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import React from 'react';

import type { UserAccountData, UserAccountTableProps } from '../../types';
import ActionButtons from '../ActionButtons/index.web';
import StatusTag from '../StatusTag/index.web';

/**
 * 帐号表格组件
 *
 * 显示帐号列表和相关操作
 */
const UserAccountTable: React.FC<UserAccountTableProps> = ({
  loading,
  data,
  currentPage,
  pageSize,
  total,
  onPageChange,
  onStatusChange,
  onEdit,
}) => {
  // 表格列配置
  const columns: ColumnsType<UserAccountData> = [
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
      width: 100,
      render: (text: string) => (
        <span className="font-medium text-gray-900 text-sm">{text}</span>
      ),
    },
    {
      title: '員工編碼',
      dataIndex: 'employeeCode',
      key: 'employeeCode',
      width: 120,
      render: (text: string) => (
        <span className="text-gray-700 text-sm">{text}</span>
      ),
    },
    {
      title: '郵箱',
      dataIndex: 'email',
      key: 'email',
      width: 180,
      render: (text: string) => (
        <span className="text-gray-700 text-sm">{text}</span>
      ),
    },
    {
      title: '手機號',
      dataIndex: 'phone',
      key: 'phone',
      width: 140,
      render: (text: string) => (
        <span className="text-gray-700 text-sm">{text}</span>
      ),
    },
    {
      title: '角色',
      dataIndex: 'roles',
      key: 'roles',
      width: 200,
      render: (roles: string[]) => (
        <div className="flex flex-wrap gap-1">
          {roles.map((role, index) => (
            <Tag key={index} color="blue" className="text-xs mb-1">
              {role}
            </Tag>
          ))}
        </div>
      ),
    },
    {
      title: '狀態',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: 'ACTIVE' | 'INACTIVE') => <StatusTag status={status} />,
    },
    {
      title: '最後更新',
      key: 'lastUpdate',
      width: 180,
      render: (_, record) => (
        <div className="text-sm">
          <div className="text-gray-900 font-medium">{record.updatedBy}</div>
          <div className="text-gray-500 text-xs">{record.updatedAt}</div>
        </div>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      fixed: 'right',
      render: (_, record) => (
        <ActionButtons
          record={record}
          onEdit={onEdit}
          onStatusChange={onStatusChange}
        />
      ),
    },
  ];

  return (
    <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
      <Table
        columns={columns}
        dataSource={data}
        rowKey="id"
        loading={loading}
        pagination={{
          current: currentPage,
          pageSize: pageSize,
          total: total,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 條，共 ${total} 條`,
          onChange: onPageChange,
          onShowSizeChange: onPageChange,
          pageSizeOptions: ['10', '20', '50', '100'],
          className: 'px-4 py-3',
        }}
        scroll={{ x: 1000 }}
        size="middle"
        className="w-full"
        rowClassName="hover:bg-gray-50"
      />
    </div>
  );
};

export default UserAccountTable;
