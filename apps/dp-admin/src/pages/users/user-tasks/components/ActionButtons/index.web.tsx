import { Button, Modal, Space } from 'antd';
import React from 'react';

import type { ActionButtonsProps } from '../../types';

/**
 * 操作按钮组件
 *
 * 提供修改和状态切换功能
 */
const ActionButtons: React.FC<ActionButtonsProps> = ({
  record,
  onEdit,
  onStatusChange,
}) => {
  // 处理状态切换
  const handleStatusToggle = () => {
    const newStatus = record.status === 'ACTIVE' ? 'INACTIVE' : 'ACTIVE';
    const statusText = newStatus === 'ACTIVE' ? '啟用' : '停用';

    Modal.confirm({
      title: '確認操作',
      content: `確定要${statusText}此帳號嗎？`,
      okText: '確認',
      cancelText: '取消',
      onOk: () => {
        onStatusChange(record.id, newStatus);
      },
    });
  };

  // 处理编辑
  const handleEdit = () => {
    onEdit(record);
  };

  return (
    <Space size="small" className="flex-wrap">
      <Button
        type="link"
        size="small"
        onClick={handleEdit}
        className="p-0 h-auto text-blue-600 hover:text-blue-800 text-xs sm:text-sm"
      >
        修改
      </Button>
      <Button
        type="link"
        size="small"
        onClick={handleStatusToggle}
        danger={record.status === 'ACTIVE'}
        className="p-0 h-auto text-xs sm:text-sm"
      >
        {record.status === 'ACTIVE' ? '停用' : '啟用'}
      </Button>
    </Space>
  );
};

export default ActionButtons;
