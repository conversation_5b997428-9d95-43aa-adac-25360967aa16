import { Form, Input, Modal, Select } from 'antd';
import React, { useEffect } from 'react';

import type { UserAccountFormProps } from '../../types';

const { Option } = Select;

/**
 * 帐号表单组件
 *
 * 用于新增和编辑帐号信息
 */
const UserAccountForm: React.FC<UserAccountFormProps> = ({
  visible,
  title,
  initialValues,
  roleOptions,
  onCancel,
  onSubmit,
  confirmLoading,
}) => {
  const [form] = Form.useForm();

  // 当初始值变化时更新表单
  useEffect(() => {
    if (visible) {
      if (initialValues) {
        form.setFieldsValue(initialValues);
      } else {
        form.resetFields();
      }
    }
  }, [visible, initialValues, form]);

  // 处理表单提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      onSubmit(values);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 处理取消
  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  // 表单验证规则
  const validationRules = {
    name: [
      { required: true, message: '請輸入姓名' },
      { max: 20, message: '姓名不能超過20個字符' },
    ],
    employeeCode: [{ required: true, message: '請輸入員工編碼' }],
    email: [
      { required: true, message: '請輸入郵箱' },
      { type: 'email' as const, message: '請輸入正確的郵箱格式' },
    ],
    phone: [
      { required: true, message: '請輸入手機號碼' },
      {
        pattern: /^(\+886|886|0)?[0-9]{8,10}$/,
        message: '請輸入正確的手機號碼格式',
      },
    ],
    roles: [{ required: true, message: '請選擇角色' }],
  };

  return (
    <Modal
      title={title}
      open={visible}
      onOk={handleSubmit}
      onCancel={handleCancel}
      confirmLoading={confirmLoading}
      okText="確認"
      cancelText="取消"
      width={600}
      className="top-8"
    >
      <Form form={form} layout="vertical" className="mt-4" autoComplete="off">
        <Form.Item name="name" label="姓名" rules={validationRules.name}>
          <Input
            placeholder="請輸入姓名"
            maxLength={20}
            showCount
            className="w-full"
          />
        </Form.Item>

        <Form.Item
          name="employeeCode"
          label="員工編碼"
          rules={validationRules.employeeCode}
        >
          <Input placeholder="請輸入員工編碼" className="w-full" />
        </Form.Item>

        <Form.Item name="email" label="郵箱" rules={validationRules.email}>
          <Input placeholder="請輸入郵箱地址" className="w-full" />
        </Form.Item>

        <Form.Item name="phone" label="手機號碼" rules={validationRules.phone}>
          <Input placeholder="請輸入手機號碼" className="w-full" />
        </Form.Item>

        <Form.Item name="roles" label="角色" rules={validationRules.roles}>
          <Select
            mode="multiple"
            placeholder="請選擇角色"
            className="w-full"
            optionFilterProp="children"
            filterOption={(input, option) =>
              String(option?.children || '')
                .toLowerCase()
                .includes(input.toLowerCase())
            }
          >
            {roleOptions
              .filter((role) => role.status === 'ACTIVE')
              .map((role) => (
                <Option key={role.id} value={role.name}>
                  {role.name}
                </Option>
              ))}
          </Select>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default UserAccountForm;
