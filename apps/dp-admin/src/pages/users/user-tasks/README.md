# 帳號管理頁面

## 功能概述

帳號管理頁面提供了完整的系統帳號管理功能，包括帳號的查看、新增、編輯、狀態管理以及角色分配。

## 主要功能

### 1. 帳號列表展示
- 顯示帳號基本信息：姓名、員工編碼、郵箱、手機號碼
- 顯示關聯角色和帳號狀態
- 顯示最後更新人和更新時間
- 支持響應式設計，適配手機、平板、電腦

### 2. 搜索和篩選
- **搜索功能**：支持按姓名、員工編碼、郵箱進行搜索
- **角色篩選**：可按角色類型篩選帳號
- **狀態篩選**：可按帳號狀態（有效/無效）篩選

### 3. 帳號管理操作
- **新增帳號**：創建新的系統帳號
- **編輯帳號**：修改帳號基本信息和角色分配
- **狀態管理**：啟用或停用帳號（需二次確認）

### 4. 權限控制
- **查看權限**：有「查看帳號」權限的用戶可以看到帳號列表
- **管理權限**：有「管理帳號」權限的用戶可以進行新增、修改操作

## 技術實現

### 組件結構
```
user-tasks/
├── components/
│   ├── ActionButtons/          # 操作按鈕組件
│   ├── StatusTag/              # 狀態標籤組件
│   ├── UserAccountForm/        # 帳號表單組件
│   ├── UserAccountTable/       # 帳號表格組件
│   └── UserAccountToolbar/     # 工具欄組件
├── services/
│   ├── api.ts                  # API 接口
│   ├── hooks.ts                # 自定義 Hooks
│   └── index.ts                # 服務入口
├── types.ts                    # 類型定義
├── index.web.tsx               # 主頁面組件
└── README.md                   # 文檔
```

### 核心技術
- **React 18** + **TypeScript**：組件開發
- **Ant Design 5**：UI 組件庫
- **Tailwind CSS 4**：樣式方案
- **React Hooks**：狀態管理

### 響應式設計
- **移動端**：單列布局，操作按鈕垂直排列
- **平板端**：兩列布局，適當調整間距
- **桌面端**：多列布局，完整功能展示

## 數據流程

### 1. 帳號創建流程
1. 填寫帳號基本信息（姓名、員工編碼、郵箱、手機號碼）
2. 選擇關聯角色（可多選，僅顯示有效角色）
3. 提交創建請求
4. 創建成功後記錄第一條更新記錄
5. 帳號狀態設置為有效

### 2. 帳號修改流程
1. 選擇要修改的帳號
2. 修改帳號信息或角色分配
3. 提交修改請求
4. 記錄變更日誌（包含修改人、修改內容、修改前後值）
5. 更新帳號信息

### 3. 狀態變更流程
1. 點擊狀態切換按鈕
2. 彈出二次確認對話框
3. 確認後提交狀態變更請求
4. 記錄狀態變更日誌
5. 更新帳號狀態

## 表單驗證

### 驗證規則
- **姓名**：必填，不超過20個字符
- **員工編碼**：必填
- **郵箱**：必填，需符合郵箱格式
- **手機號碼**：必填，需符合台灣手機號碼格式
- **角色**：必填，至少選擇一個角色

### 錯誤處理
- 表單驗證失敗時顯示具體錯誤信息
- API 請求失敗時顯示友好的錯誤提示
- 網絡異常時提供重試機制

## 日誌記錄

### 記錄內容
- **狀態變更**：記錄帳號狀態的變更
- **角色變更**：記錄角色分配的變更
- **信息變更**：記錄基本信息的變更

### 日誌字段
- 變更人：執行操作的用戶
- 變更時間：操作執行的時間
- 變更內容：具體的變更信息
- 變更前值：修改前的值
- 變更後值：修改後的值

## 使用說明

### 查看帳號列表
1. 進入帳號管理頁面
2. 系統自動載入帳號列表
3. 可使用搜索和篩選功能查找特定帳號

### 新增帳號
1. 點擊「新增帳號」按鈕
2. 填寫帳號基本信息
3. 選擇關聯角色
4. 點擊「確認」提交

### 修改帳號
1. 在帳號列表中點擊「修改」按鈕
2. 修改需要變更的信息
3. 點擊「確認」提交

### 狀態管理
1. 在帳號列表中點擊「啟用帳號」或「停用帳號」
2. 在確認對話框中點擊「確認」
3. 系統更新帳號狀態

## 注意事項

1. **權限限制**：僅有效狀態的角色的有效狀態的帳號可以應用相關權限
2. **數據安全**：所有操作都會記錄詳細的變更日誌
3. **用戶體驗**：重要操作（如狀態變更）需要二次確認
4. **響應式適配**：確保在不同設備上都有良好的使用體驗
