# 權限管理頁面

## 功能概述

權限管理頁面提供了完整的系統權限管理功能，包括權限的查看、新增、編輯、狀態管理以及權限入口分配。運營人員可以在此頁面管理不同權限的範圍，控制用戶對系統功能的訪問權限。

## 主要功能

### 1. 權限列表展示
- 顯示權限基本信息：權限入口、權限名稱、權限說明、權限Key值
- 顯示關聯角色和權限狀態
- 顯示最後更新人和更新時間
- 支持響應式設計，適配手機、平板、電腦

### 2. 搜索和篩選
- **搜索功能**：支持按權限名稱、權限Key值、權限說明進行搜索
- **權限入口篩選**：可按權限入口類型篩選權限
- **狀態篩選**：可按權限狀態（有效/無效）篩選

### 3. 權限管理操作
- **新增權限**：創建新的系統權限
- **編輯權限**：修改權限基本信息和權限入口分配
- **狀態管理**：啟用或停用權限（需二次確認）

### 4. 權限控制
- **查看權限**：有「查看權限」權限的用戶可以看到權限列表
- **管理權限**：有「管理權限」權限的用戶可以進行新增、修改操作
- **創建權限入口**：有「創建權限入口」權限的用戶可以創建新的權限入口

## 技術實現

### 組件結構
```
permissions/
├── components/
│   ├── ActionButtons/          # 操作按鈕組件
│   ├── PermissionForm/         # 權限表單組件
│   ├── PermissionTable/        # 權限表格組件
│   ├── PermissionToolbar/      # 工具欄組件
│   └── StatusTag/              # 狀態標籤組件
├── services/
│   ├── api.ts                  # API 接口
│   └── hooks.ts                # 自定義 Hooks
├── types.ts                    # 類型定義
├── index.web.tsx               # 主頁面組件
└── README.md                   # 文檔
```

### 核心技術
- **React 18** + **TypeScript**：組件開發
- **Ant Design 5**：UI 組件庫
- **Tailwind CSS 4**：樣式方案
- **React Hooks**：狀態管理

### 響應式設計
- **移動端**：單列布局，操作按鈕垂直排列
- **平板端**：兩列布局，適當調整間距
- **桌面端**：多列布局，完整功能展示

## 數據流程

### 1. 權限創建流程
1. 選擇權限入口（有創建權限入口權限的可創建新入口）
2. 填寫權限名稱（必填，不超過40個字符，提交時校驗唯一性）
3. 填寫權限說明（選填，不超過200個字符）
4. 填寫權限Key值（必填，不超過200個字符，提交時校驗唯一性）
5. 提交創建請求
6. 創建成功後記錄第一條更新記錄
7. 權限狀態設置為有效

### 2. 權限修改流程
1. 選擇要修改的權限
2. 修改權限信息（支持修改權限入口、名稱、說明、Key值）
3. 提交修改請求（提交時校驗唯一性）
4. 記錄變更日誌（包含修改人、修改內容、修改前後值）
5. 更新權限信息

### 3. 狀態變更流程
1. 點擊狀態切換按鈕
2. 彈出二次確認對話框
3. 確認後提交狀態變更請求
4. 記錄狀態變更日誌
5. 更新權限狀態

## 表單驗證

### 驗證規則
- **權限名稱**：必填，不超過40個字符，提交時校驗唯一性
- **權限說明**：選填，不超過200個字符
- **權限Key值**：必填，不超過200個字符，提交時校驗唯一性
- **權限入口**：必填

### 錯誤處理
- 表單驗證失敗時顯示具體錯誤信息
- API 請求失敗時顯示友好的錯誤提示
- 網絡異常時提供重試機制

## 日誌記錄

### 記錄內容
- **狀態變更**：記錄權限狀態的變更
- **信息變更**：記錄基本信息的變更

### 日誌字段
- 變更人：執行操作的用戶
- 變更時間：操作執行的時間
- 變更內容：具體的變更信息
- 變更前值：修改前的值
- 變更後值：修改後的值

## 使用說明

### 查看權限列表
1. 進入權限管理頁面
2. 系統自動載入權限列表
3. 可使用搜索和篩選功能查找特定權限

### 新增權限
1. 點擊「新增權限」按鈕
2. 選擇權限入口
3. 填寫權限基本信息
4. 點擊「確認」提交

### 編輯權限
1. 在權限列表中點擊「修改」按鈕
2. 修改權限信息
3. 點擊「確認」保存

### 管理權限狀態
1. 在權限列表中點擊「啟用」或「停用」按鈕
2. 確認操作
3. 系統更新權限狀態

## 權限控制說明

### 權限說明
- **查看權限**：有該列表的「查看權限」權限的可以看到標籤入口
- **管理權限**：有「管理權限」權限的可以進行新增、修改權限信息、修改權限狀態
- **創建權限入口**：有「創建權限入口」權限的人可以進行權限入口創建，否則僅可在現有入口中選擇

### 功能限制
- 修改權限狀態操作需要二次確認
- 修改動作都記錄修改日誌，包含：誰，修改什麼信息，修改前什麼內容，修改後什麼內容
- 列表僅顯示最近一次更新信息的更新人和更新時間

## 注意事項

1. **唯一性校驗**：權限名稱和權限Key值在系統中必須唯一
2. **權限入口管理**：只有有相應權限的用戶才能創建新的權限入口
3. **狀態變更確認**：所有狀態變更操作都需要用戶二次確認
4. **日誌記錄**：所有修改操作都會記錄詳細的變更日誌
5. **響應式支持**：頁面支持多種設備尺寸，確保良好的用戶體驗
