import { Table, Tag, Tooltip } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import React from 'react';

import type { PermissionData, PermissionTableProps } from '../../types';
import ActionButtons from '../ActionButtons/index.web';
import StatusTag from '../StatusTag/index.web';

/**
 * 权限表格组件
 *
 * 显示权限列表数据，支持分页和操作
 */
const PermissionTable: React.FC<PermissionTableProps> = ({
  loading,
  data,
  currentPage,
  pageSize,
  total,
  onPageChange,
  onStatusChange,
  onEdit,
}) => {
  // 定义表格列
  const columns: ColumnsType<PermissionData> = [
    {
      title: '權限名稱',
      dataIndex: 'permissionName',
      key: 'permissionName',
      width: 150,
      render: (text: string, record) => (
        <div className="text-sm">
          <div className="font-medium text-gray-900 mb-1">{text}</div>
          {/* 在小屏幕上显示权限入口 */}
          <div className="text-xs text-gray-500 block">
            {record.permissionEntry}
          </div>
        </div>
      ),
    },
    {
      title: '權限入口',
      dataIndex: 'permissionEntry',
      key: 'permissionEntry',
      width: 120,
      className: 'md:table-cell',
      render: (text: string) => (
        <span className="text-gray-900 font-medium text-sm">{text}</span>
      ),
    },
    {
      title: '權限說明',
      dataIndex: 'permissionDescription',
      key: 'permissionDescription',
      width: 200,
      className: 'lg:table-cell',
      render: (text: string) => (
        <Tooltip title={text} placement="topLeft">
          <span className="text-gray-600 text-sm line-clamp-2">{text}</span>
        </Tooltip>
      ),
    },
    {
      title: '權限Key值',
      dataIndex: 'permissionKey',
      key: 'permissionKey',
      width: 180,
      className: 'lg:table-cell',
      render: (text: string) => (
        <code className="bg-gray-100 px-2 py-1 rounded text-xs text-gray-800">
          {text}
        </code>
      ),
    },
    {
      title: '關聯角色',
      dataIndex: 'relatedRoles',
      key: 'relatedRoles',
      width: 200,
      className: 'xl:table-cell',
      render: (roles: string[]) => (
        <div className="flex flex-wrap gap-1">
          {roles.map((role, index) => (
            <Tag key={index} color="blue" className="text-xs">
              {role}
            </Tag>
          ))}
        </div>
      ),
    },
    {
      title: '狀態',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      align: 'center',
      render: (status: 'ACTIVE' | 'INACTIVE') => <StatusTag status={status} />,
    },
    {
      title: '最後更新',
      key: 'lastUpdate',
      width: 140,
      className: 'md:table-cell',
      render: (_, record) => (
        <div className="text-sm">
          <div className="text-gray-900 font-medium text-xs">
            {record.updatedBy}
          </div>
          <div className="text-gray-500 text-xs">{record.updatedAt}</div>
        </div>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      fixed: 'right',
      render: (_, record) => (
        <ActionButtons
          record={record}
          onEdit={onEdit}
          onStatusChange={onStatusChange}
        />
      ),
    },
  ];

  return (
    <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
      <Table
        columns={columns}
        dataSource={data}
        rowKey="id"
        loading={loading}
        pagination={{
          current: currentPage,
          pageSize: pageSize,
          total: total,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 條，共 ${total} 條`,
          pageSizeOptions: ['10', '20', '50', '100'],
          onChange: onPageChange,
          onShowSizeChange: onPageChange,
          className: 'px-4 py-3',
        }}
        scroll={{ x: 800 }}
        size="middle"
        className="w-full"
        rowClassName="hover:bg-gray-50"
      />
    </div>
  );
};

export default PermissionTable;
