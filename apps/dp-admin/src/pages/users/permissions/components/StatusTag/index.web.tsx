import { Tag } from 'antd';
import React from 'react';

import type { StatusTagProps } from '../../types';

/**
 * 权限状态标签组件
 *
 * 根据权限状态显示不同颜色的标签
 */
const StatusTag: React.FC<StatusTagProps> = ({ status }) => {
  const getStatusConfig = (status: 'ACTIVE' | 'INACTIVE') => {
    switch (status) {
      case 'ACTIVE':
        return {
          color: 'success',
          text: '有效',
        };
      case 'INACTIVE':
        return {
          color: 'error',
          text: '無效',
        };
      default:
        return {
          color: 'default',
          text: '未知',
        };
    }
  };

  const config = getStatusConfig(status);

  return (
    <Tag color={config.color} className="border-0">
      {config.text}
    </Tag>
  );
};

export default StatusTag;
