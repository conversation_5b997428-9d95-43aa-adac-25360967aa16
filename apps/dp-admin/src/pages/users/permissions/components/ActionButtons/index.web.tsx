import { Button, Modal, Space } from 'antd';
import React from 'react';

import type { ActionButtonsProps } from '../../types';

/**
 * 权限操作按钮组件
 *
 * 提供编辑和状态切换功能
 */
const ActionButtons: React.FC<ActionButtonsProps> = ({
  record,
  onEdit,
  onStatusChange,
}) => {
  // 处理状态切换
  const handleStatusToggle = () => {
    const newStatus = record.status === 'ACTIVE' ? 'INACTIVE' : 'ACTIVE';
    const statusText = newStatus === 'ACTIVE' ? '啟用' : '停用';

    Modal.confirm({
      title: '確認操作',
      content: `確定要${statusText}權限「${record.permissionName}」嗎？`,
      okText: '確認',
      cancelText: '取消',
      onOk: () => {
        onStatusChange(record.id, newStatus);
      },
    });
  };

  return (
    <Space size="small">
      <Button
        type="link"
        size="small"
        onClick={() => onEdit(record)}
        className="p-0 h-auto text-blue-600 hover:text-blue-800"
      >
        修改
      </Button>
      <Button
        type="link"
        size="small"
        onClick={handleStatusToggle}
        className={`p-0 h-auto ${
          record.status === 'ACTIVE'
            ? 'text-red-600 hover:text-red-800'
            : 'text-green-600 hover:text-green-800'
        }`}
      >
        {record.status === 'ACTIVE' ? '停用' : '啟用'}
      </Button>
    </Space>
  );
};

export default ActionButtons;
