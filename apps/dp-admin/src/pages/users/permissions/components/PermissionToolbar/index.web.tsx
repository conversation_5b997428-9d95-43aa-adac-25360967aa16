import { Button, Col, Input, Row, Select } from 'antd';
import React from 'react';

import type { PermissionToolbarProps } from '../../types';

const { Option } = Select;

/**
 * 权限工具栏组件
 *
 * 提供搜索、筛选和新增功能
 */
const PermissionToolbar: React.FC<PermissionToolbarProps> = ({
  searchText,
  entryFilter,
  permissionStatus,
  entryOptions,
  onSearchTextChange,
  onEntryFilterChange,
  onPermissionStatusChange,
  onSearch,
  onReset,
  onAdd,
}) => {
  return (
    <div className="bg-white p-4 rounded-lg border border-gray-200 mb-4">
      <Row gutter={[16, 16]} align="middle">
        {/* 搜索框 */}
        <Col xs={24} sm={12} md={8} lg={6}>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              搜尋
            </label>
            <Input
              placeholder="請輸入權限名稱、權限Key或說明"
              value={searchText}
              onChange={(e) => onSearchTextChange(e.target.value)}
              allowClear
              className="w-full"
            />
          </div>
        </Col>

        {/* 权限入口筛选 */}
        <Col xs={24} sm={12} md={8} lg={6}>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              權限入口
            </label>
            <Select
              value={entryFilter}
              onChange={onEntryFilterChange}
              className="w-full"
              placeholder="請選擇權限入口"
            >
              <Option value="ALL">全部入口</Option>
              {entryOptions.map((entry) => (
                <Option key={entry.id} value={entry.name}>
                  {entry.name}
                </Option>
              ))}
            </Select>
          </div>
        </Col>

        {/* 权限状态筛选 */}
        <Col xs={24} sm={12} md={8} lg={6}>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              權限狀態
            </label>
            <Select
              value={permissionStatus}
              onChange={onPermissionStatusChange}
              className="w-full"
              placeholder="請選擇權限狀態"
            >
              <Option value="ALL">全部狀態</Option>
              <Option value="ACTIVE">有效</Option>
              <Option value="INACTIVE">無效</Option>
            </Select>
          </div>
        </Col>

        {/* 操作按钮 */}
        <Col xs={24} sm={12} md={8} lg={6}></Col>
      </Row>

      <Row className="mt-4">
        <div className="flex flex-row gap-2">
          <Button
            type="primary"
            onClick={onSearch}
            className="flex-1 sm:flex-none"
          >
            搜尋
          </Button>
          <Button onClick={onReset} className="flex-1 sm:flex-none">
            重置
          </Button>
          <Button
            type="primary"
            onClick={onAdd}
            className="bg-blue-600 hover:bg-blue-700 border-blue-600 hover:border-blue-700"
          >
            新增權限
          </Button>
        </div>
      </Row>
    </div>
  );
};

export default PermissionToolbar;
