import { Form, Input, Modal, Select } from 'antd';
import React, { useEffect } from 'react';

import {
  checkPermissionKeyUnique,
  checkPermissionNameUnique,
} from '../../services/api';
import type { PermissionFormProps } from '../../types';

const { Option } = Select;
const { TextArea } = Input;

/**
 * 权限表单组件
 *
 * 用于新增和编辑权限
 */
const PermissionForm: React.FC<PermissionFormProps> = ({
  visible,
  title,
  initialValues,
  entryOptions,
  canCreateEntry,
  onCancel,
  onSubmit,
  confirmLoading,
}) => {
  const [form] = Form.useForm();

  // 当表单显示时，设置初始值
  useEffect(() => {
    if (visible) {
      form.setFieldsValue(initialValues || {});
    }
  }, [visible, initialValues, form]);

  // 处理表单提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      onSubmit(values);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 处理取消
  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  // 自定义权限名称验证
  const validatePermissionName = async (_: unknown, value: string) => {
    if (!value) {
      return Promise.reject(new Error('請輸入權限名稱'));
    }
    if (value.length > 40) {
      return Promise.reject(new Error('權限名稱不能超過40個字符'));
    }

    // 检查唯一性
    const [success, isUnique] = await checkPermissionNameUnique(
      value,
      initialValues?.id
    );
    if (success && !isUnique) {
      return Promise.reject(new Error('權限名稱已存在，請使用其他名稱'));
    }

    return Promise.resolve();
  };

  // 自定义权限Key验证
  const validatePermissionKey = async (_: unknown, value: string) => {
    if (!value) {
      return Promise.reject(new Error('請輸入權限Key值'));
    }
    if (value.length > 200) {
      return Promise.reject(new Error('權限Key值不能超過200個字符'));
    }

    // 检查唯一性
    const [success, isUnique] = await checkPermissionKeyUnique(
      value,
      initialValues?.id
    );
    if (success && !isUnique) {
      return Promise.reject(new Error('權限Key值已存在，請使用其他Key值'));
    }

    return Promise.resolve();
  };

  return (
    <Modal
      title={title}
      open={visible}
      onOk={handleSubmit}
      onCancel={handleCancel}
      confirmLoading={confirmLoading}
      okText="確認"
      cancelText="取消"
      width={600}
      destroyOnHidden
      className="permission-form-modal"
    >
      <Form form={form} layout="vertical" className="mt-4" autoComplete="off">
        {/* 权限入口 */}
        <Form.Item
          label="權限入口"
          name="permissionEntry"
          rules={[{ required: true, message: '請選擇權限入口' }]}
        >
          <Select
            placeholder="請選擇權限入口"
            className="w-full"
            disabled={!canCreateEntry && entryOptions.length === 0}
          >
            {entryOptions.map((entry) => (
              <Option key={entry.id} value={entry.name}>
                {entry.name}
              </Option>
            ))}
            {canCreateEntry && (
              <Option value="__CREATE_NEW__" disabled>
                <span className="text-gray-400">
                  （如需創建新入口，請聯繫系統管理員）
                </span>
              </Option>
            )}
          </Select>
        </Form.Item>

        {/* 权限名称 */}
        <Form.Item
          label="權限名稱"
          name="permissionName"
          rules={[{ validator: validatePermissionName }]}
        >
          <Input
            placeholder="請輸入權限名稱（不超過40個字符）"
            maxLength={40}
            showCount
          />
        </Form.Item>

        {/* 权限说明 */}
        <Form.Item
          label="權限說明"
          name="permissionDescription"
          rules={[{ max: 200, message: '權限說明不能超過200個字符' }]}
        >
          <TextArea
            placeholder="請輸入權限說明（選填，不超過200個字符）"
            rows={3}
            maxLength={200}
            showCount
          />
        </Form.Item>

        {/* 权限Key值 */}
        <Form.Item
          label="權限Key值"
          name="permissionKey"
          rules={[{ validator: validatePermissionKey }]}
          extra="權限Key值用於程序中的權限判斷，建議使用英文和點號組合，如：user.manage"
        >
          <Input
            placeholder="請輸入權限Key值（不超過200個字符）"
            maxLength={200}
            showCount
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default PermissionForm;
