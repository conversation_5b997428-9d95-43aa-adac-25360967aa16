/**
 * 权限数据类型
 */
export interface PermissionData {
  id: string | number; // 权限ID
  permissionEntry: string; // 权限入口
  permissionName: string; // 权限名称
  permissionDescription?: string; // 权限说明
  permissionKey: string; // 权限Key值
  relatedRoles: string[]; // 关联角色
  status: 'ACTIVE' | 'INACTIVE'; // 权限状态：有效/无效
  updatedBy: string; // 更新人
  updatedAt: string; // 更新时间
}

/**
 * 权限入口选项类型
 */
export interface PermissionEntryOption {
  id: string | number;
  name: string;
  status: 'ACTIVE' | 'INACTIVE';
}

/**
 * 权限表格属性
 */
export interface PermissionTableProps {
  loading: boolean;
  data: PermissionData[];
  currentPage: number;
  pageSize: number;
  total: number;
  onPageChange: (page: number, pageSize?: number) => void;
  onStatusChange: (id: string | number, status: 'ACTIVE' | 'INACTIVE') => void;
  onEdit: (permission: PermissionData) => void;
}

/**
 * 权限工具栏属性
 */
export interface PermissionToolbarProps {
  searchText: string;
  entryFilter: string;
  permissionStatus: string;
  entryOptions: PermissionEntryOption[];
  onSearchTextChange: (value: string) => void;
  onEntryFilterChange: (value: string) => void;
  onPermissionStatusChange: (value: string) => void;
  onSearch: () => void;
  onReset: () => void;
  onAdd: () => void;
}

/**
 * 权限表单属性
 */
export interface PermissionFormProps {
  visible: boolean;
  title: string;
  initialValues?: Partial<PermissionData>;
  entryOptions: PermissionEntryOption[];
  canCreateEntry: boolean; // 是否有创建权限入口的权限
  onCancel: () => void;
  onSubmit: (values: Partial<PermissionData>) => void;
  confirmLoading: boolean;
}

/**
 * 状态标签属性
 */
export interface StatusTagProps {
  status: 'ACTIVE' | 'INACTIVE';
}

/**
 * 操作按钮属性
 */
export interface ActionButtonsProps {
  record: PermissionData;
  onEdit: (permission: PermissionData) => void;
  onStatusChange: (id: string | number, status: 'ACTIVE' | 'INACTIVE') => void;
}

/**
 * 权限状态变更日志
 */
export interface PermissionStatusLog {
  id: string | number;
  permissionId: string | number;
  oldStatus: 'ACTIVE' | 'INACTIVE';
  newStatus: 'ACTIVE' | 'INACTIVE';
  updatedBy: string;
  updatedAt: string;
}

/**
 * 权限信息变更日志
 */
export interface PermissionInfoLog {
  id: string | number;
  permissionId: string | number;
  field:
    | 'permissionEntry'
    | 'permissionName'
    | 'permissionDescription'
    | 'permissionKey';
  oldValue: string;
  newValue: string;
  updatedBy: string;
  updatedAt: string;
}

/**
 * 表单验证规则
 */
export interface FormValidationRules {
  permissionName: {
    required: boolean;
    max: number;
    message: string;
  };
  permissionDescription: {
    max: number;
    message: string;
  };
  permissionKey: {
    required: boolean;
    max: number;
    message: string;
  };
  permissionEntry: {
    required: boolean;
    message: string;
  };
}

/**
 * 权限创建表单数据
 */
export interface PermissionCreateFormData {
  permissionEntry: string;
  permissionName: string;
  permissionDescription?: string;
  permissionKey: string;
}

/**
 * 权限更新表单数据
 */
export interface PermissionUpdateFormData extends PermissionCreateFormData {
  id: string | number;
}

/**
 * 权限查询参数
 */
export interface PermissionQueryParams {
  searchText?: string;
  entryFilter?: string;
  permissionStatus?: string;
  pageNum?: number;
  pageSize?: number;
}

/**
 * 权限列表响应数据
 */
export interface PermissionListResponse {
  permissions: PermissionData[];
  total: number;
}

/**
 * 权限响应数据
 */
export interface PermissionResponse {
  id: string | number;
  [key: string]: unknown;
}

/**
 * 权限状态响应数据
 */
export interface PermissionStatusResponse {
  id: string | number;
  status: 'ACTIVE' | 'INACTIVE';
}

/**
 * 权限日志响应数据
 */
export interface PermissionLogResponse {
  id: string | number;
  permissionId: string | number;
  changeType: 'status' | 'info';
  oldValue: unknown;
  newValue: unknown;
  field?: string;
}
