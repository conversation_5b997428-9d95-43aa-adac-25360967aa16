import type {
  PermissionCreateFormData,
  PermissionEntryOption,
  PermissionListResponse,
  PermissionLogResponse,
  PermissionQueryParams,
  PermissionResponse,
  PermissionStatusResponse,
  PermissionUpdateFormData,
} from '../types';

/**
 * 获取权限列表
 * @param params 查询参数
 * @returns [success, data] 成功状态和数据
 */
export const getPermissionList = async (
  params: PermissionQueryParams
): Promise<[boolean, PermissionListResponse | null]> => {
  try {
    // 模拟API调用
    // 实际项目中应该调用真实的API
    // const data = await request.post('/admin-api/permissions/list', params);

    // 模拟数据
    const mockData: PermissionListResponse = {
      total: 15,
      permissions: [
        {
          id: '1',
          permissionEntry: '商戶管理',
          permissionName: '查看商戶列表',
          permissionDescription: '可以查看商戶入住申請列表',
          permissionKey: 'merchant.list.view',
          relatedRoles: ['超級管理員', '商務主管'],
          status: 'ACTIVE',
          updatedBy: '楊芝(Cha<PERSON>.<PERSON>)',
          updatedAt: '2025-3-20 14:09',
        },
        {
          id: '2',
          permissionEntry: '商戶管理',
          permissionName: '審核商戶申請',
          permissionDescription: '可以審核商戶入住申請，包括通過和拒絕',
          permissionKey: 'merchant.application.audit',
          relatedRoles: ['運營主管', '商務主管'],
          status: 'ACTIVE',
          updatedBy: '楊芝(Chara.Yang)',
          updatedAt: '2025-3-20 14:09',
        },
        {
          id: '3',
          permissionEntry: '用戶管理',
          permissionName: '管理用戶角色',
          permissionDescription: '可以創建、編輯和刪除用戶角色',
          permissionKey: 'user.role.manage',
          relatedRoles: ['超級管理員'],
          status: 'ACTIVE',
          updatedBy: '楊芝(Chara.Yang)',
          updatedAt: '2025-3-20 14:09',
        },
        {
          id: '4',
          permissionEntry: '用戶管理',
          permissionName: '查看用戶列表',
          permissionDescription: '可以查看系統用戶列表',
          permissionKey: 'user.list.view',
          relatedRoles: ['超級管理員', '運營主管'],
          status: 'INACTIVE',
          updatedBy: '楊芝(Chara.Yang)',
          updatedAt: '2025-3-20 14:09',
        },
        {
          id: '5',
          permissionEntry: '權限管理',
          permissionName: '管理系統權限',
          permissionDescription: '可以創建、編輯和刪除系統權限',
          permissionKey: 'permission.manage',
          relatedRoles: ['超級管理員'],
          status: 'ACTIVE',
          updatedBy: '楊芝(Chara.Yang)',
          updatedAt: '2025-3-20 14:09',
        },
      ],
    };

    // 模拟搜索和筛选
    let filteredPermissions = [...mockData.permissions];

    if (params.searchText) {
      filteredPermissions = filteredPermissions.filter(
        (permission) =>
          permission.permissionName
            .toLowerCase()
            .includes(params.searchText!.toLowerCase()) ||
          permission.permissionKey
            .toLowerCase()
            .includes(params.searchText!.toLowerCase()) ||
          permission.permissionDescription
            ?.toLowerCase()
            .includes(params.searchText!.toLowerCase())
      );
    }

    if (params.entryFilter && params.entryFilter !== 'ALL') {
      filteredPermissions = filteredPermissions.filter(
        (permission) => permission.permissionEntry === params.entryFilter
      );
    }

    if (params.permissionStatus && params.permissionStatus !== 'ALL') {
      filteredPermissions = filteredPermissions.filter(
        (permission) => permission.status === params.permissionStatus
      );
    }

    // 模拟分页
    const pageNum = params.pageNum || 1;
    const pageSize = params.pageSize || 10;
    const start = (pageNum - 1) * pageSize;
    const end = start + pageSize;
    const pagedPermissions = filteredPermissions.slice(start, end);

    return [
      true,
      {
        total: filteredPermissions.length,
        permissions: pagedPermissions,
      },
    ];
  } catch (error) {
    console.error('获取权限列表失败:', error);
    return [false, null];
  }
};

/**
 * 获取权限入口选项
 * @returns [success, data] 成功状态和数据
 */
export const getPermissionEntryOptions = async (): Promise<
  [boolean, PermissionEntryOption[] | null]
> => {
  try {
    // 模拟API调用
    // const data = await request.get('/admin-api/permissions/entries');

    const mockData: PermissionEntryOption[] = [
      { id: '1', name: '商戶管理', status: 'ACTIVE' },
      { id: '2', name: '用戶管理', status: 'ACTIVE' },
      { id: '3', name: '權限管理', status: 'ACTIVE' },
      { id: '4', name: '系統設置', status: 'ACTIVE' },
      { id: '5', name: '數據統計', status: 'ACTIVE' },
    ];

    return [true, mockData];
  } catch (error) {
    console.error('获取权限入口选项失败:', error);
    return [false, null];
  }
};

/**
 * 创建权限
 * @param permission 权限数据
 * @returns [success, data] 成功状态和数据
 */
export const createPermission = async (
  permission: PermissionCreateFormData
): Promise<[boolean, PermissionResponse | null]> => {
  try {
    // 模拟API调用
    // const data = await request.post('/admin-api/permissions/create', permission);

    // 模拟成功响应
    return [true, { id: Date.now(), success: true }];
  } catch (error) {
    console.error('创建权限失败:', error);
    return [false, null];
  }
};

/**
 * 更新权限
 * @param permission 权限数据
 * @returns [success, data] 成功状态和数据
 */
export const updatePermission = async (
  permission: PermissionUpdateFormData
): Promise<[boolean, PermissionResponse | null]> => {
  try {
    // 模拟API调用
    // const data = await request.post('/admin-api/permissions/update', permission);

    // 模拟成功响应
    return [true, { id: permission.id, success: true }];
  } catch (error) {
    console.error('更新权限失败:', error);
    return [false, null];
  }
};

/**
 * 更新权限状态
 * @param id 权限ID
 * @param status 新状态
 * @returns [success, data] 成功状态和数据
 */
export const updatePermissionStatus = async (
  id: string | number,
  status: 'ACTIVE' | 'INACTIVE'
): Promise<[boolean, PermissionStatusResponse | null]> => {
  try {
    // 模拟API调用
    // const data = await request.post('/admin-api/permissions/status', { id, status });

    // 模拟成功响应
    return [true, { id, status }];
  } catch (error) {
    console.error('更新权限状态失败:', error);
    return [false, null];
  }
};

/**
 * 记录权限变更日志
 * @param params 日志参数
 * @returns [success, data] 成功状态和数据
 */
export const logPermissionChange = async (params: {
  permissionId: string | number;
  changeType: 'status' | 'info';
  oldValue: unknown;
  newValue: unknown;
  field?: string;
}): Promise<[boolean, PermissionLogResponse | null]> => {
  try {
    // 模拟API调用
    // const data = await request.post('/admin-api/permissions/log', params);

    // 模拟成功响应
    return [
      true,
      {
        id: Date.now(),
        permissionId: params.permissionId,
        changeType: params.changeType,
        oldValue: params.oldValue,
        newValue: params.newValue,
        field: params.field,
      },
    ];
  } catch (error) {
    console.error('记录权限变更日志失败:', error);
    return [false, null];
  }
};

/**
 * 检查权限名称唯一性
 * @param name 权限名称
 * @param excludeId 排除的权限ID（编辑时使用）
 * @returns [success, isUnique] 成功状态和是否唯一
 */
export const checkPermissionNameUnique = async (
  name: string,
  excludeId?: string | number
): Promise<[boolean, boolean]> => {
  try {
    // 模拟API调用
    // const data = await request.post('/admin-api/permissions/check-name', { name, excludeId });

    // 模拟检查逻辑
    const existingNames = ['查看商戶列表', '審核商戶申請', '管理用戶角色'];
    const isUnique = !existingNames.includes(name) || excludeId !== undefined;

    return [true, isUnique];
  } catch (error) {
    console.error('检查权限名称唯一性失败:', error);
    return [false, false];
  }
};

/**
 * 检查权限Key唯一性
 * @param key 权限Key
 * @param excludeId 排除的权限ID（编辑时使用）
 * @returns [success, isUnique] 成功状态和是否唯一
 */
export const checkPermissionKeyUnique = async (
  key: string,
  excludeId?: string | number
): Promise<[boolean, boolean]> => {
  try {
    // 模拟API调用
    // const data = await request.post('/admin-api/permissions/check-key', { key, excludeId });

    // 模拟检查逻辑
    const existingKeys = [
      'merchant.list.view',
      'merchant.application.audit',
      'user.role.manage',
    ];
    const isUnique = !existingKeys.includes(key) || excludeId !== undefined;

    return [true, isUnique];
  } catch (error) {
    console.error('检查权限Key唯一性失败:', error);
    return [false, false];
  }
};
