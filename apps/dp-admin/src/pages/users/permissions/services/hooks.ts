import { message } from 'antd';
import { useCallback, useEffect, useState } from 'react';

import type { PermissionData, PermissionEntryOption } from '../types';
import {
  createPermission,
  getPermissionEntryOptions,
  getPermissionList,
  logPermissionChange,
  updatePermission,
  updatePermissionStatus,
} from './api';

/**
 * 权限管理相关的自定义 Hook
 *
 * 提供权限列表数据和操作方法
 */
export const usePermissionManagement = () => {
  // 权限列表数据
  const [permissions, setPermissions] = useState<PermissionData[]>([]);
  // 总数
  const [total, setTotal] = useState(0);
  // 加载状态
  const [loading, setLoading] = useState(false);
  // 当前页码
  const [currentPage, setCurrentPage] = useState(1);
  // 每页条数
  const [pageSize, setPageSize] = useState(10);
  // 搜索文本
  const [searchText, setSearchText] = useState('');
  // 权限入口筛选
  const [entryFilter, setEntryFilter] = useState('ALL');
  // 权限状态筛选
  const [permissionStatus, setPermissionStatus] = useState('ALL');
  // 权限入口选项
  const [entryOptions, setEntryOptions] = useState<PermissionEntryOption[]>([]);
  // 权限表单可见性
  const [formVisible, setFormVisible] = useState(false);
  // 表单标题
  const [formTitle, setFormTitle] = useState('新增權限');
  // 表单初始值
  const [initialValues, setInitialValues] = useState<Partial<PermissionData>>(
    {}
  );
  // 表单提交加载状态
  const [confirmLoading, setConfirmLoading] = useState(false);

  // 获取权限列表数据
  const fetchPermissionList = useCallback(async () => {
    setLoading(true);
    try {
      const params = {
        searchText,
        entryFilter,
        permissionStatus,
        pageNum: currentPage,
        pageSize,
      };
      const [success, res] = await getPermissionList(params);
      if (success && res) {
        setPermissions(res.permissions || []);
        setTotal(res.total || 0);
      } else {
        message.error('獲取權限列表失敗');
      }
    } catch (error) {
      console.error('获取权限列表错误:', error);
      message.error('獲取權限列表出錯');
    } finally {
      setLoading(false);
    }
  }, [searchText, entryFilter, permissionStatus, currentPage, pageSize]);

  // 获取权限入口选项
  const fetchEntryOptions = useCallback(async () => {
    try {
      const [success, res] = await getPermissionEntryOptions();
      if (success && res) {
        setEntryOptions(res);
      }
    } catch (error) {
      console.error('获取权限入口选项错误:', error);
    }
  }, []);

  // 首次加载时获取数据
  useEffect(() => {
    fetchPermissionList();
  }, [fetchPermissionList]);

  // 首次加载时获取权限入口选项
  useEffect(() => {
    fetchEntryOptions();
  }, [fetchEntryOptions]);

  // 处理搜索
  const handleSearch = useCallback(() => {
    setCurrentPage(1);
    fetchPermissionList();
  }, [fetchPermissionList]);

  // 处理重置
  const handleReset = useCallback(() => {
    setSearchText('');
    setEntryFilter('ALL');
    setPermissionStatus('ALL');
    setCurrentPage(1);
  }, []);

  // 处理分页变化
  const handlePageChange = useCallback((page: number, size?: number) => {
    setCurrentPage(page);
    if (size) {
      setPageSize(size);
    }
  }, []);

  // 处理搜索文本变化
  const handleSearchTextChange = useCallback((value: string) => {
    setSearchText(value);
  }, []);

  // 处理权限入口筛选变化
  const handleEntryFilterChange = useCallback((value: string) => {
    setEntryFilter(value);
  }, []);

  // 处理权限状态筛选变化
  const handlePermissionStatusChange = useCallback((value: string) => {
    setPermissionStatus(value);
  }, []);

  // 处理权限状态变更
  const handleStatusChange = useCallback(
    async (id: string | number, status: 'ACTIVE' | 'INACTIVE') => {
      try {
        const [success] = await updatePermissionStatus(id, status);
        if (success) {
          message.success('權限狀態更新成功');

          // 记录变更日志
          await logPermissionChange({
            permissionId: id,
            changeType: 'status',
            oldValue: status === 'ACTIVE' ? 'INACTIVE' : 'ACTIVE',
            newValue: status,
          });

          fetchPermissionList();
        } else {
          message.error('權限狀態更新失敗');
        }
      } catch (error) {
        console.error('更新权限状态错误:', error);
        message.error('更新權限狀態出錯');
      }
    },
    [fetchPermissionList]
  );

  // 处理编辑权限
  const handleEdit = useCallback((permission: PermissionData) => {
    setFormTitle('編輯權限');
    setInitialValues(permission);
    setFormVisible(true);
  }, []);

  // 处理新增权限
  const handleAdd = useCallback(() => {
    setFormTitle('新增權限');
    setInitialValues({});
    setFormVisible(true);
  }, []);

  // 处理表单取消
  const handleFormCancel = useCallback(() => {
    setFormVisible(false);
  }, []);

  // 处理表单提交
  const handleFormSubmit = useCallback(
    async (values: Partial<PermissionData>) => {
      setConfirmLoading(true);
      try {
        let success = false;

        if (values.id) {
          // 编辑权限
          const [updateSuccess] = await updatePermission({
            id: values.id,
            permissionEntry: values.permissionEntry!,
            permissionName: values.permissionName!,
            permissionDescription: values.permissionDescription,
            permissionKey: values.permissionKey!,
          });
          success = updateSuccess;

          if (success) {
            // 记录变更日志
            await logPermissionChange({
              permissionId: values.id,
              changeType: 'info',
              oldValue: initialValues,
              newValue: values,
            });
          }
        } else {
          // 新增权限
          const [createSuccess] = await createPermission({
            permissionEntry: values.permissionEntry!,
            permissionName: values.permissionName!,
            permissionDescription: values.permissionDescription,
            permissionKey: values.permissionKey!,
          });
          success = createSuccess;
        }

        if (success) {
          message.success(values.id ? '權限更新成功' : '權限創建成功');
          setFormVisible(false);
          fetchPermissionList();
        } else {
          message.error(values.id ? '權限更新失敗' : '權限創建失敗');
        }
      } catch (error) {
        console.error('保存权限错误:', error);
        message.error('保存權限出錯');
      } finally {
        setConfirmLoading(false);
      }
    },
    [fetchPermissionList, initialValues]
  );

  return {
    // 数据状态
    loading,
    permissions,
    total,
    currentPage,
    pageSize,
    searchText,
    entryFilter,
    permissionStatus,
    entryOptions,
    formVisible,
    formTitle,
    initialValues,
    confirmLoading,

    // 操作方法
    handleSearch,
    handleReset,
    handlePageChange,
    handleSearchTextChange,
    handleEntryFilterChange,
    handlePermissionStatusChange,
    handleStatusChange,
    handleEdit,
    handleAdd,
    handleFormCancel,
    handleFormSubmit,
  };
};
