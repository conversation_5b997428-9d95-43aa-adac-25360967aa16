import React from 'react';

import PermissionForm from './components/PermissionForm/index.web';
import PermissionTable from './components/PermissionTable/index.web';
import PermissionToolbar from './components/PermissionToolbar/index.web';
import { usePermissionManagement } from './services/hooks';

/**
 * 权限管理页面
 *
 * 用于管理系统权限，包括查看、新增、编辑权限，以及设置权限状态
 */
const PermissionManagement: React.FC = () => {
  // 使用自定义 hooks 获取数据和状态
  const {
    loading,
    permissions,
    total,
    currentPage,
    pageSize,
    searchText,
    entryFilter,
    permissionStatus,
    entryOptions,
    formVisible,
    formTitle,
    initialValues,
    confirmLoading,
    handleSearch,
    handleReset,
    handlePageChange,
    handleSearchTextChange,
    handleEntryFilterChange,
    handlePermissionStatusChange,
    handleStatusChange,
    handleEdit,
    handleAdd,
    handleFormCancel,
    handleFormSubmit,
  } = usePermissionManagement();

  // 模拟权限检查 - 实际项目中应该从认证上下文或权限系统获取
  const canCreateEntry = true; // 是否有创建权限入口的权限
  const canManagePermissions = true; // 是否有管理权限的权限
  const canViewPermissions = true; // 是否有查看权限的权限

  // 如果没有查看权限，显示无权限提示
  if (!canViewPermissions) {
    return (
      <div className="bg-white p-8 rounded-lg shadow-sm text-center">
        <div className="text-gray-500 text-lg">您沒有查看權限列表的權限</div>
      </div>
    );
  }

  return (
    <div className="bg-white p-4 sm:p-6 md:p-8 rounded-lg shadow-sm">
      <div className="mb-6">
        <h1 className="text-xl sm:text-2xl font-medium mb-6">權限管理</h1>

        {/* 工具栏：搜索、筛选和操作按钮 */}
        {canManagePermissions && (
          <div className="mb-6">
            <PermissionToolbar
              searchText={searchText}
              entryFilter={entryFilter}
              permissionStatus={permissionStatus}
              entryOptions={entryOptions}
              onSearchTextChange={handleSearchTextChange}
              onEntryFilterChange={handleEntryFilterChange}
              onPermissionStatusChange={handlePermissionStatusChange}
              onSearch={handleSearch}
              onReset={handleReset}
              onAdd={handleAdd}
            />
          </div>
        )}

        {/* 权限列表表格 */}
        <PermissionTable
          loading={loading}
          data={permissions}
          currentPage={currentPage}
          pageSize={pageSize}
          total={total}
          onPageChange={handlePageChange}
          onStatusChange={canManagePermissions ? handleStatusChange : () => {}}
          onEdit={canManagePermissions ? handleEdit : () => {}}
        />
      </div>

      {/* 权限表单弹窗 */}
      {canManagePermissions && (
        <PermissionForm
          visible={formVisible}
          title={formTitle}
          initialValues={initialValues}
          entryOptions={entryOptions}
          canCreateEntry={canCreateEntry}
          onCancel={handleFormCancel}
          onSubmit={handleFormSubmit}
          confirmLoading={confirmLoading}
        />
      )}
    </div>
  );
};

export default PermissionManagement;
