import React from 'react';

import { useAuth } from '../../contexts/AuthContext';
import DecorativeBackground from './components/DecorativeBackground/index.web';
import LoginForm from './components/LoginForm/index.web';
import { useLogin } from './services';

/**
 * 登录页面组件
 *
 * 功能特性：
 * - 用户登录认证
 * - 记住密码功能
 * - 响应式设计
 * - 装饰性背景
 * - 登录状态管理
 */
const Login: React.FC = () => {
  const { login, loading, error } = useLogin();
  const { getSavedCredentials } = useAuth();

  /**
   * 处理登录表单提交
   * @param username - 用户名
   * @param password - 密码
   * @param remember - 是否记住密码
   */
  const handleLogin = async (
    username: string,
    password: string,
    remember?: boolean
  ): Promise<void> => {
    await login(username, password, remember);
  };

  return (
    <div className="min-w-full min-h-screen flex flex-col items-center justify-center p-3 sm:p-5 relative">
      {/* 装饰性背景元素 */}
      <DecorativeBackground />

      {/* 登录卡片容器 */}
      <div
        className="w-full max-w-[90%] sm:max-w-[450px] md:max-w-[500px] lg:max-w-[600px] bg-white/90 backdrop-blur-sm rounded-xl shadow-2xl relative z-10 overflow-hidden p-4 sm:p-6 md:p-8 lg:p-10 animate-fadeIn"
        style={{
          animationDuration: '0.8s',
          animationFillMode: 'both',
          animationTimingFunction: 'ease-out',
        }}
      >
        {/* 顶部装饰条 */}
        <div className="absolute top-0 left-0 right-0 h-1 sm:h-1.5 md:h-2 bg-gradient-to-r from-blue-500 to-indigo-600" />

        {/* 系统标题 */}
        <h1 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold text-center bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-indigo-700 mb-4 sm:mb-6 lg:mb-8">
          運營管理系統
        </h1>

        {/* 登录表单区域 */}
        <div className="w-full sm:w-4/5 md:w-full mx-auto">
          <LoginForm
            onLogin={handleLogin}
            isLoading={loading}
            error={error}
            savedCredentials={getSavedCredentials()}
          />
        </div>

        {/* 版权信息 */}
        <div className="mt-6 text-center text-xs sm:text-sm font-light bg-gradient-to-r from-blue-500 to-indigo-600 bg-clip-text text-transparent">
          © 2025 運營管理系統版權所有
        </div>
      </div>
    </div>
  );
};

export default Login;
