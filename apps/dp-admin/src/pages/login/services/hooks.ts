import { useCallback, useState } from 'react';
import { useNavigate } from 'react-router-dom';

import { useAuth } from '../../../contexts/AuthContext';
// import { hashPassword } from '../../../utils/crypto';
import { login as loginApi } from './api';

interface LoginResult {
  success: boolean;
  message?: string;
}

/**
 * 登录相关的自定义 Hook
 *
 * 提供登录功能和状态管理
 */
export const useLogin = () => {
  const navigate = useNavigate();
  const { saveAuthData } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * 执行登录操作
   *
   * @param username 用户名
   * @param password 密码
   * @param remember 是否记住密码
   * @returns 登录结果
   */
  const login = useCallback(
    async (
      username: string,
      password: string,
      remember = false
    ): Promise<LoginResult> => {
      setLoading(true);
      setError(null);

      try {
        // 对密码进行哈希处理，增加安全性
        // const hashedPassword = hashPassword(password);

        // 调用登录 API
        const [success, res] = await loginApi(username, password);
        console.log('登录结果:', res);

        if (success) {
          // 保存认证数据
          saveAuthData(username, password, remember);

          // 登录成功后导航到首页
          navigate('/');

          return { success: true };
        } else {
          const errorMessage = '登录失败，请检查用户名和密码';
          setError(errorMessage);
          return { success: false, message: errorMessage };
        }
      } catch (error) {
        console.error('登录失败:', error);
        const errorMessage =
          error instanceof Error ? error.message : '登录过程中发生错误';

        setError(errorMessage);
        return { success: false, message: errorMessage };
      } finally {
        setLoading(false);
      }
    },
    [navigate, saveAuthData]
  );

  return {
    login,
    loading,
    error,
  };
};
