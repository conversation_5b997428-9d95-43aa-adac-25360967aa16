import React from 'react';

interface DecorativeBackgroundProps {
  className?: string;
}

/**
 * 装饰性背景组件，用于登录页面等需要美观背景的场景
 */
const DecorativeBackground: React.FC<DecorativeBackgroundProps> = ({
  className = '',
}) => {
  return (
    <div
      className={`fixed inset-0 w-full h-full overflow-hidden pointer-events-none -z-10 ${className}`}
    >
      {/* 背景渐变 */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50"></div>

      {/* 左上角大圆形 - 添加动画 */}
      <div
        className="absolute -top-20 -left-20 w-64 h-64 sm:w-80 sm:h-80 md:w-96 md:h-96 lg:w-[30rem] lg:h-[30rem] bg-gradient-to-br from-blue-200 to-blue-300 rounded-full opacity-20 sm:opacity-25 md:opacity-30 blur-md animate-pulse"
        style={{ animationDuration: '8s' }}
      ></div>

      {/* 右下角大圆形 - 添加动画 */}
      <div
        className="absolute -bottom-20 -right-20 w-64 h-64 sm:w-80 sm:h-80 md:w-96 md:h-96 lg:w-[30rem] lg:h-[30rem] bg-gradient-to-tl from-indigo-200 to-purple-300 rounded-full opacity-20 sm:opacity-25 md:opacity-30 blur-md animate-pulse"
        style={{ animationDuration: '10s' }}
      ></div>

      {/* 右上角中圆形 - 添加动画 */}
      <div
        className="absolute top-[5%] right-[5%] w-32 h-32 sm:w-40 sm:h-40 md:w-48 md:h-48 lg:w-64 lg:h-64 bg-gradient-to-bl from-purple-200 to-pink-200 rounded-full opacity-15 sm:opacity-20 md:opacity-25 blur-sm animate-pulse"
        style={{ animationDuration: '7s' }}
      ></div>

      {/* 左下角中圆形 - 添加动画 */}
      <div
        className="absolute bottom-[5%] left-[5%] w-32 h-32 sm:w-40 sm:h-40 md:w-48 md:h-48 lg:w-64 lg:h-64 bg-gradient-to-tr from-blue-300 to-cyan-200 rounded-full opacity-15 sm:opacity-20 md:opacity-25 blur-sm animate-pulse"
        style={{ animationDuration: '9s' }}
      ></div>

      {/* 中心小圆形 - 装饰元素 */}
      <div
        className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-16 h-16 sm:w-20 sm:h-20 md:w-24 md:h-24 lg:w-32 lg:h-32 bg-gradient-to-r from-blue-400 to-indigo-400 rounded-full opacity-10 sm:opacity-15 md:opacity-20 blur-sm animate-ping"
        style={{ animationDuration: '5s', animationIterationCount: 'infinite' }}
      ></div>

      {/* 小装饰圆点 - 添加浮动动画 */}
      <div
        className="absolute top-[20%] left-[30%] w-4 h-4 sm:w-6 sm:h-6 md:w-8 md:h-8 bg-blue-400 rounded-full opacity-20 blur-[1px] animate-bounce"
        style={{ animationDuration: '6s' }}
      ></div>
      <div
        className="absolute top-[70%] left-[20%] w-3 h-3 sm:w-4 sm:h-4 md:w-6 md:h-6 bg-indigo-400 rounded-full opacity-20 blur-[1px] animate-bounce"
        style={{ animationDuration: '8s' }}
      ></div>
      <div
        className="absolute top-[25%] right-[25%] w-3 h-3 sm:w-4 sm:h-4 md:w-6 md:h-6 bg-purple-400 rounded-full opacity-20 blur-[1px] animate-bounce"
        style={{ animationDuration: '7s' }}
      ></div>
      <div
        className="absolute bottom-[30%] right-[35%] w-4 h-4 sm:w-6 sm:h-6 md:w-8 md:h-8 bg-cyan-400 rounded-full opacity-20 blur-[1px] animate-bounce"
        style={{ animationDuration: '9s' }}
      ></div>
    </div>
  );
};

export default DecorativeBackground;
