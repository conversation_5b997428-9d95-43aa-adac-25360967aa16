import { LockOutlined, UserOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Button, Checkbox, Form, Input } from 'antd';
import React from 'react';

interface LoginFormProps {
  onLogin: (username: string, password: string, remember?: boolean) => void;
  isLoading?: boolean;
  error?: string | null;
  savedCredentials?: {
    username: string;
    password?: string;
    remember: boolean;
  } | null;
}

const LoginForm: React.FC<LoginFormProps> = ({
  onLogin,
  isLoading = false,
  error = null,
  savedCredentials = null,
}) => {
  const [form] = Form.useForm();

  // 使用保存的凭据自动填充表单
  React.useEffect(() => {
    if (savedCredentials) {
      form.setFieldsValue(savedCredentials);
    }
  }, [form, savedCredentials]);

  const onFinish = (values: {
    username: string;
    password: string;
    remember: boolean;
  }) => {
    onLogin(values.username, values.password, values.remember);
  };

  return (
    <div className="w-full">
      <Form
        form={form}
        name="login"
        onFinish={onFinish}
        autoComplete="on"
        layout="vertical"
        size="large"
        className="w-full"
      >
        <Form.Item
          name="username"
          label={
            <span className="text-xs sm:text-sm md:text-base font-medium">
              用戶名
            </span>
          }
          rules={[{ required: true, message: '請輸入電子信箱/手機號' }]}
          className="mb-3 sm:mb-4 md:mb-5"
        >
          <Input
            prefix={<UserOutlined className="text-blue-400" />}
            placeholder="請輸入用戶名"
            autoComplete="username"
            className="h-9 sm:h-10 md:h-11 text-sm sm:text-base rounded-lg border-gray-200 hover:border-blue-400 focus:border-blue-500 transition-colors duration-300"
          />
        </Form.Item>

        <Form.Item
          name="password"
          label={
            <span className="text-xs sm:text-sm md:text-base font-medium">
              密碼
            </span>
          }
          rules={[{ required: true, message: '請輸入密碼' }]}
          className="mb-2 sm:mb-3 md:mb-4"
        >
          <Input.Password
            prefix={<LockOutlined className="text-blue-400" />}
            placeholder="請輸入密碼"
            autoComplete="current-password"
            className="h-9 sm:h-10 md:h-11 text-sm sm:text-base rounded-lg border-gray-200 hover:border-blue-400 focus:border-blue-500 transition-colors duration-300"
          />
        </Form.Item>

        <Form.Item
          name="remember"
          valuePropName="checked"
          className="mb-4 sm:mb-5 md:mb-6"
        >
          <Checkbox className="text-xs sm:text-sm md:text-base text-gray-600 hover:text-blue-600 transition-colors duration-300">
            記住密碼
          </Checkbox>
        </Form.Item>

        {error && (
          <Form.Item className="mb-4">
            <Alert
              message={error}
              type="error"
              showIcon
              className="text-xs sm:text-sm"
            />
          </Form.Item>
        )}

        <Form.Item className="mb-4 sm:mb-6">
          <Button
            type="primary"
            htmlType="submit"
            loading={isLoading}
            className="w-full h-10 sm:h-11 md:h-12 text-sm sm:text-base bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 shadow-md hover:shadow-lg rounded-lg"
            size="large"
          >
            登錄
          </Button>
        </Form.Item>
      </Form>

      <Alert
        message="請使用您的企業賬號登錄系統。如有問題，請聯繫管理員。"
        type="info"
        showIcon
        className="mt-3 sm:mt-4 md:mt-6 text-xs sm:text-sm bg-blue-50/70 border-blue-200 rounded-lg shadow-sm"
      />
    </div>
  );
};

export default LoginForm;
