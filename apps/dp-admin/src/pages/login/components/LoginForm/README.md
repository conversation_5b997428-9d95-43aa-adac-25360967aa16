# 登录表单组件 (LoginForm)

## 组件概述

`LoginForm` 是一个功能性组件，用于处理用户登录操作。该组件提供用户名和密码输入、表单验证、记住密码功能以及登录状态反馈，是登录页面的核心交互组件。

## 组件接口

```typescript
interface LoginFormProps {
  onLogin: (username: string, password: string, remember?: boolean) => void;  // 登录回调函数
  isLoading?: boolean;  // 登录加载状态
}
```

## 功能特性

1. **表单输入与验证**：
   - 用户名输入框，带有必填验证
   - 密码输入框，带有必填验证
   - 表单提交前验证

2. **记住密码功能**：
   - 记住密码选项
   - 自动保存和填充用户名和密码
   - 使用 localStorage 存储加密的用户信息

3. **登录状态反馈**：
   - 加载状态显示
   - 错误信息展示
   - 按钮状态变化

4. **用户体验优化**：
   - 输入框前缀图标
   - 输入框悬停和聚焦效果
   - 渐变色按钮，带有悬停效果

5. **响应式设计**：
   - 适配不同屏幕尺寸
   - 元素大小和间距自动调整

## 使用示例

```jsx
import LoginForm from '../components/LoginForm';

const LoginPage = () => {
  const [isLoading, setIsLoading] = useState(false);

  const handleLogin = async (username, password, remember) => {
    setIsLoading(true);
    try {
      // 登录逻辑
      await loginApi(username, password);
      // 处理记住密码
      if (remember) {
        localStorage.setItem('auth_username', username);
        // 加密存储密码
        localStorage.setItem('auth_password', encrypt(password));
      }
      // 导航到首页
      navigate('/');
    } catch (error) {
      // 处理错误
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div>
      <LoginForm onLogin={handleLogin} isLoading={isLoading} />
    </div>
  );
};
```

## 安全考虑

- 密码输入框使用 `type="password"` 确保密码不可见
- 使用加密方式存储密码，增加安全性
- 在实际应用中，应使用更安全的方式管理用户凭据

## 可访问性

- 表单元素带有标签，便于屏幕阅读器识别
- 输入框带有占位符文本，提供额外指引
- 错误信息清晰展示，便于用户理解和修正
- 按钮状态变化提供视觉反馈
