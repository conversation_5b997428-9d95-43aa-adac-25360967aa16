import { Button } from 'antd';
import clsx from 'clsx';
import React from 'react';
import { useNavigate } from 'react-router-dom';

/**
 * 404 页面组件
 *
 * 使用 Tailwind CSS 样式的响应式美观 404 页面
 */
const NotFoundPage: React.FC = () => {
  const navigate = useNavigate();

  // 返回首页
  const handleBackHome = () => {
    navigate('/');
  };

  // 返回上一页
  const handleGoBack = () => {
    navigate(-1);
  };

  return (
    <div
      className={clsx(
        'relative w-full min-h-screen overflow-hidden',
        'flex-cc',
        'bg-gradient-to-b from-blue-900 to-indigo-900',
        'px-4 sm:px-6 md:px-8'
      )}
    >
      {/* 背景装饰元素 */}
      <div className="absolute top-0 left-0 w-full h-full overflow-hidden z-0">
        <div className="absolute top-10 left-10 w-20 h-20 md:w-32 md:h-32 rounded-full bg-blue-400 opacity-10"></div>
        <div className="absolute bottom-10 right-10 w-40 h-40 md:w-64 md:h-64 rounded-full bg-indigo-500 opacity-10"></div>
        <div className="absolute top-1/4 right-1/4 w-24 h-24 rounded-full bg-purple-400 opacity-10"></div>
        <div className="absolute bottom-1/3 left-1/3 w-32 h-32 rounded-full bg-blue-300 opacity-10"></div>
      </div>

      {/* 主要内容 */}
      <div
        className={clsx(
          'relative z-10 w-full',
          'max-w-md sm:max-w-lg md:max-w-xl lg:max-w-2xl',
          'bg-white/5 backdrop-blur-sm rounded-2xl',
          'p-8 md:p-12 shadow-2xl border border-white/10'
        )}
      >
        <div className="text-center">
          <div className={clsx('flex items-center justify-center mb-6')}>
            <svg
              className="w-24 h-24 md:w-32 md:h-32 text-red-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
              ></path>
            </svg>
          </div>

          <h1 className="text-6xl sm:text-7xl md:text-8xl lg:text-9xl font-bold text-white mb-4 md:mb-6">
            404
          </h1>
          <p className="text-xl sm:text-2xl md:text-3xl text-gray-200 mb-4">
            抱歉，您访问的页面不存在
          </p>
          <p className="text-gray-300 mb-8 md:mb-10">
            您可能输入了错误的地址，或者该页面已被移动或删除
          </p>

          <div
            className={clsx(
              'flex-cc',
              'sm:flex-row sm:items-center sm:justify-start',
              'gap-4 md:gap-6'
            )}
          >
            <Button
              type="primary"
              onClick={handleBackHome}
              className="h-10 sm:h-11 md:h-12 lg:h-14 px-6 sm:px-7 md:px-8 lg:px-10 text-base sm:text-lg rounded-full bg-blue-500 border-blue-500 hover:bg-blue-600 hover:border-blue-600 shadow-lg"
            >
              返回首页
            </Button>
            <Button
              onClick={handleGoBack}
              className="h-10 sm:h-11 md:h-12 lg:h-14 px-6 sm:px-7 md:px-8 lg:px-10 text-base sm:text-lg rounded-full border-gray-300 text-white hover:text-white hover:border-gray-400"
            >
              返回上一页
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotFoundPage;
