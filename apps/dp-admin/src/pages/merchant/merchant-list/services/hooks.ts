/**
 * 商户列表管理 Hook
 */

import { message } from 'antd';
import { useCallback, useEffect, useState } from 'react';

import type {
  BDOption,
  MerchantData,
  MerchantStatus,
  OptionData,
  SearchFormData,
} from '../types';
import {
  batchAssignMerchants,
  getBDOptions,
  getBrandOptions,
  getCategoryOptions,
  getCityOptions,
  getFranchiseTypeOptions,
  getLevelOptions,
  getMerchantList,
  getMerchantStatusOptions,
  updateMerchantStatus,
} from './api';

/**
 * 商户列表管理 Hook
 *
 * 提供商户列表页面所需的所有状态管理和业务逻辑
 * 包括数据获取、表单处理、分页、批量操作等功能
 */
export const useMerchantList = () => {
  // 基础状态
  const [loading, setLoading] = useState<boolean>(false);
  const [data, setData] = useState<MerchantData[]>([]);
  const [total, setTotal] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(20);

  // 选项数据
  const [bdOptions, setBdOptions] = useState<BDOption[]>([]);
  const [merchantStatusOptions, setMerchantStatusOptions] = useState<
    OptionData[]
  >([]);
  const [categoryOptions, setCategoryOptions] = useState<OptionData[]>([]);
  const [cityOptions, setCityOptions] = useState<OptionData[]>([]);
  const [brandOptions, setBrandOptions] = useState<OptionData[]>([]);
  const [levelOptions, setLevelOptions] = useState<OptionData[]>([]);
  const [franchiseTypeOptions, setFranchiseTypeOptions] = useState<
    OptionData[]
  >([]);

  // 搜索表单数据
  const [formData, setFormData] = useState<SearchFormData>({
    keyword: '',
    bd: undefined,
    merchantStatus: [],
    category: [],
    city: [],
    brand: [],
    level: [],
    franchiseType: [],
    updateTime: null,
    joinTime: null,
    location: '',
    radius: undefined,
  });

  // 选中的行
  const [selectedRowKeys, setSelectedRowKeys] = useState<(string | number)[]>(
    []
  );
  const [selectedRows, setSelectedRows] = useState<MerchantData[]>([]);

  // 模态框状态
  const [batchAssignVisible, setBatchAssignVisible] = useState<boolean>(false);
  const [batchLoading, setBatchLoading] = useState<boolean>(false);

  /**
   * 获取商户列表
   */
  const fetchMerchantList = useCallback(async (): Promise<void> => {
    setLoading(true);
    try {
      const [success, result] = await getMerchantList({
        ...formData,
        pageNum: currentPage,
        pageSize: pageSize,
      });

      if (success && result && typeof result === 'object' && 'list' in result) {
        setData(result.list);
        setTotal(result.total);
      } else {
        message.error('获取商户列表失败');
        setData([]);
        setTotal(0);
      }
    } catch (error) {
      console.error('获取商户列表出错:', error);
      message.error('获取商户列表出错');
    } finally {
      setLoading(false);
    }
  }, [formData, currentPage, pageSize]);

  /**
   * 获取选项数据
   */
  const fetchOptions = useCallback(async (): Promise<void> => {
    try {
      const [
        [bdSuccess, bdResult],
        [statusSuccess, statusResult],
        [categorySuccess, categoryResult],
        [citySuccess, cityResult],
        [brandSuccess, brandResult],
        [levelSuccess, levelResult],
        [franchiseSuccess, franchiseResult],
      ] = await Promise.all([
        getBDOptions(),
        getMerchantStatusOptions(),
        getCategoryOptions(),
        getCityOptions(),
        getBrandOptions(),
        getLevelOptions(),
        getFranchiseTypeOptions(),
      ]);

      if (bdSuccess && Array.isArray(bdResult)) {
        setBdOptions(bdResult);
      }
      if (statusSuccess && Array.isArray(statusResult)) {
        setMerchantStatusOptions(statusResult);
      }
      if (categorySuccess && Array.isArray(categoryResult)) {
        setCategoryOptions(categoryResult);
      }
      if (citySuccess && Array.isArray(cityResult)) {
        setCityOptions(cityResult);
      }
      if (brandSuccess && Array.isArray(brandResult)) {
        setBrandOptions(brandResult);
      }
      if (levelSuccess && Array.isArray(levelResult)) {
        setLevelOptions(levelResult);
      }
      if (franchiseSuccess && Array.isArray(franchiseResult)) {
        setFranchiseTypeOptions(franchiseResult);
      }
    } catch (error) {
      console.error('获取选项数据出错:', error);
    }
  }, []);

  /**
   * 初始化数据
   */
  useEffect(() => {
    fetchOptions();
  }, [fetchOptions]);

  /**
   * 获取商户列表数据
   */
  useEffect(() => {
    fetchMerchantList();
  }, [fetchMerchantList]);

  /**
   * 处理表单数据变更
   */
  const handleFormDataChange = useCallback(
    (data: Partial<SearchFormData>): void => {
      setFormData((prev) => ({ ...prev, ...data }));
    },
    []
  );

  /**
   * 处理搜索
   */
  const handleSearch = useCallback((): void => {
    setCurrentPage(1);
    fetchMerchantList();
  }, [fetchMerchantList]);

  /**
   * 处理重置
   */
  const handleReset = useCallback((): void => {
    setFormData({
      keyword: '',
      bd: undefined,
      merchantStatus: [],
      category: [],
      city: [],
      brand: [],
      level: [],
      franchiseType: [],
      updateTime: null,
      joinTime: null,
      location: '',
      radius: undefined,
    });
    setCurrentPage(1);
  }, []);

  /**
   * 处理分页变更
   */
  const handlePageChange = useCallback(
    (page: number, size?: number): void => {
      setCurrentPage(page);
      if (size && size !== pageSize) {
        setPageSize(size);
      }
    },
    [pageSize]
  );

  /**
   * 处理行选择变更
   */
  const handleRowSelectionChange = useCallback(
    (
      selectedRowKeys: (string | number)[],
      selectedRows: MerchantData[]
    ): void => {
      setSelectedRowKeys(selectedRowKeys);
      setSelectedRows(selectedRows);
    },
    []
  );

  /**
   * 处理查看详情
   */
  const handleViewDetail = useCallback((id: string | number): void => {
    console.log('查看详情:', id);
    // TODO: 实现查看详情逻辑
  }, []);

  /**
   * 处理编辑
   */
  const handleEdit = useCallback((id: string | number): void => {
    console.log('编辑商户:', id);
    // TODO: 实现编辑逻辑
  }, []);

  /**
   * 处理状态变更
   */
  const handleStatusChange = useCallback(
    async (id: string | number, status: MerchantStatus): Promise<void> => {
      try {
        const [success, message_text] = await updateMerchantStatus(id, status);
        if (success) {
          message.success(message_text || '状态更新成功');
          fetchMerchantList();
        } else {
          message.error(message_text || '状态更新失败');
        }
      } catch (error) {
        console.error('状态更新出错:', error);
        message.error('状态更新出错');
      }
    },
    [fetchMerchantList]
  );

  /**
   * 处理批量分配
   */
  const handleBatchAssign = useCallback((): void => {
    if (selectedRowKeys.length === 0) {
      message.warning('请先选择要分配的商户');
      return;
    }
    setBatchAssignVisible(true);
  }, [selectedRowKeys]);

  /**
   * 处理批量分配提交
   */
  const handleBatchAssignSubmit = useCallback(
    async (bdId: string): Promise<void> => {
      setBatchLoading(true);
      try {
        const [success, message_text] = await batchAssignMerchants(
          selectedRowKeys,
          bdId
        );
        if (success) {
          message.success(message_text || '批量分配成功');
          setBatchAssignVisible(false);
          setSelectedRowKeys([]);
          setSelectedRows([]);
          fetchMerchantList();
        } else {
          message.error(message_text || '批量分配失败');
        }
      } catch (error) {
        console.error('批量分配出错:', error);
        message.error('批量分配出错');
      } finally {
        setBatchLoading(false);
      }
    },
    [selectedRowKeys, fetchMerchantList]
  );

  /**
   * 处理批量创建
   */
  const handleBatchCreate = useCallback((): void => {
    console.log('批量创建');
    // TODO: 实现批量创建逻辑
  }, []);

  return {
    // 基础状态
    loading,
    data,
    total,
    currentPage,
    pageSize,

    // 表单数据
    formData,

    // 选项数据
    bdOptions,
    merchantStatusOptions,
    categoryOptions,
    cityOptions,
    brandOptions,
    levelOptions,
    franchiseTypeOptions,

    // 选择状态
    selectedRowKeys,
    selectedRows,

    // 模态框状态
    batchAssignVisible,
    batchLoading,

    // 事件处理函数
    handleFormDataChange,
    handleSearch,
    handleReset,
    handlePageChange,
    handleRowSelectionChange,
    handleViewDetail,
    handleEdit,
    handleStatusChange,
    handleBatchAssign,
    handleBatchAssignSubmit,
    handleBatchCreate,

    // 模态框控制函数
    setBatchAssignVisible,
  };
};
