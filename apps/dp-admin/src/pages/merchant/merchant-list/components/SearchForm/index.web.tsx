import { PlusOutlined, TeamOutlined } from '@ant-design/icons';
import {
  Button,
  Cascader,
  Col,
  DatePicker,
  Flex,
  Form,
  Input,
  Row,
  Select,
} from 'antd';
import type { RangePickerProps } from 'antd/es/date-picker';
import React from 'react';

// import type { SearchFormProps } from '../../types';

const { RangePicker } = DatePicker;
const { Option } = Select;

/**
 * 任务搜索表单组件
 *
 * 提供多维度的任务搜索和筛选功能，支持批量操作
 */
const SearchForm: React.FC<any> = ({
  formData,
  assigneeOptions,
  taskStatusOptions,
  taskTypeOptions,
  onFormDataChange,
  onSearch,
  onReset,
  onBatchAssign,
  onBatchCreate,
  onCityFilter,
}) => {
  return (
    // eslint-disable-next-line react/jsx-no-duplicate-props
    <Form layout="inline" colon className="w-full">
      <Row
        gutter={[0, 16]}
        align="middle"
        className="bg-white p-4 rounded-lg border border-gray-200 mb-4 w-full"
      >
        <Col xs={24} sm={12} md={6} lg={6}>
          <Form.Item name="name" label="搜索" layout="vertical">
            <Input placeholder="请输入搜索关键字, 支持商户ID/商户名称/商户标签/统一编码" />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={6} lg={6}>
          <Form.Item name="BDInfo" label="BD" layout="vertical">
            <Select
              options={[
                { value: 'jack', label: 'Jack' },
                { value: 'lucy', label: 'Lucy' },
                { value: 'Yiminghe', label: 'yiminghe' },
                { value: 'disabled', label: 'Disabled', disabled: true },
              ]}
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={6} lg={6}>
          <Form.Item name="type" label="商户狀態" layout="vertical">
            <Select
              options={[
                { value: 'jack', label: 'Jack' },
                { value: 'lucy', label: 'Lucy' },
                { value: 'Yiminghe', label: 'yiminghe' },
                { value: 'disabled', label: 'Disabled', disabled: true },
              ]}
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={6} lg={6}>
          <Form.Item name="status" label="商户类目" layout="vertical">
            <Select
              options={[
                { value: 'jack', label: 'Jack' },
                { value: 'lucy', label: 'Lucy' },
                { value: 'Yiminghe', label: 'yiminghe' },
                { value: 'disabled', label: 'Disabled', disabled: true },
              ]}
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={6} lg={6}>
          <Form.Item name="status" label="城市" layout="vertical">
            <Cascader
              style={{ width: '100%' }}
              options={[
                {
                  value: 'jiangsu',
                  label: 'Jiangsu',
                  children: [
                    {
                      value: 'nanjing',
                      label: 'Nanjing',
                    },
                  ],
                },
              ]}
              showSearch={{ filter: onCityFilter }}
              multiple
              maxTagCount="responsive"
              placeholder="請選擇城市"
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={6} lg={6}>
          <Form.Item name="status" label="商户品牌" layout="vertical">
            <Cascader
              style={{ width: '100%' }}
              options={[
                {
                  value: 'jiangsu',
                  label: 'Jiangsu',
                  children: [
                    {
                      value: 'nanjing',
                      label: 'Nanjing',
                    },
                  ],
                },
              ]}
              multiple
              maxTagCount="responsive"
              placeholder="請選擇商户品牌"
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={6} lg={6}>
          <Form.Item name="status" label="商户等级" layout="vertical">
            <Cascader
              style={{ width: '100%' }}
              options={[
                {
                  value: 'jiangsu',
                  label: 'Jiangsu',
                  children: [
                    {
                      value: 'nanjing',
                      label: 'Nanjing',
                    },
                  ],
                },
              ]}
              multiple
              maxTagCount="responsive"
              placeholder="請選擇商户品牌"
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={6} lg={6}>
          <Form.Item name="status" label="加盟类型" layout="vertical">
            <Cascader
              style={{ width: '100%' }}
              options={[
                {
                  value: 'jiangsu',
                  label: 'Jiangsu',
                  children: [
                    {
                      value: 'nanjing',
                      label: 'Nanjing',
                    },
                  ],
                },
              ]}
              multiple
              maxTagCount="responsive"
              placeholder="請選擇商户品牌"
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={6} lg={6}>
          <Form.Item name="time" label="更新时间" layout="vertical">
            <RangePicker
              value={formData.updateTime as RangePickerProps['value']}
              className="flex-1"
              style={{ width: '100%' }}
              format="YYYY-MM-DD"
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={6} lg={6}>
          <Form.Item name="time" label="入住时间" layout="vertical">
            <RangePicker
              value={formData.updateTime as RangePickerProps['value']}
              className="flex-1"
              style={{ width: '100%' }}
              format="YYYY-MM-DD"
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={12} lg={12}>
          <Form.Item name="time" label="地理位置" layout="vertical">
            <Flex align="center" gap={10}>
              <Select
                showSearch
                placeholder="请选择精确地址"
                optionFilterProp="label"
                options={[
                  {
                    value: 'jack',
                    label: 'Jack',
                  },
                  {
                    value: 'lucy',
                    label: 'Lucy',
                  },
                  {
                    value: 'tom',
                    label: 'Tom',
                  },
                ]}
              />
              <span className="whitespace-nowrap">半径</span>
              <div className="w-1/2">
                <Input placeholder="请输入10000以内的数字" />
              </div>
              <span>米</span>
            </Flex>
          </Form.Item>
        </Col>
        <Col xs={24}>
          <Form.Item>
            <div className="flex flex-wrap gap-3 justify-between items-center pt-2">
              <Flex gap={10}>
                <Button htmlType="submit" type="primary">
                  查询
                </Button>
                <Button htmlType="reset">重置</Button>
              </Flex>
              <Flex gap={10}>
                <Button type="primary" icon={<TeamOutlined />}>
                  批量分配
                </Button>
                <Button icon={<PlusOutlined />}>批量創建</Button>
              </Flex>
            </div>
          </Form.Item>
        </Col>
      </Row>
    </Form>
  );
};

export default SearchForm;
