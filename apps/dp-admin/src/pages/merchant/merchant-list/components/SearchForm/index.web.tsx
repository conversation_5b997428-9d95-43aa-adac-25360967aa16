import React from 'react';

import { PlusOutlined, TeamOutlined } from '@ant-design/icons';
import {
  Button,
  Cascader,
  Col,
  DatePicker,
  Flex,
  Form,
  Input,
  Row,
  Select,
} from 'antd';

import type { SearchFormProps } from '../../types';

const { RangePicker } = DatePicker;

/**
 * 商户搜索表单组件
 *
 * 提供多维度的商户搜索和筛选功能，支持批量操作
 */
const SearchForm: React.FC<SearchFormProps> = ({
  formData,
  bdOptions,
  merchantStatusOptions,
  categoryOptions,
  cityOptions,
  brandOptions,
  levelOptions,
  franchiseTypeOptions,
  onFormDataChange,
  onSearch,
  onReset,
  onBatchAssign,
  onBatchCreate,
}) => {
  const [form] = Form.useForm();

  // 当formData变化时，更新表单值
  React.useEffect(() => {
    form.setFieldsValue(formData);
  }, [form, formData]);

  // 处理重置
  const handleReset = () => {
    form.resetFields();
    onReset();
  };

  return (
    <Form form={form} layout="inline" className="w-full">
      <Row
        gutter={[0, 16]}
        align="middle"
        className="bg-white p-4 rounded-lg border border-gray-200 mb-4 w-full"
      >
        <Col xs={24} sm={12} md={6} lg={6}>
          <Form.Item name="keyword" label="搜索" layout="vertical">
            <Input
              placeholder="请输入搜索关键字, 支持商户ID/商户名称/商户标签/统一编码"
              onChange={(e) => onFormDataChange({ keyword: e.target.value })}
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={6} lg={6}>
          <Form.Item name="bd" label="BD" layout="vertical">
            <Select
              placeholder="请选择BD"
              onChange={(value) => onFormDataChange({ bd: value })}
              options={bdOptions}
              allowClear
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={6} lg={6}>
          <Form.Item name="merchantStatus" label="商户狀態" layout="vertical">
            <Select
              placeholder="请选择商户状态"
              mode="multiple"
              onChange={(value) => onFormDataChange({ merchantStatus: value })}
              options={merchantStatusOptions}
              allowClear
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={6} lg={6}>
          <Form.Item name="category" label="商户类目" layout="vertical">
            <Select
              placeholder="请选择商户类目"
              mode="multiple"
              onChange={(value) => onFormDataChange({ category: value })}
              options={categoryOptions}
              allowClear
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={6} lg={6}>
          <Form.Item name="city" label="城市" layout="vertical">
            <Cascader
              style={{ width: '100%' }}
              options={cityOptions}
              onChange={(value) =>
                onFormDataChange({ city: value as string[][] })
              }
              multiple
              maxTagCount="responsive"
              placeholder="請選擇城市"
              allowClear
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={6} lg={6}>
          <Form.Item name="brand" label="商户品牌" layout="vertical">
            <Select
              placeholder="请选择商户品牌"
              mode="multiple"
              onChange={(value) => onFormDataChange({ brand: value })}
              options={brandOptions}
              allowClear
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={6} lg={6}>
          <Form.Item name="level" label="商户等级" layout="vertical">
            <Select
              placeholder="请选择商户等级"
              mode="multiple"
              onChange={(value) => onFormDataChange({ level: value })}
              options={levelOptions}
              allowClear
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={6} lg={6}>
          <Form.Item name="franchiseType" label="加盟类型" layout="vertical">
            <Select
              placeholder="请选择加盟类型"
              mode="multiple"
              onChange={(value) => onFormDataChange({ franchiseType: value })}
              options={franchiseTypeOptions}
              allowClear
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={6} lg={6}>
          <Form.Item name="updateTime" label="更新时间" layout="vertical">
            <RangePicker
              onChange={(dates) =>
                onFormDataChange({
                  updateTime: dates
                    ? ([
                        dates[0]?.format('YYYY-MM-DD'),
                        dates[1]?.format('YYYY-MM-DD'),
                      ] as [string, string])
                    : null,
                })
              }
              className="flex-1"
              style={{ width: '100%' }}
              format="YYYY-MM-DD"
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={6} lg={6}>
          <Form.Item name="joinTime" label="入住时间" layout="vertical">
            <RangePicker
              onChange={(dates) =>
                onFormDataChange({
                  joinTime: dates
                    ? ([
                        dates[0]?.format('YYYY-MM-DD'),
                        dates[1]?.format('YYYY-MM-DD'),
                      ] as [string, string])
                    : null,
                })
              }
              className="flex-1"
              style={{ width: '100%' }}
              format="YYYY-MM-DD"
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={12} lg={12}>
          <Form.Item label="地理位置" layout="vertical">
            <Flex align="center" gap={10}>
              <Form.Item name="location" noStyle>
                <Select
                  showSearch
                  placeholder="请选择精确地址"
                  onChange={(value) => onFormDataChange({ location: value })}
                  optionFilterProp="label"
                  options={[
                    {
                      value: 'taipei_101',
                      label: '台北101',
                    },
                    {
                      value: 'taichung_station',
                      label: '台中火车站',
                    },
                    {
                      value: 'kaohsiung_port',
                      label: '高雄港',
                    },
                  ]}
                  allowClear
                />
              </Form.Item>
              <span className="whitespace-nowrap">半径</span>
              <div className="w-1/2">
                <Form.Item name="radius" noStyle>
                  <Input
                    placeholder="请输入10000以内的数字"
                    type="number"
                    max={10000}
                    min={0}
                    onChange={(e) =>
                      onFormDataChange({ radius: Number(e.target.value) })
                    }
                  />
                </Form.Item>
              </div>
              <span>米</span>
            </Flex>
          </Form.Item>
        </Col>
        <Col xs={24}>
          <Form.Item>
            <div className="flex flex-wrap gap-3 justify-between items-center pt-2">
              <Flex gap={10}>
                <Button type="primary" onClick={onSearch}>
                  查询
                </Button>
                <Button onClick={handleReset}>重置</Button>
              </Flex>
              <Flex gap={10}>
                <Button
                  type="primary"
                  icon={<TeamOutlined />}
                  onClick={onBatchAssign}
                >
                  批量分配
                </Button>
                <Button icon={<PlusOutlined />} onClick={onBatchCreate}>
                  批量創建
                </Button>
              </Flex>
            </div>
          </Form.Item>
        </Col>
      </Row>
    </Form>
  );
};

export default SearchForm;
