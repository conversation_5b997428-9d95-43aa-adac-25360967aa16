import { Table } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import React from 'react';

import type { TaskData, TaskTableProps } from '../../types';

/**
 * 商户列表表格组件
 *
 * 显示商户列表数据，支持分页、选择和操作
 */
const MerchantListTable: React.FC<TaskTableProps> = ({
  loading,
  data,
  selectedRowKeys,
  currentPage,
  pageSize,
  total,
  onPageChange,
  onRowSelectionChange,
  onTaskDetail: _onTaskDetail,
  onTaskHandler: _onTaskHandler,
  onTaskAssignee: _onTaskAssignee,
  onFollowUpHistory: _onFollowUpHistory,
}) => {
  // 定义表格列
  const columns: ColumnsType<TaskData> = [
    {
      title: '商戶ID',
      dataIndex: 'merchantId',
      key: 'merchantId',
    },
    {
      title: '商戶狀態',
      dataIndex: 'merchantId',
      key: 'merchantId',
    },
    {
      title: '商戶類目',
      dataIndex: 'merchantId',
      key: 'merchantId',
    },
    {
      title: '商戶圖片',
      dataIndex: 'merchantId',
      key: 'merchantId',
    },
    {
      title: '城市',
      dataIndex: 'merchantId',
      key: 'merchantId',
    },
    {
      title: '行政區',
      dataIndex: 'merchantId',
      key: 'merchantId',
    },
    {
      title: '商圈',
      dataIndex: 'merchantId',
      key: 'merchantId',
    },
    {
      title: '商戶名稱',
      dataIndex: 'merchantId',
      key: 'merchantId',
    },
    {
      title: '商戶地址',
      dataIndex: 'merchantId',
      key: 'merchantId',
    },
    {
      title: '營業狀態',
      dataIndex: 'merchantId',
      key: 'merchantId',
    },
    {
      title: '品牌',
      dataIndex: 'merchantId',
      key: 'merchantId',
    },
    {
      title: '商戶等級',
      dataIndex: 'merchantId',
      key: 'merchantId',
    },
    {
      title: '統一編碼',
      dataIndex: 'merchantId',
      key: 'merchantId',
    },
    {
      title: '加盟類型',
      dataIndex: 'merchantId',
      key: 'merchantId',
    },
    {
      title: 'BD',
      dataIndex: 'merchantId',
      key: 'merchantId',
    },
    {
      title: '更新時間',
      dataIndex: 'merchantId',
      key: 'merchantId',
    },
    {
      title: '入住時間',
      dataIndex: 'merchantId',
      key: 'merchantId',
    },
    {
      title: '操作',
      dataIndex: 'merchantId',
      key: 'merchantId',
      fixed: 'right',
    },
  ];

  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedRowKeys: React.Key[], selectedRows: TaskData[]) => {
      onRowSelectionChange(
        selectedRowKeys as (string | number)[],
        selectedRows
      );
    },
    getCheckboxProps: (record: TaskData) => ({
      disabled: false,
      name: record.id.toString(),
    }),
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
      <Table
        columns={columns}
        dataSource={data}
        rowKey="id"
        loading={loading}
        rowSelection={rowSelection}
        pagination={{
          current: currentPage,
          pageSize: pageSize,
          total: total,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 條，共 ${total} 條`,
          pageSizeOptions: ['10', '20', '50', '100'],
          onChange: onPageChange,
          onShowSizeChange: onPageChange,
          style: { margin: '16px' },
        }}
        scroll={{ x: 1400 }}
        size="middle"
        className="w-full"
        rowClassName="hover:bg-gray-50"
      />
    </div>
  );
};

export default MerchantListTable;
