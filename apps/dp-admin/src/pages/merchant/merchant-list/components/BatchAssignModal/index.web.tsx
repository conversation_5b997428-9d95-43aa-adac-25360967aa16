import { Form, Modal, Select } from 'antd';
import React, { useEffect } from 'react';

import type { BDOption, MerchantData } from '../../types';

interface BatchAssignModalProps {
  /** 模态框可见性 */
  visible: boolean;
  /** 确认加载状态 */
  confirmLoading: boolean;
  /** 选中的商户 */
  selectedMerchants: MerchantData[];
  /** BD选项 */
  bdOptions: BDOption[];
  /** 取消回调 */
  onCancel: () => void;
  /** 提交回调 */
  onSubmit: (bdId: string) => void;
}

/**
 * 批量分配模态框组件
 */
const BatchAssignModal: React.FC<BatchAssignModalProps> = ({
  visible,
  confirmLoading,
  selectedMerchants,
  bdOptions,
  onCancel,
  onSubmit,
}) => {
  const [form] = Form.useForm();

  // 重置表单
  useEffect(() => {
    if (visible) {
      form.resetFields();
    }
  }, [visible, form]);

  // 处理提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      onSubmit(values.bdId);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  return (
    <Modal
      title="批量分配商戶"
      open={visible}
      onOk={handleSubmit}
      onCancel={onCancel}
      confirmLoading={confirmLoading}
      destroyOnHidden
      width={500}
    >
      <div className="mb-4">
        <p className="text-gray-600 mb-2">
          已選中 <span className="font-semibold text-blue-600">{selectedMerchants.length}</span> 個商戶
        </p>
        <div className="max-h-32 overflow-y-auto bg-gray-50 p-2 rounded">
          {selectedMerchants.map((merchant) => (
            <div key={merchant.id} className="text-sm text-gray-700 py-1">
              {merchant.merchantId} - {merchant.merchantName}
            </div>
          ))}
        </div>
      </div>

      <Form
        form={form}
        layout="vertical"
        requiredMark={false}
      >
        <Form.Item
          name="bdId"
          label="選擇BD"
          rules={[
            { required: true, message: '請選擇要分配的BD' },
          ]}
        >
          <Select
            placeholder="請選擇BD"
            options={bdOptions}
            showSearch
            optionFilterProp="label"
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default BatchAssignModal;
