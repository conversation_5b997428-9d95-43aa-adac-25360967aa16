import React from 'react';

import BatchAssignModal from './components/BatchAssignModal/index.web';
import MerchantListTable from './components/MerchantListTable/index.web';
import SearchForm from './components/SearchForm/index.web';
import { useMerchantList } from './services';

/**
 * 商户列表页面
 */
const MerchantListPage: React.FC = () => {
  const {
    // 基础状态
    loading,
    searchLoading,
    data,
    total,
    currentPage,
    pageSize,

    // 表单数据
    formData,

    // 选项数据
    bdOptions,
    merchantStatusOptions,
    categoryOptions,
    cityOptions,
    brandOptions,
    levelOptions,
    franchiseTypeOptions,

    // 选择状态
    selectedRowKeys,
    selectedRows,

    // 模态框状态
    batchAssignVisible,
    batchLoading,

    // 事件处理函数
    handleFormDataChange,
    handleSearch,
    handleReset,
    handlePageChange,
    handleRowSelectionChange,
    handleViewDetail,
    handleEdit,
    handleStatusChange,
    handleBatchAssign,
    handleBatchAssignSubmit,
    handleBatchCreate,

    // 模态框控制函数
    setBatchAssignVisible,
  } = useMerchantList();

  return (
    <div className="bg-white p-4 sm:p-6 md:p-8 rounded-lg shadow-sm">
      {/* 页面容器 */}
      <div className="mx-auto p-4 space-y-4">
        {/* 页面标题区域 */}
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <h1 className="text-xl font-semibold text-gray-900 mb-2">
            商戶列表管理
          </h1>
          <p className="text-sm text-gray-600">
            管理商戶信息，支持搜索篩選、批量分配和狀態管理功能
          </p>
        </div>

        {/* 搜索表单区域 */}
        <SearchForm
          formData={formData}
          loading={searchLoading}
          bdOptions={bdOptions}
          merchantStatusOptions={merchantStatusOptions}
          categoryOptions={categoryOptions}
          cityOptions={cityOptions}
          brandOptions={brandOptions}
          levelOptions={levelOptions}
          franchiseTypeOptions={franchiseTypeOptions}
          onFormDataChange={handleFormDataChange}
          onSearch={handleSearch}
          onReset={handleReset}
          onBatchAssign={handleBatchAssign}
          onBatchCreate={handleBatchCreate}
        />

        {/* 商户表格区域 */}
        <MerchantListTable
          loading={loading}
          data={data}
          selectedRowKeys={selectedRowKeys}
          currentPage={currentPage}
          pageSize={pageSize}
          total={total}
          onPageChange={handlePageChange}
          onRowSelectionChange={handleRowSelectionChange}
          onViewDetail={handleViewDetail}
          onEdit={handleEdit}
          onStatusChange={handleStatusChange}
        />

        {/* 批量分配模态框 */}
        <BatchAssignModal
          visible={batchAssignVisible}
          confirmLoading={batchLoading}
          selectedMerchants={selectedRows}
          bdOptions={bdOptions}
          onCancel={() => setBatchAssignVisible(false)}
          onSubmit={handleBatchAssignSubmit}
        />
      </div>
    </div>
  );
};

export default MerchantListPage;
