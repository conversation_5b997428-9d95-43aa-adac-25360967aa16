/**
 * 商户列表数据类型定义
 */

/**
 * 商户状态枚举
 */
export enum MerchantStatus {
  /** 待审核 */
  PENDING = 'PENDING',
  /** 已通过 */
  APPROVED = 'APPROVED',
  /** 已驳回 */
  REJECTED = 'REJECTED',
  /** 已停用 */
  DISABLED = 'DISABLED',
}

/**
 * 营业状态枚举
 */
export enum BusinessStatus {
  /** 营业中 */
  OPEN = 'OPEN',
  /** 暂停营业 */
  CLOSED = 'CLOSED',
  /** 永久关闭 */
  PERMANENTLY_CLOSED = 'PERMANENTLY_CLOSED',
}

/**
 * 商户数据接口
 */
export interface TaskData {
  /** 商户ID */
  id: string | number;
  /** 商户ID */
  merchantId: string;
  /** 商户状态 */
  merchantStatus: MerchantStatus;
  /** 商户类目 */
  category: string;
  /** 商户图片 */
  imageUrl?: string;
  /** 城市 */
  city: string;
  /** 行政区 */
  district: string;
  /** 商圈 */
  businessArea: string;
  /** 商户名称 */
  merchantName: string;
  /** 商户地址 */
  address: string;
  /** 营业状态 */
  businessStatus: BusinessStatus;
  /** 品牌 */
  brand: string;
  /** 商户等级 */
  level: string;
  /** 统一编码 */
  unifiedCode: string;
  /** 加盟类型 */
  franchiseType: string;
  /** BD */
  bd: string;
  /** 更新时间 */
  updatedAt: string;
  /** 入住时间 */
  joinedAt: string;
}

/**
 * 商户表格组件属性接口
 */
export interface TaskTableProps {
  /** 加载状态 */
  loading: boolean;
  /** 商户数据列表 */
  data: TaskData[];
  /** 选中行的键值 */
  selectedRowKeys: (string | number)[];
  /** 当前页码 */
  currentPage: number;
  /** 每页条数 */
  pageSize: number;
  /** 总条数 */
  total: number;
  /** 页码变更回调 */
  onPageChange: (page: number, pageSize?: number) => void;
  /** 行选择变更回调 */
  onRowSelectionChange: (
    selectedRowKeys: (string | number)[],
    selectedRows: TaskData[]
  ) => void;
  /** 查看详情回调 */
  onTaskDetail: (id: string | number) => void;
  /** 处理回调 */
  onTaskHandler: (id: string | number) => void;
  /** 分配回调 */
  onTaskAssignee: (id: string | number) => void;
  /** 查看历史回调 */
  onFollowUpHistory: (id: string | number) => void;
}
