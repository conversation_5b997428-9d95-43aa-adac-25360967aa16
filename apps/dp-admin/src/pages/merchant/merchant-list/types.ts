/**
 * 商戶列表數據類型定義
 */

/**
 * 商戶狀態枚舉
 */
export enum MerchantStatus {
  /** 待審核 */
  PENDING = 'PENDING',
  /** 已通過 */
  APPROVED = 'APPROVED',
  /** 已駁回 */
  REJECTED = 'REJECTED',
  /** 已停用 */
  DISABLED = 'DISABLED',
}

/**
 * 營業狀態枚舉
 */
export enum BusinessStatus {
  /** 營業中 */
  OPEN = 'OPEN',
  /** 暫停營業 */
  CLOSED = 'CLOSED',
  /** 永久關閉 */
  PERMANENTLY_CLOSED = 'PERMANENTLY_CLOSED',
}

/**
 * 商戶數據接口
 */
export interface TaskData {
  /** 商戶ID */
  id: string | number;
  /** 商戶ID */
  merchantId: string;
  /** 商戶狀態 */
  merchantStatus: MerchantStatus;
  /** 商戶類目 */
  category: string;
  /** 商戶圖片 */
  imageUrl?: string;
  /** 城市 */
  city: string;
  /** 行政區 */
  district: string;
  /** 商圈 */
  businessArea: string;
  /** 商戶名稱 */
  merchantName: string;
  /** 商戶地址 */
  address: string;
  /** 營業狀態 */
  businessStatus: BusinessStatus;
  /** 品牌 */
  brand: string;
  /** 商戶等級 */
  level: string;
  /** 統一編碼 */
  unifiedCode: string;
  /** 加盟類型 */
  franchiseType: string;
  /** BD */
  bd: string;
  /** 更新時間 */
  updatedAt: string;
  /** 入住時間 */
  joinedAt: string;
}

/**
 * 商戶表格組件屬性接口
 */
export interface TaskTableProps {
  /** 加載狀態 */
  loading: boolean;
  /** 商戶數據列表 */
  data: TaskData[];
  /** 選中行的鍵值 */
  selectedRowKeys: (string | number)[];
  /** 當前頁碼 */
  currentPage: number;
  /** 每頁條數 */
  pageSize: number;
  /** 總條數 */
  total: number;
  /** 頁碼變更回調 */
  onPageChange: (page: number, pageSize?: number) => void;
  /** 行選擇變更回調 */
  onRowSelectionChange: (
    selectedRowKeys: (string | number)[],
    selectedRows: TaskData[]
  ) => void;
  /** 查看詳情回調 */
  onTaskDetail: (id: string | number) => void;
  /** 處理回調 */
  onTaskHandler: (id: string | number) => void;
  /** 分配回調 */
  onTaskAssignee: (id: string | number) => void;
  /** 查看歷史回調 */
  onFollowUpHistory: (id: string | number) => void;
}