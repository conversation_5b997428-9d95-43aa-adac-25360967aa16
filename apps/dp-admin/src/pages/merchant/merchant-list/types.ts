/**
 * 商户列表数据类型定义
 */

/**
 * 商户状态枚举
 */
export enum MerchantStatus {
  /** 待审核 */
  PENDING = 'PENDING',
  /** 已通过 */
  APPROVED = 'APPROVED',
  /** 已驳回 */
  REJECTED = 'REJECTED',
  /** 已停用 */
  DISABLED = 'DISABLED',
}

/**
 * 营业状态枚举
 */
export enum BusinessStatus {
  /** 营业中 */
  OPEN = 'OPEN',
  /** 暂停营业 */
  CLOSED = 'CLOSED',
  /** 永久关闭 */
  PERMANENTLY_CLOSED = 'PERMANENTLY_CLOSED',
}

/**
 * 商户等级枚举
 */
export enum MerchantLevel {
  /** A级 */
  A = 'A',
  /** B级 */
  B = 'B',
  /** C级 */
  C = 'C',
  /** D级 */
  D = 'D',
}

/**
 * 加盟类型枚举
 */
export enum FranchiseType {
  /** 直营 */
  DIRECT = 'DIRECT',
  /** 加盟 */
  FRANCHISE = 'FRANCHISE',
  /** 合作 */
  COOPERATION = 'COOPERATION',
}

/**
 * 商户数据接口
 */
export interface MerchantData {
  /** 商户ID */
  id: string | number;
  /** 商户ID */
  merchantId: string;
  /** 商户状态 */
  merchantStatus: MerchantStatus;
  /** 商户类目 */
  category: string;
  /** 商户图片 */
  imageUrl?: string;
  /** 城市 */
  city: string;
  /** 行政区 */
  district: string;
  /** 商圈 */
  businessArea: string;
  /** 商户名称 */
  merchantName: string;
  /** 商户地址 */
  address: string;
  /** 营业状态 */
  businessStatus: BusinessStatus;
  /** 品牌 */
  brand: string;
  /** 商户等级 */
  level: MerchantLevel;
  /** 统一编码 */
  unifiedCode: string;
  /** 加盟类型 */
  franchiseType: FranchiseType;
  /** BD */
  bd: string;
  /** 更新时间 */
  updatedAt: string;
  /** 入住时间 */
  joinedAt: string;
}

/**
 * 搜索表单数据接口
 */
export interface SearchFormData {
  /** 搜索关键字 */
  keyword?: string;
  /** BD */
  bd?: string;
  /** 商户状态 */
  merchantStatus?: MerchantStatus[];
  /** 商户类目 */
  category?: string[];
  /** 城市 */
  city?: string[][];
  /** 商户品牌 */
  brand?: string[];
  /** 商户等级 */
  level?: MerchantLevel[];
  /** 加盟类型 */
  franchiseType?: FranchiseType[];
  /** 更新时间 */
  updateTime?: [string, string] | null;
  /** 入住时间 */
  joinTime?: [string, string] | null;
  /** 地理位置 */
  location?: string;
  /** 半径 */
  radius?: number;
}

/**
 * 选项数据接口
 */
export interface OptionData {
  value: string;
  label: string;
  children?: OptionData[];
}

/**
 * BD选项接口
 */
export interface BDOption {
  value: string;
  label: string;
}

/**
 * 搜索表单组件属性接口
 */
export interface SearchFormProps {
  /** 表单数据 */
  formData: SearchFormData;
  /** BD选项 */
  bdOptions: BDOption[];
  /** 商户状态选项 */
  merchantStatusOptions: OptionData[];
  /** 商户类目选项 */
  categoryOptions: OptionData[];
  /** 城市选项 */
  cityOptions: OptionData[];
  /** 品牌选项 */
  brandOptions: OptionData[];
  /** 等级选项 */
  levelOptions: OptionData[];
  /** 加盟类型选项 */
  franchiseTypeOptions: OptionData[];
  /** 表单数据变更回调 */
  onFormDataChange: (data: Partial<SearchFormData>) => void;
  /** 搜索回调 */
  onSearch: () => void;
  /** 重置回调 */
  onReset: () => void;
  /** 批量分配回调 */
  onBatchAssign: () => void;
  /** 批量创建回调 */
  onBatchCreate: () => void;
}

/**
 * 商户表格组件属性接口
 */
export interface MerchantTableProps {
  /** 加载状态 */
  loading: boolean;
  /** 商户数据列表 */
  data: MerchantData[];
  /** 选中行的键值 */
  selectedRowKeys: (string | number)[];
  /** 当前页码 */
  currentPage: number;
  /** 每页条数 */
  pageSize: number;
  /** 总条数 */
  total: number;
  /** 页码变更回调 */
  onPageChange: (page: number, pageSize?: number) => void;
  /** 行选择变更回调 */
  onRowSelectionChange: (
    selectedRowKeys: (string | number)[],
    selectedRows: MerchantData[]
  ) => void;
  /** 查看详情回调 */
  onViewDetail: (id: string | number) => void;
  /** 编辑回调 */
  onEdit: (id: string | number) => void;
  /** 状态变更回调 */
  onStatusChange: (id: string | number, status: MerchantStatus) => void;
}

// 保持向后兼容
export type TaskData = MerchantData;
export type TaskTableProps = MerchantTableProps;
