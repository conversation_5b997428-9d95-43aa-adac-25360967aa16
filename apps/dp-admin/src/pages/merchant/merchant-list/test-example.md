# 商戶列表功能測試說明

## 🔍 搜索功能測試

### 1. 搜索Loading效果測試
- **測試步驟**:
  1. 在搜索表單中輸入任意關鍵字
  2. 點擊「查詢」按鈕
  3. 觀察按鈕狀態變化

- **預期結果**:
  - ✅ 查詢按鈕顯示loading狀態（轉圈圖標）
  - ✅ 重置按鈕變為禁用狀態
  - ✅ 搜索完成後按鈕恢復正常狀態

### 2. 搜索功能測試
- **測試場景**:
  - 關鍵字搜索：輸入商戶ID、商戶名稱等
  - BD篩選：選擇特定BD
  - 商戶狀態：多選不同狀態
  - 商戶類目：多選不同類目
  - 城市選擇：級聯選擇城市
  - 時間範圍：選擇更新時間和入住時間
  - 地理位置：選擇地址和半徑

- **預期結果**:
  - ✅ 每次搜索都會顯示loading效果
  - ✅ 搜索結果正確更新
  - ✅ 頁碼重置為第1頁

## 🔄 重置功能測試

### 1. 重置Loading效果測試
- **測試步驟**:
  1. 在搜索表單中填入各種篩選條件
  2. 點擊「重置」按鈕
  3. 觀察按鈕狀態和表單變化

- **預期結果**:
  - ✅ 重置按鈕顯示禁用狀態
  - ✅ 查詢按鈕顯示loading狀態
  - ✅ 所有表單字段清空
  - ✅ 頁碼重置為第1頁
  - ✅ 自動觸發數據刷新
  - ✅ 表格顯示重置後的數據

### 2. 重置功能完整性測試
- **測試場景**:
  - 填入所有可能的篩選條件
  - 點擊重置按鈕
  - 檢查每個字段是否正確重置

- **預期結果**:
  - ✅ 關鍵字字段清空
  - ✅ BD選擇清空
  - ✅ 所有多選字段清空
  - ✅ 時間範圍清空
  - ✅ 地理位置相關字段清空
  - ✅ 數據自動刷新為初始狀態

## 🎯 用戶體驗測試

### 1. 按鈕狀態測試
- **搜索時**:
  - 查詢按鈕：顯示loading圖標，文字為「查詢」
  - 重置按鈕：禁用狀態，防止誤操作

- **重置時**:
  - 查詢按鈕：顯示loading圖標
  - 重置按鈕：禁用狀態

### 2. 響應速度測試
- **測試要點**:
  - loading狀態應該立即顯示
  - 操作完成後狀態應該立即恢復
  - 不應該有明顯的延遲或卡頓

## 🐛 邊界情況測試

### 1. 快速連續操作測試
- **測試步驟**:
  1. 快速連續點擊查詢按鈕
  2. 在loading期間點擊重置按鈕
  3. 觀察系統行為

- **預期結果**:
  - ✅ 防止重複提交
  - ✅ 按鈕狀態正確
  - ✅ 不會出現異常錯誤

### 2. 網絡異常測試
- **測試場景**:
  - 模擬網絡延遲
  - 模擬網絡錯誤
  - 觀察loading狀態和錯誤處理

- **預期結果**:
  - ✅ loading狀態正確顯示和隱藏
  - ✅ 錯誤信息正確提示
  - ✅ 按鈕狀態正確恢復

## 📝 測試檢查清單

### 搜索功能 ✅
- [x] 查詢按鈕loading效果
- [x] 重置按鈕禁用效果
- [x] 搜索結果正確更新
- [x] 頁碼正確重置

### 重置功能 ✅
- [x] 重置按鈕禁用效果
- [x] 查詢按鈕loading效果
- [x] 表單字段正確清空
- [x] 數據自動刷新
- [x] 頁碼正確重置

### 用戶體驗 ✅
- [x] 按鈕狀態變化流暢
- [x] loading圖標正確顯示
- [x] 操作反饋及時
- [x] 防止重複操作

### 錯誤處理 ✅
- [x] 網絡錯誤處理
- [x] 異常情況處理
- [x] 狀態正確恢復

## 🔧 技術實現要點

### 1. 狀態管理
```typescript
// 分離搜索loading和表格loading
const [loading, setLoading] = useState<boolean>(false);          // 表格loading
const [searchLoading, setSearchLoading] = useState<boolean>(false); // 搜索loading
```

### 2. 搜索實現
```typescript
const handleSearch = useCallback(async (): Promise<void> => {
  setCurrentPage(1);
  setSearchLoading(true);  // 開始loading
  try {
    // 執行搜索邏輯
  } finally {
    setSearchLoading(false); // 結束loading
  }
}, [formData, pageSize]);
```

### 3. 重置實現
```typescript
const handleReset = useCallback(async (): Promise<void> => {
  // 重置表單數據
  setFormData(resetData);
  setCurrentPage(1);
  
  // 自動觸發數據刷新
  setSearchLoading(true);
  try {
    // 執行數據刷新
  } finally {
    setSearchLoading(false);
  }
}, [pageSize]);
```

### 4. UI狀態綁定
```tsx
<Button type="primary" loading={loading} onClick={onSearch}>
  查詢
</Button>
<Button disabled={loading} onClick={onReset}>
  重置
</Button>
```

## ✨ 改進效果

### 修復前問題
- ❌ 搜索時沒有loading效果
- ❌ 重置後不會自動刷新數據
- ❌ 用戶體驗不佳

### 修復後效果
- ✅ 搜索時顯示loading效果
- ✅ 重置時顯示loading效果並禁用按鈕
- ✅ 重置後自動刷新數據
- ✅ 防止重複操作
- ✅ 用戶體驗良好

## 📞 問題反饋

如果在測試過程中發現任何問題，請記錄以下信息：
1. 操作步驟
2. 預期結果
3. 實際結果
4. 瀏覽器和版本
5. 錯誤信息（如有）
