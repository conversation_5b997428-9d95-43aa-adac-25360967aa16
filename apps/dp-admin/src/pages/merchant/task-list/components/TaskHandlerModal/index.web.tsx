import { Button, DatePicker, Form, Modal, Radio } from 'antd';
import TextArea from 'antd/es/input/TextArea';
import React, { useCallback, useEffect, useState } from 'react';

import type {
  FollowUpStatusOption,
  TaskHandlerFormData,
  TaskHandlerModalProps,
} from '../../types';
import HistoryTable from '../HistoryTable/index.web';

/**
 * 跟進狀態選項
 */
const followUpStatusOptions: FollowUpStatusOption[] = [
  { label: '跟進中', value: 'IN_PROGRESS' },
  { label: '跟進完成', value: 'COMPLETED' },
  { label: '下次跟進', value: 'NEXT_FOLLOW_UP' },
];

const NEXT_FOLLOW_UP_TIME = 'NEXT_FOLLOW_UP';

/**
 * 任務處理彈窗組件
 *
 * 提供任務跟進狀態更新、下次跟進時間設置和跟進詳情記錄功能
 */
const TaskHandlerModal: React.FC<TaskHandlerModalProps> = ({
  visible,
  confirmLoading = false,
  taskId: _taskId,
  taskData,
  historyList,
  onCancel,
  onSave,
}) => {
  const [form] = Form.useForm<TaskHandlerFormData>();
  const status = Form.useWatch('followUpStatus', form);
  const [isShowNextFollowUpTime, setIsShowNextFollowUpTime] = useState(false);

  // 監聽跟進狀態變化，控制下次跟進時間顯示
  useEffect(() => {
    setIsShowNextFollowUpTime(status === NEXT_FOLLOW_UP_TIME);
  }, [status]);

  // 重置表單
  const handleReset = useCallback(() => {
    form.resetFields();
    setIsShowNextFollowUpTime(false);
  }, [form]);

  // 處理彈窗關閉
  const handleCancel = useCallback(() => {
    handleReset();
    onCancel();
  }, [handleReset, onCancel]);

  // 處理表單提交
  const handleSubmit = useCallback(async () => {
    try {
      const values = await form.validateFields();
      onSave(values);
      handleReset();
    } catch (error) {
      console.error('表單驗證失敗:', error);
    }
  }, [form, onSave, handleReset]);

  // 彈窗關閉時重置表單
  useEffect(() => {
    if (!visible) {
      handleReset();
    }
  }, [visible, handleReset]);

  return (
    <Modal
      open={visible}
      title="處理任務"
      width={800}
      destroyOnHidden
      onCancel={handleCancel}
      footer={null}
    >
      <div className="space-y-6">
        {/* 任務基本信息 */}
        {taskData && (
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="text-sm font-medium text-gray-900 mb-2">任務信息</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">任務名稱：</span>
                <span className="text-gray-900">{taskData.taskName}</span>
              </div>
              <div>
                <span className="text-gray-600">商戶名稱：</span>
                <span className="text-gray-900">{taskData.merchantName}</span>
              </div>
            </div>
          </div>
        )}

        {/* 跟進表單 */}
        <Form layout="vertical" form={form}>
          <Form.Item
            rules={[{ required: true, message: '請選擇本次跟進進度' }]}
            label="請選擇本次跟進進度"
            name="followUpStatus"
          >
            <Radio.Group buttonStyle="solid">
              {followUpStatusOptions.map((item) => (
                <Radio.Button key={item.value} value={item.value}>
                  {item.label}
                </Radio.Button>
              ))}
            </Radio.Group>
          </Form.Item>

          {isShowNextFollowUpTime && (
            <Form.Item
              name="nextFollowUpTime"
              label="下次跟進時間"
              rules={[{ required: true, message: '請選擇下次跟進時間' }]}
            >
              <DatePicker
                showTime
                format="YYYY-MM-DD HH:mm"
                placeholder="請選擇下次跟進時間"
                className="w-full"
              />
            </Form.Item>
          )}

          <Form.Item
            name="followUpDetail"
            label="請填寫跟進詳情"
            rules={[{ required: true, message: '請填寫跟進詳情' }]}
          >
            <TextArea
              rows={4}
              placeholder="請輸入跟進詳情，不超過2000字"
              maxLength={2000}
              showCount
            />
          </Form.Item>

          {/* 操作按鈕 */}
          <Form.Item className="mb-0">
            <div className="flex justify-end gap-3">
              <Button onClick={handleCancel}>取消</Button>
              <Button
                type="primary"
                loading={confirmLoading}
                onClick={handleSubmit}
              >
                保存
              </Button>
            </div>
          </Form.Item>
        </Form>

        {/* 跟進歷史 */}
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-3">跟進歷史</h4>
          <HistoryTable list={historyList} />
        </div>
      </div>
    </Modal>
  );
};

export default TaskHandlerModal;
