/* eslint-disable @typescript-eslint/no-explicit-any */
import { Form, Modal, Select, Table } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import React, { useEffect } from 'react';

import type { BatchAssignModalProps, TaskData } from '../../types';

const { Option } = Select;

/**
 * 批量分配任务模态框组件
 *
 * 用于批量分配任务给指定的跟进人
 */
const BatchAssignModal: React.FC<BatchAssignModalProps> = ({
  visible,
  confirmLoading,
  selectedTasks,
  assigneeOptions,
  onCancel,
  onSubmit,
}) => {
  const [form] = Form.useForm();

  // 当模态框显示时重置表单
  useEffect(() => {
    if (visible) {
      form.resetFields();
    }
  }, [visible, form]);

  // 处理表单提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      const assignee = assigneeOptions.find((a) => a.id === values.assigneeId);

      onSubmit({
        assigneeId: values.assigneeId,
        assigneeName: assignee?.name || '',
        taskIds: selectedTasks.map((task) => task.id),
      });
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 处理取消
  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  // 渲染跟进人选项
  const renderAssigneeOption = (option: any) => {
    const prefix = option.isCurrentUser ? '(我) ' : '';
    const emailPrefix = option.email.split('@')[0];
    return `${prefix}${option.name}+${emailPrefix}`;
  };

  // 任务列表表格列定义
  const columns: ColumnsType<TaskData> = [
    {
      title: '任務ID',
      dataIndex: 'id',
      key: 'id',
      width: 100,
    },
    {
      title: '任務類型',
      dataIndex: 'taskType',
      key: 'taskType',
      width: 100,
    },
    {
      title: '商戶名稱',
      dataIndex: 'merchantName',
      key: 'merchantName',
      width: 200,
      render: (text: string) => (
        <span className="line-clamp-1" title={text}>
          {text}
        </span>
      ),
    },
    {
      title: '當前跟進人',
      dataIndex: 'assignee',
      key: 'assignee',
      width: 120,
      render: (text: string) => (
        <span className="text-gray-600">
          {text || <span className="text-gray-400">未分配</span>}
        </span>
      ),
    },
  ];

  return (
    <Modal
      title="批量分配任務"
      open={visible}
      onOk={handleSubmit}
      onCancel={handleCancel}
      confirmLoading={confirmLoading}
      okText="確認分配"
      cancelText="取消"
      width={800}
      destroyOnHidden
      className="batch-assign-modal"
    >
      <div className="space-y-4">
        {/* 分配表单 */}
        <Form form={form} layout="horizontal" className="mb-4">
          <Form.Item
            label="選擇跟進人"
            name="assigneeId"
            rules={[{ required: true, message: '請選擇跟進人' }]}
          >
            <Select
              placeholder="請選擇要分配的跟進人"
              className="w-full"
              optionLabelProp="label"
            >
              {assigneeOptions.map((option) => {
                const label = renderAssigneeOption(option);
                return (
                  <Option key={option.id} value={option.id} label={label}>
                    {label}
                  </Option>
                );
              })}
            </Select>
          </Form.Item>
        </Form>

        {/* 选中的任务列表 */}
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-2">
            已選中的任務 ({selectedTasks.length} 個)
          </h4>
          <Table
            columns={columns}
            dataSource={selectedTasks}
            rowKey="id"
            pagination={false}
            size="small"
            scroll={{ y: 300 }}
            className="border border-gray-200 rounded"
          />
        </div>

        {/* 提示信息 */}
        <div className="bg-blue-50 border border-blue-200 rounded p-3">
          <p className="text-sm text-blue-800">
            <strong>注意：</strong>
            批量分配將會覆蓋所選任務的當前跟進人，請確認後再進行操作。
          </p>
        </div>
      </div>
    </Modal>
  );
};

export default BatchAssignModal;
