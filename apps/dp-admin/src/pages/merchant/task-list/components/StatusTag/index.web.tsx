import { Tag } from 'antd';
import React from 'react';

interface StatusTagProps {
  status: string;
}

/**
 * 任务状态标签组件
 *
 * 根据不同的任务状态显示对应颜色的标签
 */
const StatusTag: React.FC<StatusTagProps> = ({ status }) => {
  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'PENDING':
        return {
          color: 'orange',
          text: '待處理',
        };
      case 'IN_PROGRESS':
        return {
          color: 'blue',
          text: '進行中',
        };
      case 'COMPLETED':
        return {
          color: 'green',
          text: '已完成',
        };
      case 'CANCELLED':
        return {
          color: 'red',
          text: '已取消',
        };
      default:
        return {
          color: 'default',
          text: status,
        };
    }
  };

  const config = getStatusConfig(status);

  return (
    <Tag color={config.color} className="text-xs font-medium">
      {config.text}
    </Tag>
  );
};

export default StatusTag;
