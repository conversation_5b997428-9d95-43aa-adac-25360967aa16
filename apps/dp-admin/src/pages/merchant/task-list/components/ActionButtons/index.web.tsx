import { EyeOutlined, MoreOutlined } from '@ant-design/icons';
import type { MenuProps } from 'antd';
import { Button, Dropdown, Space } from 'antd';
import React from 'react';

import type { ActionButtonsProps } from '../../types';

/**
 * 操作按钮组件
 *
 * 提供任务的查看、编辑、删除等操作
 */
const ActionButtons: React.FC<ActionButtonsProps> = ({
  record,
  onDetail,
  onTaskHandler,
  onTaskAssignee,
  onFollowUpHistory,
}) => {
  // 下拉菜单项
  const menuItems: MenuProps['items'] = [
    {
      key: 'taskHandler',
      label: '處理任務',
      onClick: () => onTaskHandler(record.id),
      style: { color: '#eb984e' },
    },
    {
      key: 'taskAssignee',
      label: '分配任務',
      onClick: () => onTaskAssignee(record.id),
      style: { color: '#52be80' },
    },
    {
      key: 'followUpHistory',
      label: '跟進歷史',
      onClick: () => onFollowUpHistory(record.id),
      style: { color: '#9b59b6' },
    },
  ];

  return (
    <Space.Compact>
      {/* 查看详情按钮 */}
      <Button
        type="link"
        size="small"
        icon={<EyeOutlined />}
        onClick={() => onDetail(record.id)}
        className="text-blue-600 hover:text-blue-800"
        title="查看詳情"
      >
        查看詳情
      </Button>

      {/* 更多操作下拉菜单 */}
      <Dropdown
        menu={{ items: menuItems }}
        trigger={['click']}
        placement="bottomRight"
      >
        <Button
          type="text"
          size="small"
          icon={<MoreOutlined />}
          className="text-gray-600 hover:text-gray-800"
          title="更多操作"
        />
      </Dropdown>
    </Space.Compact>
  );
};

export default ActionButtons;
