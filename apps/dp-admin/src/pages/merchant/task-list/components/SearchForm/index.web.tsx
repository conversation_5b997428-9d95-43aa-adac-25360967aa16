import { PlusOutlined, TeamOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, <PERSON>r, Col, DatePicker, Input, Row, Select } from 'antd';
import type { RangePickerProps } from 'antd/es/date-picker';
import React from 'react';

import type { SearchFormProps } from '../../types';

const { RangePicker } = DatePicker;
const { Option } = Select;

/**
 * 任务搜索表单组件
 *
 * 提供多维度的任务搜索和筛选功能，支持批量操作
 */
const SearchForm: React.FC<SearchFormProps> = ({
  formData,
  assigneeOptions,
  taskStatusOptions,
  taskTypeOptions,
  onFormDataChange,
  onSearch,
  onReset,
  onBatchAssign,
  onBatchCreate,
  onCityFilter,
}) => {
  return (
    <div className="bg-white p-4 rounded-lg border border-gray-200 mb-4">
      <Row gutter={[16, 16]} align="middle">
        <Col xs={24} sm={12} md={8} lg={6}>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              商戶
            </label>
            <Input
              placeholder="請輸入商户GoogleID/ID/統編/食品編號/名称"
              value={formData.merchantInfo}
              onChange={(e) => onFormDataChange('merchantInfo', e.target.value)}
              allowClear
            />
          </div>
        </Col>

        <Col xs={24} sm={12} md={6} lg={6}>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              跟進人
            </label>
            <Select
              placeholder="請選擇跟進人"
              value={formData.assignee}
              onChange={(value) => onFormDataChange('assignee', value)}
              allowClear
              className="w-full"
              optionLabelProp="label"
            >
              <Option value="ALL" label="全部">
                全部
              </Option>
              <Option value="NO_ASSIGNEE" label="無跟進人">
                無跟進人
              </Option>
              {assigneeOptions.map((option) => {
                const prefix = option.isCurrentUser ? '(我) ' : '';
                const emailPrefix = option.email.split('@')[0];
                const label = `${prefix}${option.name}+${emailPrefix}`;
                return (
                  <Option key={option.id} value={option.id} label={label}>
                    {label}
                  </Option>
                );
              })}
            </Select>
          </div>
        </Col>

        <Col xs={24} sm={12} md={6} lg={6}>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              任務狀態
            </label>
            <Select
              mode="multiple"
              placeholder="請選擇任務狀態"
              value={formData.taskStatus}
              onChange={(value) => onFormDataChange('taskStatus', value)}
              allowClear
              className="w-full"
              maxTagCount="responsive"
            >
              {taskStatusOptions.map((option) => (
                <Option key={option.value} value={option.value}>
                  <span style={{ color: option.color }}>{option.label}</span>
                </Option>
              ))}
            </Select>
          </div>
        </Col>

        <Col xs={24} sm={12} md={6} lg={6}>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              任務類型
            </label>
            <Select
              mode="multiple"
              placeholder="請選擇任務類型"
              value={formData.taskType}
              onChange={(value) => onFormDataChange('taskType', value)}
              allowClear
              className="w-full"
              maxTagCount="responsive"
            >
              {taskTypeOptions.map((option) => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </div>
        </Col>
        <Col xs={24} sm={12} md={6} lg={6}>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              城市
            </label>
            <Cascader
              style={{ width: '100%' }}
              options={[
                {
                  value: 'jiangsu',
                  label: 'Jiangsu',
                  children: [
                    {
                      value: 'nanjing',
                      label: 'Nanjing',
                    },
                  ],
                },
              ]}
              onChange={(value) => onFormDataChange('city', value)}
              showSearch={{ filter: onCityFilter }}
              multiple
              maxTagCount="responsive"
              placeholder="請選擇城市"
            />
          </div>
        </Col>
        <Col xs={24} sm={12} md={6} lg={6}>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              下次跟進時間
            </label>
            <div className="flex gap-2">
              <RangePicker
                value={formData.followUpTime as RangePickerProps['value']}
                onChange={(dates) => onFormDataChange('followUpTime', dates)}
                className="flex-1"
                showTime
                format="YYYY-MM-DD HH:mm"
              />
            </div>
          </div>
        </Col>
        <Col xs={24} sm={12} md={6} lg={6}>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              生成時間
            </label>
            <div className="flex gap-2">
              <RangePicker
                value={formData.createTime as RangePickerProps['value']}
                onChange={(dates) => onFormDataChange('createTime', dates)}
                className="flex-1"
                showTime
                format="YYYY-MM-DD HH:mm"
              />
            </div>
          </div>
        </Col>
        <Col xs={24} sm={12} md={6} lg={6}>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              更新時間
            </label>
            <div className="flex gap-2">
              <RangePicker
                value={formData.updateTime as RangePickerProps['value']}
                onChange={(dates) => onFormDataChange('updateTime', dates)}
                className="flex-1"
                showTime
                format="YYYY-MM-DD HH:mm"
              />
            </div>
          </div>
        </Col>
        {/* 操作按钮 */}
        <Col xs={24}>
          <div className="flex flex-wrap gap-3 justify-between items-center pt-2">
            {/* 搜索和重置按钮 */}
            <div className="flex gap-3">
              <Button type="primary" onClick={onSearch} className="min-w-20">
                搜尋
              </Button>
              <Button onClick={onReset} className="min-w-20">
                重置
              </Button>
            </div>

            {/* 批量操作按钮 */}
            <div className="flex gap-3">
              <Button
                type="default"
                icon={<TeamOutlined />}
                onClick={onBatchAssign}
                className="min-w-24"
              >
                批量分配
              </Button>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={onBatchCreate}
                className="min-w-24"
              >
                批量創建
              </Button>
            </div>
          </div>
        </Col>
      </Row>
    </div>
  );
};

export default SearchForm;
