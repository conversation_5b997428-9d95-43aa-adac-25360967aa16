import { PlusOutlined, TeamOutlined } from '@ant-design/icons';
import { <PERSON>ton, Cascader, Col, DatePicker, Form, Input, Row, Select } from 'antd';
import React from 'react';

import type { SearchFormProps } from '../../types';

const { RangePicker } = DatePicker;
const { Option } = Select;

/**
 * 任务搜索表单组件
 *
 * 提供多维度的任务搜索和筛选功能，支持批量操作
 */
const SearchForm: React.FC<SearchFormProps> = ({
  formData,
  assigneeOptions,
  taskStatusOptions,
  taskTypeOptions,
  onFormDataChange,
  onSearch,
  onReset,
  onBatchAssign,
  onBatchCreate,
  onCityFilter,
}) => {
  const [form] = Form.useForm();

  // 同步表单值
  React.useEffect(() => {
    form.setFieldsValue(formData);
  }, [form, formData]);

  // 重置处理
  const handleReset = () => {
    form.resetFields();
    onReset();
  };
  return (
    <div className="bg-white p-4 rounded-lg border border-gray-200 mb-4">
      <Form form={form} layout="vertical">
        <Row gutter={[16, 16]} align="middle">
        <Col xs={24} sm={12} md={8} lg={6}>
          <Form.Item
            label="商戶"
            name="merchantInfo"
            className="mb-0"
          >
            <Input
              placeholder="請輸入商户GoogleID/ID/統編/食品編號/名称"
              onChange={(e) => onFormDataChange('merchantInfo', e.target.value)}
              allowClear
            />
          </Form.Item>
        </Col>

        <Col xs={24} sm={12} md={6} lg={6}>
          <Form.Item
            label="跟進人"
            name="assignee"
            className="mb-0"
          >
            <Select
              placeholder="請選擇跟進人"
              onChange={(value) => onFormDataChange('assignee', value)}
              allowClear
              className="w-full"
              optionLabelProp="label"
            >
              <Option value="ALL" label="全部">
                全部
              </Option>
              <Option value="NO_ASSIGNEE" label="無跟進人">
                無跟進人
              </Option>
              {assigneeOptions.map((option) => {
                const prefix = option.isCurrentUser ? '(我) ' : '';
                const emailPrefix = option.email.split('@')[0];
                const label = `${prefix}${option.name}+${emailPrefix}`;
                return (
                  <Option key={option.id} value={option.id} label={label}>
                    {label}
                  </Option>
                );
              })}
            </Select>
          </Form.Item>
        </Col>

        <Col xs={24} sm={12} md={6} lg={6}>
          <Form.Item
            label="任務狀態"
            name="taskStatus"
            className="mb-0"
          >
            <Select
              mode="multiple"
              placeholder="請選擇任務狀態"
              onChange={(value) => onFormDataChange('taskStatus', value)}
              allowClear
              className="w-full"
              maxTagCount="responsive"
            >
              {taskStatusOptions.map((option) => (
                <Option key={option.value} value={option.value}>
                  <span style={{ color: option.color }}>{option.label}</span>
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Col>

        <Col xs={24} sm={12} md={6} lg={6}>
          <Form.Item
            label="任務類型"
            name="taskType"
            className="mb-0"
          >
            <Select
              mode="multiple"
              placeholder="請選擇任務類型"
              onChange={(value) => onFormDataChange('taskType', value)}
              allowClear
              className="w-full"
              maxTagCount="responsive"
            >
              {taskTypeOptions.map((option) => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={6} lg={6}>
          <Form.Item
            label="城市"
            name="city"
            className="mb-0"
          >
            <Cascader
              style={{ width: '100%' }}
              options={[
                {
                  value: 'jiangsu',
                  label: 'Jiangsu',
                  children: [
                    {
                      value: 'nanjing',
                      label: 'Nanjing',
                    },
                  ],
                },
              ]}
              onChange={(value) => onFormDataChange('city', value as unknown as string[])}
              showSearch={{ filter: onCityFilter }}
              multiple
              maxTagCount="responsive"
              placeholder="請選擇城市"
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={6} lg={6}>
          <Form.Item
            label="下次跟進時間"
            name="followUpTime"
            className="mb-0"
          >
            <RangePicker
              onChange={(dates) => onFormDataChange('followUpTime', dates as any)}
              className="w-full"
              showTime
              format="YYYY-MM-DD HH:mm"
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={6} lg={6}>
          <Form.Item
            label="生成時間"
            name="createTime"
            className="mb-0"
          >
            <RangePicker
              onChange={(dates) => onFormDataChange('createTime', dates as any)}
              className="w-full"
              showTime
              format="YYYY-MM-DD HH:mm"
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={6} lg={6}>
          <Form.Item
            label="更新時間"
            name="updateTime"
            className="mb-0"
          >
            <RangePicker
              onChange={(dates) => onFormDataChange('updateTime', dates as any)}
              className="w-full"
              showTime
              format="YYYY-MM-DD HH:mm"
            />
          </Form.Item>
        </Col>
        {/* 操作按钮 */}
        <Col xs={24}>
          <div className="flex flex-wrap gap-3 justify-between items-center pt-2">
            {/* 搜索和重置按钮 */}
            <div className="flex gap-3">
              <Button type="primary" onClick={onSearch} className="min-w-20">
                搜尋
              </Button>
              <Button onClick={handleReset} className="min-w-20">
                重置
              </Button>
            </div>

            {/* 批量操作按钮 */}
            <div className="flex gap-3">
              <Button
                type="default"
                icon={<TeamOutlined />}
                onClick={onBatchAssign}
                className="min-w-24"
              >
                批量分配
              </Button>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={onBatchCreate}
                className="min-w-24"
              >
                批量創建
              </Button>
            </div>
          </div>
        </Col>
        </Row>
      </Form>
    </div>
  );
};

export default SearchForm;
