import { Tag } from 'antd';
import React from 'react';

interface PriorityTagProps {
  priority: string;
}

/**
 * 优先级标签组件
 *
 * 根据不同的优先级显示对应颜色的标签
 */
const PriorityTag: React.FC<PriorityTagProps> = ({ priority }) => {
  const getPriorityConfig = (priority: string) => {
    switch (priority) {
      case 'LOW':
        return {
          color: 'default',
          text: '低',
        };
      case 'MEDIUM':
        return {
          color: 'blue',
          text: '中',
        };
      case 'HIGH':
        return {
          color: 'orange',
          text: '高',
        };
      case 'URGENT':
        return {
          color: 'red',
          text: '緊急',
        };
      default:
        return {
          color: 'default',
          text: priority,
        };
    }
  };

  const config = getPriorityConfig(priority);

  return (
    <Tag color={config.color} className="text-xs font-medium">
      {config.text}
    </Tag>
  );
};

export default PriorityTag;
