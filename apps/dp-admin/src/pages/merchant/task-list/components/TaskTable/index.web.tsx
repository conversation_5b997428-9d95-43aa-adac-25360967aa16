import { Table, Tooltip } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import React from 'react';

import type { TaskData, TaskTableProps } from '../../types';
import ActionButtons from '../ActionButtons/index.web';
import PriorityTag from '../PriorityTag/index.web';
import StatusTag from '../StatusTag/index.web';

/**
 * 任务表格组件
 *
 * 显示任务列表数据，支持分页、选择和操作
 */
const TaskTable: React.FC<TaskTableProps> = ({
  loading,
  data,
  selectedRowKeys,
  currentPage,
  pageSize,
  total,
  onPageChange,
  onRowSelectionChange,
  onTaskDetail,
  onTaskHandler,
  onTaskAssignee,
  onFollowUpHistory,
}) => {
  // 定义表格列
  const columns: ColumnsType<TaskData> = [
    {
      title: '任務類型',
      dataIndex: 'taskType',
      key: 'taskType',
      width: 100,
      render: (text: string) => (
        <span className="text-gray-900 font-medium text-sm">{text}</span>
      ),
    },
    {
      title: '任務狀態',
      dataIndex: 'taskStatus',
      key: 'taskStatus',
      width: 100,
      align: 'center',
      render: (status: string) => <StatusTag status={status} />,
    },
    {
      title: '下次跟進時間',
      dataIndex: 'nextFollowUpTime',
      key: 'nextFollowUpTime',
      width: 140,
      render: (text: string) => (
        <span className="text-gray-900 text-sm">{text || '--'}</span>
      ),
    },
    {
      title: '生成時間',
      dataIndex: 'createdTime',
      key: 'createdTime',
      width: 140,
      render: (text: string) => (
        <span className="text-gray-600 text-sm">{text}</span>
      ),
    },
    {
      title: '更新時間',
      dataIndex: 'updatedTime',
      key: 'updatedTime',
      width: 140,
      render: (text: string) => (
        <span className="text-gray-600 text-sm">{text}</span>
      ),
    },
    {
      title: '商戶資訊',
      key: 'merchantInfo',
      width: 200,
      render: (_, record) => (
        <div className="text-sm">
          <div className="font-medium text-gray-900 mb-1 line-clamp-1">
            {record.merchantName}
          </div>
          <div className="text-gray-500 text-xs space-y-1">
            <div>ID: {record.merchantId}</div>
            {record.merchantGoogleId && (
              <div className="block">Google: {record.merchantGoogleId}</div>
            )}
            {record.merchantTaxId && (
              <div className="block">統編: {record.merchantTaxId}</div>
            )}
          </div>
        </div>
      ),
    },
    {
      title: 'GoogleID',
      dataIndex: 'merchantGoogleId',
      key: 'merchantGoogleId',
      width: 120,
      render: (text: string) => (
        <span className="text-gray-600 text-sm font-mono">{text || '--'}</span>
      ),
    },
    {
      title: '統編',
      dataIndex: 'merchantTaxId',
      key: 'merchantTaxId',
      width: 120,
      render: (text: string) => (
        <span className="text-gray-600 text-sm font-mono">{text || '--'}</span>
      ),
    },
    {
      title: '食品編號',
      dataIndex: 'merchantFoodId',
      key: 'merchantFoodId',
      width: 120,
      render: (text: string) => (
        <span className="text-gray-600 text-sm font-mono">{text || '--'}</span>
      ),
    },
    {
      title: '跟進人',
      dataIndex: 'assignee',
      key: 'assignee',
      width: 120,
      render: (text: string) => (
        <span className="text-gray-900 text-sm">
          {text || <span className="text-gray-400">未分配</span>}
        </span>
      ),
    },
    {
      title: '優先級',
      dataIndex: 'priority',
      key: 'priority',
      width: 80,
      align: 'center',
      render: (priority: string) => <PriorityTag priority={priority} />,
    },
    {
      title: '任務詳情',
      key: 'taskDetail',
      width: 200,
      render: (_, record) => (
        <div className="text-sm">
          <div className="font-medium text-gray-900 mb-1 line-clamp-1">
            {record.taskName}
          </div>
          <Tooltip title={record.taskDescription} placement="topLeft">
            <div className="text-gray-600 text-xs line-clamp-2">
              {record.taskDescription || '--'}
            </div>
          </Tooltip>
        </div>
      ),
    },
    {
      title: '創建人',
      dataIndex: 'createdBy',
      key: 'createdBy',
      width: 100,
      render: (text: string) => (
        <span className="text-gray-600 text-sm">{text}</span>
      ),
    },
    {
      title: '分配時間',
      key: 'assignTime',
      width: 140,
      render: (_, record) => (
        <span className="text-gray-600 text-sm">{record.updatedTime}</span>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 130,
      fixed: 'right',
      render: (_, record) => (
        <ActionButtons
          record={record}
          onDetail={onTaskDetail}
          onTaskHandler={onTaskHandler}
          onTaskAssignee={onTaskAssignee}
          onFollowUpHistory={onFollowUpHistory}
        />
      ),
    },
  ];

  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedRowKeys: React.Key[], selectedRows: TaskData[]) => {
      onRowSelectionChange(
        selectedRowKeys as (string | number)[],
        selectedRows
      );
    },
    getCheckboxProps: (record: TaskData) => ({
      disabled: false,
      name: record.id.toString(),
    }),
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
      <Table
        columns={columns}
        dataSource={data}
        rowKey="id"
        loading={loading}
        rowSelection={rowSelection}
        pagination={{
          current: currentPage,
          pageSize: pageSize,
          total: total,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 條，共 ${total} 條`,
          pageSizeOptions: ['10', '20', '50', '100'],
          onChange: onPageChange,
          onShowSizeChange: onPageChange,
          style: { margin: '16px' },
        }}
        scroll={{ x: 1400 }}
        size="middle"
        className="w-full"
        rowClassName="hover:bg-gray-50"
      />
    </div>
  );
};

export default TaskTable;
