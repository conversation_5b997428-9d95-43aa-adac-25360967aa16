import { Tag, Tooltip } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import React from 'react';

import type { FollowUpRecord, HistoryTableProps } from '../../types';
import CommTable from '../CommTable/index.web';

/**
 * 跟進歷史表格組件
 *
 * 顯示任務的跟進歷史記錄
 */
const HistoryTable: React.FC<HistoryTableProps> = ({
  list,
  loading = false,
}) => {
  // 獲取跟進狀態配置
  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'IN_PROGRESS':
        return { color: 'blue', text: '跟進中' };
      case 'COMPLETED':
        return { color: 'green', text: '跟進完成' };
      case 'NEXT_FOLLOW_UP':
        return { color: 'orange', text: '下次跟進' };
      default:
        return { color: 'default', text: status };
    }
  };

  // 定義表格列
  const columns: ColumnsType<FollowUpRecord> = [
    {
      title: '跟進時間',
      dataIndex: 'followUpTime',
      key: 'followUpTime',
      width: 150,
      render: (text: string) => (
        <span className="text-gray-900 text-sm">{text || '--'}</span>
      ),
    },
    {
      title: '跟進人',
      dataIndex: 'followUpPerson',
      key: 'followUpPerson',
      width: 120,
      render: (text: string) => (
        <span className="text-gray-900 text-sm">{text || '--'}</span>
      ),
    },
    {
      title: '跟進狀態',
      dataIndex: 'followUpStatus',
      key: 'followUpStatus',
      width: 100,
      align: 'center',
      render: (status: string) => {
        const config = getStatusConfig(status);
        return (
          <Tag color={config.color} className="text-xs">
            {config.text}
          </Tag>
        );
      },
    },
    {
      title: '下次跟進時間',
      dataIndex: 'nextFollowUpTime',
      key: 'nextFollowUpTime',
      width: 150,
      render: (text: string) => (
        <span className="text-gray-600 text-sm">{text || '--'}</span>
      ),
    },
    {
      title: '跟進詳情',
      dataIndex: 'followUpDetail',
      key: 'followUpDetail',
      ellipsis: true,
      render: (text: string) => (
        <Tooltip placement="topLeft" title={text}>
          <span className="text-gray-900 text-sm">{text || '--'}</span>
        </Tooltip>
      ),
    },
  ];

  return <CommTable dataSource={list} columns={columns} loading={loading} />;
};

export default HistoryTable;
