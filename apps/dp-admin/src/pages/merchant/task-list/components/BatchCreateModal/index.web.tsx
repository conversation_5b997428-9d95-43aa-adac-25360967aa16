/* eslint-disable @typescript-eslint/no-explicit-any */
import { DownloadOutlined, UploadOutlined } from '@ant-design/icons';
import type { UploadProps } from 'antd';
import { Button, Form, Input, message, Modal, Select, Upload } from 'antd';
import React, { useState } from 'react';

import type {
  BatchCreateModalProps,
  BatchCreateTaskFormData,
} from '../../types';

const { Option } = Select;
const { TextArea } = Input;

/**
 * 批量创建任务模态框组件
 *
 * 支持手动创建和Excel批量导入两种方式
 */
const BatchCreateModal: React.FC<BatchCreateModalProps> = ({
  visible,
  confirmLoading,
  assigneeOptions,
  onCancel,
  onSubmit,
}) => {
  const [form] = Form.useForm();
  const [createMode, setCreateMode] = useState<'manual' | 'upload'>('manual');
  const [uploadedData, setUploadedData] = useState<BatchCreateTaskFormData[]>(
    []
  );

  // 处理表单提交
  const handleSubmit = async () => {
    if (createMode === 'manual') {
      try {
        const values = await form.validateFields();
        const assignee = assigneeOptions.find(
          (a) => a.id === values.assigneeId
        );

        onSubmit([
          {
            merchantId: values.merchantId,
            assigneeId: values.assigneeId,
            assigneeName: assignee?.name || '',
            priority: values.priority,
            taskName: values.taskName,
            taskDescription: values.taskDescription,
          },
        ]);
      } catch (error) {
        console.error('表单验证失败:', error);
      }
    } else {
      if (uploadedData.length === 0) {
        message.error('請先上傳任務數據');
        return;
      }
      onSubmit(uploadedData);
    }
  };

  // 处理取消
  const handleCancel = () => {
    form.resetFields();
    setCreateMode('manual');
    setUploadedData([]);
    onCancel();
  };

  // 下载模板
  const handleDownloadTemplate = () => {
    // 创建模板数据
    const templateData = [
      ['商戶ID', '任務分配人ID', '優先級', '任務名稱', '任務說明'],
      [
        'MERCHANT001',
        'USER001',
        'HIGH',
        '商戶資料審核',
        '審核商戶提交的基本資料',
      ],
      [
        'MERCHANT002',
        'USER002',
        'MEDIUM',
        '跟進商戶進度',
        '跟進商戶入駐進度並提供協助',
      ],
    ];

    // 创建CSV内容
    const csvContent = templateData.map((row) => row.join(',')).join('\n');
    const blob = new Blob(['\uFEFF' + csvContent], {
      type: 'text/csv;charset=utf-8;',
    });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', '批量創建任務模板.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // 文件上传配置
  const uploadProps: UploadProps = {
    name: 'file',
    accept: '.csv,.xlsx,.xls',
    beforeUpload: (file) => {
      // 这里应该解析文件内容
      // 实际项目中需要使用 xlsx 或其他库来解析 Excel 文件
      console.log('上传文件:', file);

      // 模拟解析结果
      const mockData: BatchCreateTaskFormData[] = [
        {
          merchantId: 'MERCHANT001',
          assigneeId: 'USER001',
          assigneeName: '張三',
          priority: 'HIGH',
          taskName: '商戶資料審核',
          taskDescription: '審核商戶提交的基本資料',
        },
        {
          merchantId: 'MERCHANT002',
          assigneeId: 'USER002',
          assigneeName: '李四',
          priority: 'MEDIUM',
          taskName: '跟進商戶進度',
          taskDescription: '跟進商戶入駐進度並提供協助',
        },
      ];

      setUploadedData(mockData);
      message.success(`成功解析 ${mockData.length} 條任務數據`);

      return false; // 阻止自动上传
    },
    onRemove: () => {
      setUploadedData([]);
    },
  };

  // 渲染跟进人选项
  const renderAssigneeOption = (option: any) => {
    const prefix = option.isCurrentUser ? '(我) ' : '';
    const emailPrefix = option.email.split('@')[0];
    return `${prefix}${option.name}+${emailPrefix}`;
  };

  return (
    <Modal
      title="批量創建任務"
      open={visible}
      onOk={handleSubmit}
      onCancel={handleCancel}
      confirmLoading={confirmLoading}
      okText="確認創建"
      cancelText="取消"
      width={600}
      destroyOnHidden
      className="batch-create-modal"
    >
      <div className="space-y-4">
        {/* 创建模式选择 */}
        <div className="flex gap-4 p-3 bg-gray-50 rounded">
          <Button
            type={createMode === 'manual' ? 'primary' : 'default'}
            onClick={() => setCreateMode('manual')}
          >
            手動創建
          </Button>
          <Button
            type={createMode === 'upload' ? 'primary' : 'default'}
            onClick={() => setCreateMode('upload')}
          >
            批量上傳
          </Button>
        </div>

        {/* 手动创建模式 */}
        {createMode === 'manual' && (
          <Form form={form} layout="vertical" autoComplete="off">
            <Form.Item
              label="商戶ID"
              name="merchantId"
              rules={[{ required: true, message: '請輸入商戶ID' }]}
            >
              <Input placeholder="請輸入商戶ID" />
            </Form.Item>

            <Form.Item
              label="任務分配人"
              name="assigneeId"
              rules={[{ required: true, message: '請選擇任務分配人' }]}
            >
              <Select placeholder="請選擇任務分配人" optionLabelProp="label">
                {assigneeOptions.map((option) => {
                  const label = renderAssigneeOption(option);
                  return (
                    <Option key={option.id} value={option.id} label={label}>
                      {label}
                    </Option>
                  );
                })}
              </Select>
            </Form.Item>

            <Form.Item
              label="優先級"
              name="priority"
              rules={[{ required: true, message: '請選擇優先級' }]}
            >
              <Select placeholder="請選擇優先級">
                <Option value="LOW">低</Option>
                <Option value="MEDIUM">中</Option>
                <Option value="HIGH">高</Option>
                <Option value="URGENT">緊急</Option>
              </Select>
            </Form.Item>

            <Form.Item
              label="任務名稱"
              name="taskName"
              rules={[{ required: true, message: '請輸入任務名稱' }]}
            >
              <Input placeholder="請輸入任務名稱" maxLength={100} />
            </Form.Item>

            <Form.Item
              label="任務說明"
              name="taskDescription"
              rules={[{ required: true, message: '請輸入任務說明' }]}
            >
              <TextArea
                placeholder="請輸入任務說明"
                rows={3}
                maxLength={500}
                showCount
              />
            </Form.Item>
          </Form>
        )}

        {/* 批量上传模式 */}
        {createMode === 'upload' && (
          <div className="space-y-4">
            {/* 下载模板 */}
            <div className="flex justify-between items-center p-3 bg-blue-50 border border-blue-200 rounded">
              <span className="text-sm text-blue-800">
                請先下載模板，填寫任務數據後上傳
              </span>
              <Button
                type="link"
                icon={<DownloadOutlined />}
                onClick={handleDownloadTemplate}
              >
                下載模板
              </Button>
            </div>

            {/* 文件上传 */}
            <Upload {...uploadProps}>
              <Button icon={<UploadOutlined />}>選擇文件上傳</Button>
            </Upload>

            {/* 上传结果预览 */}
            {uploadedData.length > 0 && (
              <div className="p-3 bg-green-50 border border-green-200 rounded">
                <p className="text-sm text-green-800 mb-2">
                  成功解析 {uploadedData.length} 條任務數據：
                </p>
                <ul className="text-xs text-green-700 space-y-1">
                  {uploadedData.slice(0, 3).map((item, index) => (
                    <li key={index}>
                      {item.merchantId} - {item.taskName} ({item.assigneeName})
                    </li>
                  ))}
                  {uploadedData.length > 3 && (
                    <li>... 還有 {uploadedData.length - 3} 條數據</li>
                  )}
                </ul>
              </div>
            )}
          </div>
        )}
      </div>
    </Modal>
  );
};

export default BatchCreateModal;
