import type { CascaderProps, GetProp } from 'antd';
import { message } from 'antd';
import { useCallback, useEffect, useState } from 'react';

type DefaultOptionType = GetProp<CascaderProps, 'options'>[number];

import type {
  AssigneeOption,
  BatchAssignFormData,
  BatchCreateTaskFormData,
  SearchFormData,
  TaskData,
  TaskStatusOption,
  TaskTypeOption,
} from '../types';
import {
  batchAssignTasks,
  batchCreateTasks,
  getAssigneeOptions,
  getTaskList,
  getTaskStatusOptions,
  getTaskTypeOptions,
} from './api';

/**
 * 任务列表管理 Hook
 *
 * 提供任务列表页面所需的所有状态管理和业务逻辑
 * 包括数据获取、表单处理、分页、批量操作等功能
 */
export const useTaskList = () => {
  // 基础状态
  const [loading, setLoading] = useState<boolean>(false);
  const [data, setData] = useState<TaskData[]>([]);
  const [total, setTotal] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(20);

  // 选项数据
  const [assigneeOptions, setAssigneeOptions] = useState<AssigneeOption[]>([]);
  const [taskStatusOptions, setTaskStatusOptions] = useState<
    TaskStatusOption[]
  >([]);
  const [taskTypeOptions, setTaskTypeOptions] = useState<TaskTypeOption[]>([]);

  // 搜索表单数据
  const [formData, setFormData] = useState<SearchFormData>({
    assignee: 'ALL',
    taskStatus: [],
    taskType: [],
    followUpTime: null,
    createTime: null,
    updateTime: null,
    merchantInfo: '',
    city: [],
  });

  // 选中的行
  const [selectedRowKeys, setSelectedRowKeys] = useState<(string | number)[]>(
    []
  );
  const [selectedRows, setSelectedRows] = useState<TaskData[]>([]);

  // 模态框状态
  const [batchAssignVisible, setBatchAssignVisible] = useState<boolean>(false);
  const [batchCreateVisible, setBatchCreateVisible] = useState<boolean>(false);
  const [batchLoading, setBatchLoading] = useState<boolean>(false);
  const [taskHandlerVisible, setTaskHandlerVisible] = useState<boolean>(false);

  /**
   * 获取任务列表
   */
  const fetchTaskList = useCallback(async (): Promise<void> => {
    setLoading(true);
    try {
      const [success, result] = await getTaskList({
        ...formData,
        pageNum: currentPage,
        pageSize: pageSize,
      });

      if (success && result && typeof result === 'object' && 'list' in result) {
        setData(result.list);
        setTotal(result.total);
      } else {
        message.error('获取任务列表失败');
        setData([]);
        setTotal(0);
      }
    } catch (error) {
      console.error('获取任务列表出错:', error);
      message.error('获取任务列表出错');
    } finally {
      setLoading(false);
    }
  }, [formData, currentPage, pageSize]);

  /**
   * 获取选项数据
   */
  const fetchOptions = useCallback(async (): Promise<void> => {
    try {
      const [
        [assigneeSuccess, assigneeResult],
        [statusSuccess, statusResult],
        [typeSuccess, typeResult],
      ] = await Promise.all([
        getAssigneeOptions(),
        getTaskStatusOptions(),
        getTaskTypeOptions(),
      ]);

      if (assigneeSuccess && Array.isArray(assigneeResult)) {
        setAssigneeOptions(assigneeResult);
      }
      if (statusSuccess && Array.isArray(statusResult)) {
        setTaskStatusOptions(statusResult);
      }
      if (typeSuccess && Array.isArray(typeResult)) {
        setTaskTypeOptions(typeResult);
      }
    } catch (error) {
      console.error('获取选项数据出错:', error);
    }
  }, []);

  /**
   * 初始化數據
   */
  useEffect(() => {
    fetchOptions();
  }, [fetchOptions]);

  useEffect(() => {
    fetchTaskList();
  }, [fetchTaskList]);

  /**
   * 處理表單數據變化
   */
  const handleFormDataChange = useCallback(
    <K extends keyof SearchFormData>(field: K, value: SearchFormData[K]) => {
      setFormData((prev) => ({
        ...prev,
        [field]: value,
      }));
    },
    []
  );

  /**
   * 處理搜索
   */
  const handleSearch = useCallback((): void => {
    setCurrentPage(1);
    fetchTaskList();
  }, [fetchTaskList]);

  /**
   * 處理重置
   */
  const handleReset = useCallback((): void => {
    setFormData({
      assignee: 'ALL',
      taskStatus: [],
      taskType: [],
      followUpTime: null,
      createTime: null,
      updateTime: null,
      merchantInfo: '',
      city: [],
    });
    setCurrentPage(1);
  }, []);

  /**
   * 處理分頁變化
   */
  const handlePageChange = useCallback(
    (page: number, size?: number): void => {
      setCurrentPage(page);
      if (size && size !== pageSize) {
        setPageSize(size);
      }
    },
    [pageSize]
  );

  /**
   * 處理行選擇變化
   */
  const handleRowSelectionChange = useCallback(
    (keys: (string | number)[], rows: TaskData[]): void => {
      setSelectedRowKeys(keys);
      setSelectedRows(rows);
    },
    []
  );

  // 处理批量分配
  const handleBatchAssign = useCallback(() => {
    if (selectedRowKeys.length === 0) {
      message.warning('請先選擇要分配的任務');
      return;
    }
    setBatchAssignVisible(true);
  }, [selectedRowKeys]);

  // 处理批量创建
  const handleBatchCreate = useCallback(() => {
    setBatchCreateVisible(true);
  }, []);

  // 提交批量分配
  const handleBatchAssignSubmit = useCallback(
    async (data: BatchAssignFormData) => {
      setBatchLoading(true);
      try {
        const [success] = await batchAssignTasks(data);
        if (success) {
          message.success('批量分配成功');
          setBatchAssignVisible(false);
          setSelectedRowKeys([]);
          setSelectedRows([]);
          fetchTaskList();
        } else {
          message.error('批量分配失败');
        }
      } catch (error) {
        console.error('批量分配出错:', error);
        message.error('批量分配出错');
      } finally {
        setBatchLoading(false);
      }
    },
    [fetchTaskList]
  );

  // 提交批量创建
  const handleBatchCreateSubmit = useCallback(
    async (data: BatchCreateTaskFormData[]) => {
      setBatchLoading(true);
      try {
        const [success] = await batchCreateTasks(data);
        if (success) {
          message.success(`成功創建 ${data.length} 個任務`);
          setBatchCreateVisible(false);
          fetchTaskList();
        } else {
          message.error('批量創建失败');
        }
      } catch (error) {
        console.error('批量創建出错:', error);
        message.error('批量創建出错');
      } finally {
        setBatchLoading(false);
      }
    },
    [fetchTaskList]
  );

  // 处理任务详情查看
  const handleTaskDetail = useCallback((taskId: string | number) => {
    // 这里可以跳转到任务详情页面
    console.log('查看任务详情:', taskId);
    message.info(`查看任務詳情: ${taskId}`);
  }, []);

  const handleCityFilter = useCallback(
    (inputValue: string, path: DefaultOptionType[]) =>
      path.some(
        (option) =>
          (option.label as string)
            .toLowerCase()
            .indexOf(inputValue.toLowerCase()) > -1
      ),
    []
  );
  /**
   * 處理任務
   */
  const handleTaskHandler = useCallback((_taskId: string | number): void => {
    // TODO: 實現任務處理邏輯
    setTaskHandlerVisible(true);
  }, []);

  /**
   * 分配任務
   */
  const handleTaskAssignee = useCallback((taskId: string | number): void => {
    // TODO: 實現任務分配邏輯
    setTaskHandlerVisible(true);
    console.log('分配任務:', taskId);
    message.info(`分配任務: ${taskId}`);
  }, []);

  /**
   * 查看跟進歷史
   */
  const handleFollowUpHistory = useCallback((taskId: string | number): void => {
    // TODO: 實現跟進歷史查看邏輯
    console.log('查看跟進歷史:', taskId);
    message.info(`查看跟進歷史: ${taskId}`);
  }, []);

  return {
    // 基础状态
    loading,
    data,
    total,
    currentPage,
    pageSize,

    // 表单数据
    formData,

    // 选项数据
    assigneeOptions,
    taskStatusOptions,
    taskTypeOptions,

    // 选择状态
    selectedRowKeys,
    selectedRows,

    // 模态框状态
    batchAssignVisible,
    batchCreateVisible,
    batchLoading,

    // 处理任务弹窗状态
    taskHandlerVisible,

    // 事件处理
    handleFormDataChange,
    handleSearch,
    handleReset,
    handlePageChange,
    handleRowSelectionChange,
    handleBatchAssign,
    handleBatchCreate,
    handleBatchAssignSubmit,
    handleBatchCreateSubmit,
    handleTaskDetail,
    handleTaskHandler,
    handleTaskAssignee,
    handleFollowUpHistory,
    handleCityFilter,
    // 模态框控制
    setBatchAssignVisible,
    setBatchCreateVisible,
    // 处理任务弹窗控制
    setTaskHandlerVisible,
  };
};
