import type {
  AssigneeOption,
  BatchAssignFormData,
  BatchCreateTaskFormData,
  TaskListResponse,
  TaskQueryParams,
  TaskStatusOption,
  TaskTypeOption,
} from '../types';

/**
 * 获取任务列表
 * @param params 查询参数
 * @returns [success, data] 成功状态和数据
 */
export const getTaskList = async (
  params: TaskQueryParams
): Promise<[boolean, TaskListResponse | Error]> => {
  try {
    // 模拟API调用
    // 实际项目中应该调用真实的API
    // const data = await request.post('/admin-api/merchant/tasks/list', params);

    console.log('查询参数:', params);

    // 模拟数据
    const mockData: TaskListResponse = {
      list: [
        {
          id: '3838438',
          taskType: '待審核',
          taskStatus: 'PENDING',
          nextFollowUpTime: '2025-03-22 13:20',
          createdTime: '2025-03-22 13:20',
          updatedTime: '2025-03-22 13:20',
          merchantGoogleId: 'GoogleID',
          merchantId: '3838438',
          merchantTaxId: '94943438',
          merchantFoodId: '2',
          merchantName: '新東方大茶館 (台北站店)',
          assignee: '楊名 (Chara Yang)',
          assigneeId: 'USER001',
          priority: 'HIGH',
          taskName: '商戶資料審核',
          taskDescription: '審核商戶提交的基本資料和證件',
          createdBy: '系統自動',
          updatedBy: '楊名',
        },
        {
          id: '3838439',
          taskType: '跟進中',
          taskStatus: 'IN_PROGRESS',
          nextFollowUpTime: '2025-03-22 13:20',
          createdTime: '2025-03-22 13:20',
          updatedTime: '2025-03-22 13:20',
          merchantGoogleId: 'GoogleID2',
          merchantId: '3838439',
          merchantTaxId: '94943439',
          merchantFoodId: '3',
          merchantName: '台北小吃店',
          assignee: '李四 (Li Si)',
          assigneeId: 'USER002',
          priority: 'MEDIUM',
          taskName: '商戶進度跟進',
          taskDescription: '跟進商戶入駐進度並提供協助',
          createdBy: '張三',
          updatedBy: '李四',
        },
      ],
      total: 2,
      pageNum: params.pageNum,
      pageSize: params.pageSize,
    };

    return [true, mockData];
  } catch (error) {
    console.error('获取任务列表失败:', error);
    return [false, error as Error];
  }
};

/**
 * 获取跟进人选项列表
 * @returns [success, data] 成功状态和数据
 */
export const getAssigneeOptions = async (): Promise<
  [boolean, AssigneeOption[] | Error]
> => {
  try {
    // 模拟API调用
    // const data = await request.get('/admin-api/users/assignees');

    const mockData: AssigneeOption[] = [
      {
        id: 'USER001',
        name: '楊名',
        email: '<EMAIL>',
        isCurrentUser: true,
      },
      {
        id: 'USER002',
        name: '李四',
        email: '<EMAIL>',
        isCurrentUser: false,
      },
      {
        id: 'USER003',
        name: '王五',
        email: '<EMAIL>',
        isCurrentUser: false,
      },
    ];

    return [true, mockData];
  } catch (error) {
    console.error('获取跟进人列表失败:', error);
    return [false, error as Error];
  }
};

/**
 * 获取任务状态选项
 * @returns [success, data] 成功状态和数据
 */
export const getTaskStatusOptions = async (): Promise<
  [boolean, TaskStatusOption[] | Error]
> => {
  try {
    const mockData: TaskStatusOption[] = [
      { value: 'PENDING', label: '待處理', color: '#fa8c16' },
      { value: 'IN_PROGRESS', label: '進行中', color: '#1890ff' },
      { value: 'COMPLETED', label: '已完成', color: '#52c41a' },
      { value: 'CANCELLED', label: '已取消', color: '#ff4d4f' },
    ];

    return [true, mockData];
  } catch (error) {
    console.error('获取任务状态选项失败:', error);
    return [false, error as Error];
  }
};

/**
 * 获取任务类型选项
 * @returns [success, data] 成功状态和数据
 */
export const getTaskTypeOptions = async (): Promise<
  [boolean, TaskTypeOption[] | Error]
> => {
  try {
    const mockData: TaskTypeOption[] = [
      { value: 'AUDIT', label: '待審核' },
      { value: 'FOLLOW_UP', label: '跟進中' },
      { value: 'REVIEW', label: '已審核' },
      { value: 'REJECTED', label: '已駁回' },
      { value: 'CUSTOM', label: '自定義' },
    ];

    return [true, mockData];
  } catch (error) {
    console.error('获取任务类型选项失败:', error);
    return [false, error as Error];
  }
};

/**
 * 批量分配任务
 * @param data 分配数据
 * @returns [success, result] 成功状态和结果
 */
export const batchAssignTasks = async (
  data: BatchAssignFormData
): Promise<[boolean, unknown | Error]> => {
  try {
    // 模拟API调用
    // const result = await request.post('/admin-api/merchant/tasks/batch-assign', data);

    console.log('批量分配任务:', data);

    // 模拟延迟
    await new Promise((resolve) => setTimeout(resolve, 1000));

    return [true, { success: true, message: '批量分配成功' }];
  } catch (error) {
    console.error('批量分配任务失败:', error);
    return [false, error as Error];
  }
};

/**
 * 批量创建任务
 * @param data 任务数据列表
 * @returns [success, result] 成功状态和结果
 */
export const batchCreateTasks = async (
  data: BatchCreateTaskFormData[]
): Promise<[boolean, unknown | Error]> => {
  try {
    // 模拟API调用
    // const result = await request.post('/admin-api/merchant/tasks/batch-create', data);

    console.log('批量创建任务:', data);

    // 模拟延迟
    await new Promise((resolve) => setTimeout(resolve, 1000));

    return [true, { success: true, message: `成功创建 ${data.length} 个任务` }];
  } catch (error) {
    console.error('批量创建任务失败:', error);
    return [false, error as Error];
  }
};

/**
 * 获取任务详情
 * @param taskId 任务ID
 * @returns [success, data] 成功状态和数据
 */
export const getTaskDetail = async (
  taskId: string | number
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
): Promise<[boolean, any | Error]> => {
  try {
    // 模拟API调用
    // const data = await request.get(`/admin-api/merchant/tasks/${taskId}`);

    console.log('获取任务详情:', taskId);

    const mockData = {
      id: taskId,
      taskType: '待審核',
      taskStatus: 'PENDING',
      // 更多详情数据...
    };

    return [true, mockData];
  } catch (error) {
    console.error('获取任务详情失败:', error);
    return [false, error as Error];
  }
};
