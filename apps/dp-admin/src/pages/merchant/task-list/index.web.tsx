import React from 'react';

import BatchAssignModal from './components/BatchAssignModal/index.web';
import BatchCreateModal from './components/BatchCreateModal/index.web';
import FollowUpHistoryModal from './components/FollowUpHistoryModal/index.web';
import SearchForm from './components/SearchForm/index.web';
import TaskAssigneeModal from './components/TaskAssigneeModal/index.web';
import TaskTable from './components/TaskTable/index.web';
import { useTaskList } from './services/hooks';

/**
 * 商戶任務列表頁面組件
 *
 * 功能特性：
 * - 任務搜索與篩選
 * - 任務查看與詳情
 * - 批量分配任務
 * - 批量創建任務
 * - 跟進歷史查看
 * - 響應式設計，適配移動端、平板和PC端
 *
 * @returns {React.FC} 任務列表頁面組件
 */
const TaskListPage: React.FC = () => {
  const {
    // 基礎狀態
    loading,
    data,
    total,
    currentPage,
    pageSize,

    // 表單數據
    formData,

    // 選項數據
    assigneeOptions,
    taskStatusOptions,
    taskTypeOptions,

    // 選擇狀態
    selectedRowKeys,
    selectedRows,

    // 模態框狀態
    batchAssignVisible,
    batchCreateVisible,
    batchLoading,

    // 處理任務彈窗狀態
    taskHandlerVisible,

    // 事件處理函數
    handleFormDataChange,
    handleSearch,
    handleReset,
    handlePageChange,
    handleRowSelectionChange,
    handleBatchAssign,
    handleBatchCreate,
    handleBatchAssignSubmit,
    handleBatchCreateSubmit,
    handleTaskDetail,
    handleTaskHandler,
    handleTaskAssignee,
    handleFollowUpHistory,
    handleCityFilter,

    // 模態框控制函數
    setBatchAssignVisible,
    setBatchCreateVisible,

    // 處理任務彈窗控制函數
    setTaskHandlerVisible,
  } = useTaskList();

  return (
    <div className="bg-white p-4 sm:p-6 md:p-8 rounded-lg shadow-sm">
      {/* 頁面容器 */}
      <div className="mx-auto p-4 space-y-4">
        {/* 頁面標題區域 */}
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <h1 className="text-xl font-semibold text-gray-900 mb-2">
            商戶任務管理
          </h1>
          <p className="text-sm text-gray-600">
            管理商戶相關任務，支持搜索篩選、批量分配和批量創建功能
          </p>
        </div>

        {/* 搜索表單區域 */}
        <SearchForm
          formData={formData}
          assigneeOptions={assigneeOptions}
          taskStatusOptions={taskStatusOptions}
          taskTypeOptions={taskTypeOptions}
          onFormDataChange={handleFormDataChange}
          onSearch={handleSearch}
          onReset={handleReset}
          onBatchAssign={handleBatchAssign}
          onBatchCreate={handleBatchCreate}
          onCityFilter={handleCityFilter}
        />

        {/* 任務表格區域 */}
        <TaskTable
          loading={loading}
          data={data}
          selectedRowKeys={selectedRowKeys}
          currentPage={currentPage}
          pageSize={pageSize}
          total={total}
          onPageChange={handlePageChange}
          onRowSelectionChange={handleRowSelectionChange}
          onTaskDetail={handleTaskDetail}
          onTaskHandler={handleTaskHandler}
          onTaskAssignee={handleTaskAssignee}
          onFollowUpHistory={handleFollowUpHistory}
        />

        {/* 批量分配模態框 */}
        <BatchAssignModal
          visible={batchAssignVisible}
          confirmLoading={batchLoading}
          selectedTasks={selectedRows}
          assigneeOptions={assigneeOptions}
          onCancel={() => setBatchAssignVisible(false)}
          onSubmit={handleBatchAssignSubmit}
        />

        {/* 批量創建模態框 */}
        <BatchCreateModal
          visible={batchCreateVisible}
          confirmLoading={batchLoading}
          assigneeOptions={assigneeOptions}
          onCancel={() => setBatchCreateVisible(false)}
          onSubmit={handleBatchCreateSubmit}
        />

        {/* 任務分配模態框 */}
        <TaskAssigneeModal
          visible={taskHandlerVisible}
          onCancel={() => setTaskHandlerVisible(false)}
          onSave={() => {
            // TODO: 實現任務分配保存邏輯
            setTaskHandlerVisible(false);
          }}
          historyList={[]}
        />

        {/* 跟進歷史模態框 */}
        <FollowUpHistoryModal />
      </div>
    </div>
  );
};

export default TaskListPage;
