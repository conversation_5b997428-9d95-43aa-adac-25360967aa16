# 商户任务管理页面

## 功能概述

商户任务管理页面提供了完整的任务管理功能，包括任务搜索、查看、批量分配和批量创建等操作。页面采用响应式设计，支持移动端、平板和PC端的良好展示效果。

## 主要功能

### 1. 任务搜索与筛选

- **跟进人筛选**：支持按跟进人筛选，显示格式为「姓名+邮箱前缀」或「(我) 姓名+邮箱前缀」
- **无跟进人筛选**：支持筛选没有分配跟进人的任务
- **任务状态**：多选筛选（待处理、进行中、已完成、已取消）
- **任务类型**：多选筛选（待审核、跟进中、已审核、已驳回、自定义）
- **下次跟进时间筛选**：支持按下次跟进时间进行日期范围筛选
- **商户信息筛选**：
  - 商户（支持GoogleID/ID/统编/食品编号/名称搜索）

### 2. 任务列表展示

- **响应式表格**：根据屏幕尺寸自动调整列的显示
- **移动端适配**：在小屏幕设备上隐藏次要信息，保持核心信息可见
- **状态标签**：使用颜色区分不同的任务状态和优先级
- **分页功能**：支持自定义每页显示数量和快速跳转

### 3. 批量操作

#### 批量分配任务
- 选择多个任务进行批量分配
- 选择目标跟进人
- 显示分配任务列表预览
- 确认后执行批量分配操作

#### 批量创建任务
- **手动创建**：单个任务手动填写表单创建
- **批量上传**：通过Excel文件批量导入任务
- **模板下载**：提供标准的Excel模板下载
- **数据验证**：上传文件后进行数据格式验证

### 4. 任务操作

- **查看详情**：点击查看任务的详细信息
- **编辑任务**：修改任务信息（预留功能）
- **删除任务**：删除不需要的任务（预留功能）

## 技术特性

### 响应式设计

- **移动端（< 768px）**：
  - 隐藏次要列信息
  - 商户信息合并显示
  - 简化操作按钮

- **平板端（768px - 1024px）**：
  - 显示主要列信息
  - 保持良好的可读性

- **PC端（> 1024px）**：
  - 显示完整的列信息
  - 提供最佳的操作体验

### 状态管理

- 使用自定义Hook `useTaskList` 管理页面状态
- 统一的API调用和错误处理
- 表单状态与URL参数同步（可扩展）

### 组件架构

```
task-list/
├── components/           # 组件目录
│   ├── SearchForm/      # 搜索表单组件
│   ├── TaskTable/       # 任务表格组件
│   ├── StatusTag/       # 状态标签组件
│   ├── PriorityTag/     # 优先级标签组件
│   ├── ActionButtons/   # 操作按钮组件
│   ├── BatchAssignModal/    # 批量分配模态框
│   └── BatchCreateModal/    # 批量创建模态框
├── services/            # 服务目录
│   ├── api.ts          # API接口定义
│   └── hooks.ts        # 自定义Hooks
├── types.ts            # 类型定义
├── index.web.tsx       # 主页面组件
└── README.md           # 说明文档
```

## 使用说明

### 访问路径

页面路径：`/merchant/task-list`

### 权限要求

需要登录并具有商户管理权限才能访问此页面。

### 数据格式

#### 任务数据结构
```typescript
interface TaskData {
  id: string | number;
  taskType: string;
  taskStatus: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
  nextFollowUpTime: string;
  createdTime: string;
  updatedTime: string;
  merchantGoogleId: string;
  merchantId: string;
  merchantTaxId: string;
  merchantFoodId: string;
  merchantName: string;
  assignee: string;
  assigneeId: string;
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  taskName: string;
  taskDescription: string;
  createdBy: string;
  updatedBy: string;
}
```

#### 批量创建Excel模板格式
| 商戶ID | 任務分配人ID | 優先級 | 任務名稱 | 任務說明 |
|--------|-------------|--------|----------|----------|
| MERCHANT001 | USER001 | HIGH | 商戶資料審核 | 審核商戶提交的基本資料 |

## 开发注意事项

1. **API接口**：当前使用模拟数据，实际开发时需要替换为真实的API调用
2. **文件上传**：批量创建功能需要集成Excel文件解析库（如xlsx）
3. **权限控制**：根据用户角色控制功能的可见性和可操作性
4. **错误处理**：完善的错误提示和异常处理机制
5. **性能优化**：大数据量时考虑虚拟滚动和分页优化

## 扩展功能

- 任务状态流转管理
- 任务提醒和通知
- 任务统计和报表
- 任务模板管理
- 工作流集成
