/* eslint-disable @typescript-eslint/no-explicit-any */
/**
 * 任务数据类型定义
 */
import type { CascaderProps, GetProp } from 'antd';
type DefaultOptionType = GetProp<CascaderProps, 'options'>[number];

export interface TaskData {
  id: string | number; // 任务ID
  taskType: string; // 任务类型
  taskStatus: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED'; // 任务状态
  nextFollowUpTime: string; // 下次跟进时间
  createdTime: string; // 生成时间
  updatedTime: string; // 更新时间
  merchantGoogleId: string; // 商户GoogleID
  merchantId: string; // 商户ID
  merchantTaxId: string; // 商户统编
  merchantFoodId: string; // 商户食品编号
  merchantName: string; // 商户名称
  assignee: string; // 跟进人
  assigneeId: string; // 跟进人ID
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT'; // 优先级
  taskName: string; // 任务名称
  taskDescription: string; // 任务说明
  createdBy: string; // 创建人
  updatedBy: string; // 更新人
}

/**
 * 跟进人选项
 */
export interface AssigneeOption {
  id: string;
  name: string;
  email: string;
  isCurrentUser: boolean; // 是否为当前用户
}

/**
 * 任务状态选项
 */
export interface TaskStatusOption {
  value: string;
  label: string;
  color: string;
}

/**
 * 任务类型选项
 */
export interface TaskTypeOption {
  value: string;
  label: string;
}

/**
 * 搜索表单数据
 */
export interface SearchFormData {
  assignee: string; // 跟进人
  taskStatus: string[]; // 任务状态（多选）
  taskType: string[]; // 任务类型（多选）
  followUpTime: [string, string] | null; // 下次跟进时间范围
  createTime: [string, string] | null; // 下次跟进时间范围
  updateTime: [string, string] | null; // 下次跟进时间范围
  merchantInfo: string; // 商户信息（GoogleID/ID/统编/食品编号/名称）
  city: string[]; // 城市
}

/**
 * 搜索表单组件属性
 */
export interface SearchFormProps {
  formData: SearchFormData;
  assigneeOptions: AssigneeOption[];
  taskStatusOptions: TaskStatusOption[];
  taskTypeOptions: TaskTypeOption[];
  onFormDataChange: (field: keyof SearchFormData, value: any) => void;
  onSearch: () => void;
  onReset: () => void;
  onBatchAssign: () => void;
  onBatchCreate: () => void;
  onCityFilter: (inputValue: string, path: DefaultOptionType[]) => boolean;
}

/**
 * 任务表格组件属性
 */
export interface TaskTableProps {
  loading: boolean;
  data: TaskData[];
  selectedRowKeys: (string | number)[];
  currentPage: number;
  pageSize: number;
  total: number;
  onPageChange: (page: number, pageSize?: number) => void;
  onRowSelectionChange: (
    selectedRowKeys: (string | number)[],
    selectedRows: TaskData[]
  ) => void;
  onTaskDetail: (id: string | number) => void;
  onTaskHandler: (id: string | number) => void;
  onTaskAssignee: (id: string | number) => void;
  onFollowUpHistory: (id: string | number) => void;
}

/**
 * 批量分配表单数据
 */
export interface BatchAssignFormData {
  assigneeId: string;
  assigneeName: string;
  taskIds: (string | number)[];
}

/**
 * 批量创建任务表单数据
 */
export interface BatchCreateTaskFormData {
  merchantId: string;
  assigneeId: string;
  assigneeName: string;
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  taskName: string;
  taskDescription: string;
}

/**
 * 批量创建任务组件属性
 */
export interface BatchCreateModalProps {
  visible: boolean;
  confirmLoading: boolean;
  assigneeOptions: AssigneeOption[];
  onCancel: () => void;
  onSubmit: (data: BatchCreateTaskFormData[]) => void;
}

/**
 * 批量分配组件属性
 */
export interface BatchAssignModalProps {
  visible: boolean;
  confirmLoading: boolean;
  selectedTasks: TaskData[];
  assigneeOptions: AssigneeOption[];
  onCancel: () => void;
  onSubmit: (data: BatchAssignFormData) => void;
}

/**
 * 查询参数
 */
export interface TaskQueryParams extends SearchFormData {
  pageNum: number;
  pageSize: number;
}

/**
 * API 响应数据
 */
export interface TaskListResponse {
  list: TaskData[];
  total: number;
  pageNum: number;
  pageSize: number;
}

/**
 * 操作按钮组件属性
 */
export interface ActionButtonsProps {
  record: TaskData;
  onDetail: (id: string | number) => void;
  onTaskHandler: (id: string | number) => void;
  onTaskAssignee: (id: string | number) => void;
  onFollowUpHistory: (id: string | number) => void;
}

/**
 * 跟进状态选项
 */
export interface FollowUpStatusOption {
  label: string;
  value: string;
}

/**
 * 跟进记录数据
 */
export interface FollowUpRecord {
  id: string | number;
  followUpTime: string; // 跟进时间
  followUpPerson: string; // 跟进人
  followUpStatus: string; // 跟进状态
  nextFollowUpTime?: string; // 下次跟进时间
  followUpDetail: string; // 跟进详情
  createdAt: string; // 创建时间
  createdBy: string; // 创建人
}

/**
 * 任务处理表单数据
 */
export interface TaskHandlerFormData {
  followUpStatus: string; // 跟进状态
  nextFollowUpTime?: string; // 下次跟进时间
  followUpDetail: string; // 跟进详情
}

/**
 * 任务处理弹窗组件属性
 */
export interface TaskHandlerModalProps {
  visible: boolean;
  confirmLoading?: boolean;
  taskId?: string | number;
  taskData?: TaskData;
  historyList: FollowUpRecord[];
  onCancel: () => void;
  onSave: (data: TaskHandlerFormData) => void;
}

/**
 * 跟进历史表格组件属性
 */
export interface HistoryTableProps {
  list: FollowUpRecord[];
  loading?: boolean;
}
