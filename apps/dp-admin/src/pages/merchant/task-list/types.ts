/**
 * 任務數據類型定義
 */
import type { CascaderProps, GetProp } from 'antd';

type DefaultOptionType = GetProp<CascaderProps, 'options'>[number];

/**
 * 任務狀態枚舉
 */
export enum TaskStatus {
  /** 待處理 */
  PENDING = 'PENDING',
  /** 進行中 */
  IN_PROGRESS = 'IN_PROGRESS',
  /** 已完成 */
  COMPLETED = 'COMPLETED',
  /** 已取消 */
  CANCELLED = 'CANCELLED',
}

/**
 * 任務類型枚舉
 */
export enum TaskType {
  /** 待審核 */
  AUDIT = 'AUDIT',
  /** 跟進中 */
  FOLLOW_UP = 'FOLLOW_UP',
  /** 已審核 */
  REVIEWED = 'REVIEWED',
  /** 已駁回 */
  REJECTED = 'REJECTED',
  /** 自定義 */
  CUSTOM = 'CUSTOM',
}

/**
 * 優先級枚舉
 */
export enum Priority {
  /** 低 */
  LOW = 'LOW',
  /** 中 */
  MEDIUM = 'MEDIUM',
  /** 高 */
  HIGH = 'HIGH',
  /** 緊急 */
  URGENT = 'URGENT',
}

/**
 * 任務數據接口
 */
export interface TaskData {
  /** 任務ID */
  id: string | number;
  /** 任務類型 */
  taskType: TaskType | string;
  /** 任務狀態 */
  taskStatus: TaskStatus;
  /** 下次跟進時間 */
  nextFollowUpTime: string;
  /** 創建時間 */
  createdTime: string;
  /** 更新時間 */
  updatedTime: string;
  /** 商戶GoogleID */
  merchantGoogleId: string;
  /** 商戶ID */
  merchantId: string;
  /** 商戶統編 */
  merchantTaxId: string;
  /** 商戶食品編號 */
  merchantFoodId: string;
  /** 商戶名稱 */
  merchantName: string;
  /** 跟進人 */
  assignee: string;
  /** 跟進人ID */
  assigneeId: string;
  /** 優先級 */
  priority: Priority;
  /** 任務名稱 */
  taskName: string;
  /** 任務說明 */
  taskDescription: string;
  /** 創建人 */
  createdBy: string;
  /** 更新人 */
  updatedBy: string;
}

/**
 * 跟進人選項接口
 */
export interface AssigneeOption {
  /** 用戶ID */
  id: string;
  /** 用戶姓名 */
  name: string;
  /** 用戶郵箱 */
  email: string;
  /** 是否為當前用戶 */
  isCurrentUser: boolean;
  /** 部門（可選） */
  department?: string;
  /** 職位（可選） */
  position?: string;
}

/**
 * 任務狀態選項接口
 */
export interface TaskStatusOption {
  /** 狀態值 */
  value: TaskStatus | string;
  /** 顯示標籤 */
  label: string;
  /** 狀態顏色 */
  color: string;
  /** 狀態描述（可選） */
  description?: string;
}

/**
 * 任務類型選項接口
 */
export interface TaskTypeOption {
  /** 類型值 */
  value: TaskType | string;
  /** 顯示標籤 */
  label: string;
  /** 類型圖標（可選） */
  icon?: string;
  /** 類型描述（可選） */
  description?: string;
}

/**
 * 搜索表單數據接口
 */
export interface SearchFormData {
  /** 跟進人 */
  assignee: string;
  /** 任務狀態（多選） */
  taskStatus: (TaskStatus | string)[];
  /** 任務類型（多選） */
  taskType: (TaskType | string)[];
  /** 下次跟進時間範圍 */
  followUpTime: [string, string] | null;
  /** 創建時間範圍 */
  createTime: [string, string] | null;
  /** 更新時間範圍 */
  updateTime: [string, string] | null;
  /** 商戶信息（GoogleID/ID/統編/食品編號/名稱） */
  merchantInfo: string;
  /** 城市列表 */
  city: string[];
}

/**
 * 搜索表單組件屬性接口
 */
export interface SearchFormProps {
  /** 表單數據 */
  formData: SearchFormData;
  /** 跟進人選項列表 */
  assigneeOptions: AssigneeOption[];
  /** 任務狀態選項列表 */
  taskStatusOptions: TaskStatusOption[];
  /** 任務類型選項列表 */
  taskTypeOptions: TaskTypeOption[];
  /** 表單數據變更回調 */
  onFormDataChange: <K extends keyof SearchFormData>(
    field: K,
    value: SearchFormData[K]
  ) => void;
  /** 搜索回調 */
  onSearch: () => void;
  /** 重置回調 */
  onReset: () => void;
  /** 批量分配回調 */
  onBatchAssign: () => void;
  /** 批量創建回調 */
  onBatchCreate: () => void;
  /** 城市篩選回調 */
  onCityFilter: (inputValue: string, path: DefaultOptionType[]) => boolean;
}

/**
 * 任務表格組件屬性接口
 */
export interface TaskTableProps {
  /** 加載狀態 */
  loading: boolean;
  /** 任務數據列表 */
  data: TaskData[];
  /** 選中行的鍵值 */
  selectedRowKeys: (string | number)[];
  /** 當前頁碼 */
  currentPage: number;
  /** 每頁條數 */
  pageSize: number;
  /** 總條數 */
  total: number;
  /** 頁碼變更回調 */
  onPageChange: (page: number, pageSize?: number) => void;
  /** 行選擇變更回調 */
  onRowSelectionChange: (
    selectedRowKeys: (string | number)[],
    selectedRows: TaskData[]
  ) => void;
  /** 查看任務詳情回調 */
  onTaskDetail: (id: string | number) => void;
  /** 任務處理回調 */
  onTaskHandler: (id: string | number) => void;
  /** 任務分配回調 */
  onTaskAssignee: (id: string | number) => void;
  /** 查看跟進歷史回調 */
  onFollowUpHistory: (id: string | number) => void;
}

/**
 * 批量分配表單數據接口
 */
export interface BatchAssignFormData {
  /** 跟進人ID */
  assigneeId: string;
  /** 跟進人姓名 */
  assigneeName: string;
  /** 任務ID列表 */
  taskIds: (string | number)[];
}

/**
 * 批量創建任務表單數據接口
 */
export interface BatchCreateTaskFormData {
  /** 商戶ID */
  merchantId: string;
  /** 跟進人ID */
  assigneeId: string;
  /** 跟進人姓名 */
  assigneeName: string;
  /** 優先級 */
  priority: Priority;
  /** 任務名稱 */
  taskName: string;
  /** 任務描述 */
  taskDescription: string;
}

/**
 * 批量創建任務組件屬性接口
 */
export interface BatchCreateModalProps {
  /** 是否可見 */
  visible: boolean;
  /** 確認加載狀態 */
  confirmLoading: boolean;
  /** 跟進人選項列表 */
  assigneeOptions: AssigneeOption[];
  /** 取消回調 */
  onCancel: () => void;
  /** 提交回調 */
  onSubmit: (data: BatchCreateTaskFormData[]) => void;
}

/**
 * 批量分配組件屬性接口
 */
export interface BatchAssignModalProps {
  /** 是否可見 */
  visible: boolean;
  /** 確認加載狀態 */
  confirmLoading: boolean;
  /** 選中的任務列表 */
  selectedTasks: TaskData[];
  /** 跟進人選項列表 */
  assigneeOptions: AssigneeOption[];
  /** 取消回調 */
  onCancel: () => void;
  /** 提交回調 */
  onSubmit: (data: BatchAssignFormData) => void;
}

/**
 * 查詢參數接口
 */
export interface TaskQueryParams extends SearchFormData {
  /** 頁碼 */
  pageNum: number;
  /** 每頁條數 */
  pageSize: number;
}

/**
 * API 響應數據接口
 */
export interface TaskListResponse {
  /** 任務列表 */
  list: TaskData[];
  /** 總條數 */
  total: number;
  /** 頁碼 */
  pageNum: number;
  /** 每頁條數 */
  pageSize: number;
}

/**
 * 操作按鈕組件屬性接口
 */
export interface ActionButtonsProps {
  /** 任務記錄 */
  record: TaskData;
  /** 查看詳情回調 */
  onDetail: (id: string | number) => void;
  /** 任務處理回調 */
  onTaskHandler: (id: string | number) => void;
  /** 任務分配回調 */
  onTaskAssignee: (id: string | number) => void;
  /** 查看跟進歷史回調 */
  onFollowUpHistory: (id: string | number) => void;
}

/**
 * 跟進狀態選項接口
 */
export interface FollowUpStatusOption {
  /** 顯示標籤 */
  label: string;
  /** 選項值 */
  value: string;
  /** 狀態描述（可選） */
  description?: string;
}

/**
 * 跟進記錄數據接口
 */
export interface FollowUpRecord {
  /** 記錄ID */
  id: string | number;
  /** 跟進時間 */
  followUpTime: string;
  /** 跟進人 */
  followUpPerson: string;
  /** 跟進狀態 */
  followUpStatus: string;
  /** 下次跟進時間（可選） */
  nextFollowUpTime?: string;
  /** 跟進詳情 */
  followUpDetail: string;
  /** 創建時間 */
  createdAt: string;
  /** 創建人 */
  createdBy: string;
}

/**
 * 任務處理表單數據接口
 */
export interface TaskHandlerFormData {
  /** 跟進狀態 */
  followUpStatus: string;
  /** 下次跟進時間（可選） */
  nextFollowUpTime?: string;
  /** 跟進詳情 */
  followUpDetail: string;
}

/**
 * 任務處理彈窗組件屬性接口
 */
export interface TaskHandlerModalProps {
  /** 是否可見 */
  visible: boolean;
  /** 確認加載狀態（可選） */
  confirmLoading?: boolean;
  /** 任務ID（可選） */
  taskId?: string | number;
  /** 任務數據（可選） */
  taskData?: TaskData;
  /** 跟進歷史列表 */
  historyList: FollowUpRecord[];
  /** 取消回調 */
  onCancel: () => void;
  /** 保存回調 */
  onSave: (data: TaskHandlerFormData) => void;
}

/**
 * 跟進歷史表格組件屬性接口
 */
export interface HistoryTableProps {
  /** 跟進記錄列表 */
  list: FollowUpRecord[];
  /** 加載狀態（可選） */
  loading?: boolean;
}
