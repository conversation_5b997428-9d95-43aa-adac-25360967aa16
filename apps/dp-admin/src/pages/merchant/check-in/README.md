# 入住審核頁面

## 頁面概述

入住審核頁面用於管理和審核商戶的入駐申請。運營人員可以在此頁面查看待審核的商戶信息，進行審核操作，並跟蹤審核狀態。

## 組件結構

```
/check-in
├── components/
│   ├── ActionButtons.web.tsx  # 操作按鈕組件
│   ├── MerchantTable.web.tsx  # 商戶表格組件
│   ├── SearchForm.web.tsx     # 搜索表單組件
│   └── StatusTag.web.tsx      # 狀態標籤組件
├── index.web.tsx              # 頁面主組件
├── types.ts                   # 類型定義
└── README.md                  # 本文檔
```

## 數據權限

- **查看權限**：有查看數據權限的人可見目錄和全量數據
- **操作權限**：無額外控制

## 頁面默認顯示

- 進入頁面默認篩選審核狀態為「待審核」
- 數據按照信息更新時間降序排列
- 注意：1個商戶可能多次提交審核

## 搜索與篩選功能

搜索表單提供以下篩選條件：

### 搜索項
- **商戶名稱/ID**：支持模糊搜索商戶名稱或精確搜索商戶ID
- **提交時間**：使用日期範圍選擇器篩選提交時間
- **商戶分類**：多選下拉框，支持搜索功能
- **審核狀態**：單選下拉框，包含全部、待審核步驟1、待審核步驟2、已拒絕等選項

### 操作按鈕
- **重置**：清空所有搜索條件
- **搜索**：根據設置的條件進行搜索

### 列表字段說明

| 字段名稱 | 說明 |
|---------|------|
| 審核類型 | 入駐審核 |
| 商戶ID | 平台商戶ID，非Google商戶ID |
| 商戶主圖 | 對應用戶端商戶列表圖片 |
| 商戶名稱 | 優先顯示商戶/BD填寫的名稱，若沒有，顯示Google接口取到的商戶name |
| 商戶地址 | 簡單地址，優先顯示商戶/BD填寫的地址，若沒有，顯示Google接口取到的商戶地址 |
| 商戶分類 | 顯示為「一級類目-二級類目-三級類目」 |
| 當前分配人 | 顯示為「姓名+郵箱前綴」 |
| 提交時間 | 商戶提交審核的時間 |
| 審核狀態 | 待審核、已駁回、已通過 |
| 更新時間 | 審核狀態最近一次更新時間，顯示格式：今年顯示為「3-15 13:42」；非今年顯示為「2024-3-15 12:32」 |
| 更新人 | 審核狀態最近一次更新的操作人，若為運營平台員工顯示為「姓名+郵箱前綴」，否則顯示為「--」 |
| 操作 | 查看詳情按鈕 |

### 狀態標籤說明

- **待審核**：橙色背景，表示審核中狀態
- **已駁回**：紅色背景，表示審核未通過
- **已通過**：綠色背景，表示審核已通過

## 技術實現

### 主要組件

#### 1. SearchForm 組件

搜索表單組件提供多種搜索條件，使用 Ant Design 的表單組件實現：

- **Input**：用於商戶名稱/ID搜索
- **DatePicker.RangePicker**：用於提交時間範圍選擇
- **Select (多選)**：用於商戶分類篩選，支持搜索功能
- **Select (單選)**：用於審核狀態篩選
- **Button**：重置和搜索按鈕

#### 2. MerchantTable 組件

商戶表格組件使用 Ant Design 的 Table 組件實現，提供以下功能：

- 數據展示
- 分頁控制
- 狀態標籤集成
- 操作按鈕集成

#### 3. StatusTag 組件

狀態標籤組件根據不同的審核狀態顯示不同樣式的標籤，使用 Tailwind CSS 實現樣式定制。

#### 4. ActionButtons 組件

操作按鈕組件提供查看詳情功能，點擊後可以查看商戶的詳細信息。

### 響應式設計

頁面使用 Tailwind CSS 實現響應式設計，確保在不同設備上都能提供良好的用戶體驗：

- **移動設備**：搜索條件垂直排列，每行一個條件
- **平板設備**：搜索條件每行兩個
- **桌面設備**：搜索條件每行四個，布局更加寬鬆

### 開發注意事項

1. 確保列表數據的實時性，特別是審核狀態的變更
2. 優化搜索和篩選功能的響應速度
3. 確保審核操作有適當的權限控制
4. 提供清晰的審核歷史記錄

## 未來改進

1. 添加批量操作功能
2. 實現數據導出功能
3. 添加高級搜索選項
4. 優化移動端體驗
5. 添加審核流程可視化展示
