/**
 * 商户列表响应
 */
export interface MerchantListResponse {
  applications: MerchantListItem[];
  total: number;
  pageNum: number;
  pageSize: number;
}

/**
 * 商户列表项
 */
export interface MerchantListItem {
  applicationId: number | string;
  merchantId: number;
  merchantName: string;
  merchantType: string;
  businessLicenseNo: string;
  businessLicenseImage: string;
  legalRepresentative: string;
  legalIdCardNo: string;
  legalPhone: string;
  businessOwner: string;
  businessOwnerIdCardNo: string;
  businessOwnerIdCardFront: string;
  businessOwnerIdCardBack: string;
  businessOwnerPhone: string;
  foodBusinessRegistrationNo: string;
  healthPermitNo: number;
  buildingUsagePermitNo: number;
  fireSafetyCertificateNo: number;
  publicLiabilityInsuranceNo: number;
  foodSafetyManagerCertificateNo: number;
  alcoholSalesPermitNo: number;
  environmentalPermitNo: number;
  address: string;
  description: string;
  applicationStatus: 'APPROVED' | 'REJECTED' | 'PENDING_REVIEW' | 'DRAFT';
  applicant: string;
  remark: string;
  reviewRecord: ReviewRecord;
  createdAt: string;
  createdBy: string;
  updatedAt: string;
  updatedBy: string;
  shopType: string;
  categories: categories[];
  merchantCreatedTime: string;
  merchantUpdatedTime: string;
  provinceName: string;
  cityName: string;
  districtName: string;
  shopName: string;
}

export interface categories {
  level: number;
  categoryId: number;
  categoryName: string;
  parentId: number;
}

export interface ReviewRecord {
  reviewId: number;
  applicationId: number;
  reviewStatus: string;
  backStep: string;
  reviewComment: string;
  reviewer: number;
  reviewedAt: string;
}
