import { message } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';

import type { MerchantData, MerchantTypeOption } from '../types';
import { getMerchantList, getMerchantTypes } from './api';
import type {
  MerchantListItem,
  MerchantListResponse,
} from './interface/apiResponse';

/**
 * 商户列表 Hook
 * 处理商户列表的获取、分页和状态管理
 */
export const useMerchantList = () => {
  // 状态管理，用于分页和筛选条件
  interface FilterState {
    currentPage: number;
    pageSize: number;
    nameOrId: string;
    assignee: string;
    merchantTypes: string[] | number[];
    status: string;
  }

  const [filterState, setFilterState] = useState<FilterState>({
    currentPage: 1,
    pageSize: 10,
    nameOrId: '',
    assignee: '',
    merchantTypes: [],
    status: 'ALL',
  });

  // 列表状态
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<MerchantData[]>([]);
  const [total, setTotal] = useState(0);
  const [categoryOptions, setCategoryOptions] = useState<MerchantTypeOption[]>(
    []
  );

  // 获取商户列表数据
  const fetchMerchantList = async () => {
    setLoading(true);
    try {
      const params = {
        currentPage: filterState.currentPage,
        pageSize: filterState.pageSize,
        searchText: filterState.nameOrId,
        reviewer: filterState.assignee,
        categoryIds: filterState.merchantTypes,
        applicationStatus:
          filterState.status === 'ALL' ? '' : filterState.status,
      };
      const [success, res] = await getMerchantList(params);
      if (success) {
        // 转换后端数据为前端需要的格式
        const apiResponse = res as MerchantListResponse;
        const formattedData = Array.isArray(apiResponse.applications)
          ? apiResponse.applications.map((item: MerchantListItem) => {
              return {
                id: item.applicationId,
                reviewType: item.reviewRecord?.reviewComment || '- -',
                image: item.businessLicenseImage,
                name: item.shopName || '',
                address: item.address || '',
                category: item.categories?.[0]?.categoryName || '',
                assignee: item.applicant || '--',
                applyTime: formatTime(item.merchantCreatedTime) || '',
                status: item.applicationStatus,
                updateTime: formatTime(item.merchantUpdatedTime) || '',
                updatedBy: item.updatedBy || '--',
                provinceName: item.provinceName || '',
                cityName: item.cityName || '',
                districtName: item.districtName || '',
              };
            })
          : [];

        setData(formattedData);
        setTotal(apiResponse.total || 0);
      } else {
        setData([]);
        setTotal(0);
        message.error('获取商户列表失败');
      }
    } catch (error) {
      console.error('获取商户列表失败:', error);
      message.error('获取商户列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理页码变更 - 主动触发查询
  const handlePageChange = (page: number, pageSize?: number) => {
    setFilterState({
      ...filterState,
      currentPage: page,
      pageSize: pageSize || filterState.pageSize,
    });
    // 页码变更时主动触发查询
    setTimeout(() => fetchMerchantList(), 0);
  };

  // 处理名称或ID变更
  const handleNameOrIdChange = (value: string) => {
    setFilterState({
      ...filterState,
      nameOrId: value,
    });
  };

  // 处理分配人变更
  const handleAssigneeChange = (value: string) => {
    setFilterState({
      ...filterState,
      assignee: value,
    });
  };

  // 处理商户类型变更
  const handleMerchantTypesChange = (values: string[] | number[]) => {
    setFilterState({
      ...filterState,
      merchantTypes: values,
    });
  };

  // 处理状态变更
  const handleStatusChange = (value: string) => {
    setFilterState({
      ...filterState,
      status: value,
    });
  };

  // 处理搜索条件变更 - 主动触发查询
  const handleSearch = () => {
    // 搜索时重置到第一页
    setFilterState({
      ...filterState,
      currentPage: 1,
    });
    // 主动触发查询
    setTimeout(() => fetchMerchantList(), 0);
  };

  // 重置搜索条件 - 主动触发查询
  const handleReset = () => {
    setFilterState({
      currentPage: 1,
      pageSize: 10,
      nameOrId: '',
      assignee: '',
      merchantTypes: [],
      status: 'ALL',
    });
    // 主动触发查询
    setTimeout(() => fetchMerchantList(), 0);
  };

  const queryMerchantTypes = async () => {
    const [success, data] = await getMerchantTypes();
    if (success) {
      setCategoryOptions(data as MerchantTypeOption[]);
    } else {
      setCategoryOptions([]);
    }
  };

  // 初始加载时获取商户类型数据和商户列表
  useEffect(() => {
    queryMerchantTypes();
    fetchMerchantList(); // 初始加载时获取一次数据
  }, []);

  return {
    loading,
    data,
    total,
    filterState,
    setFilterState,
    fetchMerchantList,
    handlePageChange,
    handleSearch,
    handleReset,
    handleNameOrIdChange,
    handleAssigneeChange,
    handleMerchantTypesChange,
    handleStatusChange,
    categoryOptions,
  };
};

//格式化时间
export const formatTime = (time: string) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss');
};
