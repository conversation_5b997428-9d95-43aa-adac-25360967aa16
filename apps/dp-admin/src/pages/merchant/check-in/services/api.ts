import { request } from '@dp-frontend/request';

/**
 * 获取商户分类树
 * @returns [success, data] 成功状态和数据
 */
export const getMerchantTypes = async () => {
  try {
    const data = await request.get('/admin-api/merchant/category/tree');
    return [true, data];
  } catch (error) {
    console.error('获取商户分类失败:', error);
    return [false, error];
  }
};

/**
 * 获取商户列表
 * @param params 查询参数
 * @returns [success, data] 成功状态和数据
 */
export const getMerchantList = async (params: {
  searchText: string;
  reviewer: string;
  categoryIds: string[] | number[];
  applicationStatus: string;
  pageNum?: number;
  pageSize?: number;
}) => {
  console.log(params, 'params');
  try {
    const data = await request.post(
      '/admin-api/merchant/application/list',
      params
    );
    return [true, data];
  } catch (error) {
    console.error('获取商户列表失败:', error);
    return [false, error];
  }
};
