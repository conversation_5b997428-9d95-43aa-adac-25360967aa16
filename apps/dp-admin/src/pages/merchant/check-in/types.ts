/**
 * 商戶數據類型定義
 */
export interface MerchantData {
  id: string | number; // 商戶ID
  reviewType: string; // 審核類型
  image: string; // 商戶主圖
  name: string; // 商戶名稱
  address: string; // 商戶地址
  category: string; // 商戶分類
  assignee: string; // 當前分配人
  applyTime: string; // 提交時間
  status: 'APPROVED' | 'REJECTED' | 'PENDING_REVIEW' | 'DRAFT'; // 審核狀態
  updateTime: string; // 更新時間
  updatedBy: string; // 更新人
  provinceName: string;
  cityName: string;
  districtName: string;
}

/**
 * 搜索表單屬性
 */
export interface SearchFormProps {
  nameOrId: string;
  assignee: string;
  merchantTypes: string[] | number[];
  status: string;
  categoryOptions: MerchantTypeOption[];
  onNameOrIdChange: (value: string) => void;
  onAssigneeChange: (value: string) => void;
  onMerchantTypesChange: (values: string[] | number[]) => void;
  onStatusChange: (value: string) => void;
  onSearch: () => void;
  onReset: () => void;
}

/**
 * 商戶分類選項
 */
export interface MerchantTypeOption {
  name: string;
  id: string;
  children?: MerchantTypeOption[];
}

/**
 * 商戶表格屬性
 */
export interface MerchantTableProps {
  loading: boolean;
  data: MerchantData[];
  currentPage: number;
  pageSize: number;
  total: number;
  onPageChange: (page: number, pageSize?: number) => void;
}

/**
 * 操作按鈕屬性
 */
export interface ActionButtonsProps {
  record: MerchantData;
  onReview: (id: string | number) => void;
}
