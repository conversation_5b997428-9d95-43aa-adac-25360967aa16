import React from 'react';
/**
 * 狀態標籤組件
 *
 * 根據不同的審核狀態顯示不同顏色的標籤
 */
const StatusTag = ({
  status,
}: {
  status: 'PENDING_REVIEW' | 'REJECTED' | 'APPROVED' | 'DRAFT';
}) => {
  const getStatusConfig = () => {
    switch (status) {
      case 'PENDING_REVIEW':
        return {
          color: 'bg-orange-100 text-orange-600',
          text: '待審核',
        };
      case 'REJECTED':
        return {
          color: 'bg-red-100 text-red-600',
          text: '已駁回',
        };
      case 'APPROVED':
        return {
          color: 'bg-green-100 text-green-600',
          text: '已通過',
        };
      case 'DRAFT':
        return {
          color: 'bg-green-100 text-green-600',
          text: '待提交',
        };
      default:
        return {
          color: 'bg-gray-100 text-gray-600',
          text: '未知狀態',
        };
    }
  };

  const { color, text } = getStatusConfig();

  return (
    <span className={`px-3 py-1 rounded-full text-xs font-medium ${color}`}>
      {text}
    </span>
  );
};

export default StatusTag;
