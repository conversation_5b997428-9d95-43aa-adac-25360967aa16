import { Image, Pagination, Table } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import React from 'react';
import { useNavigate } from 'react-router-dom';

import { EMPTY_IMG } from '@/utils';

import type { MerchantData, MerchantTableProps } from '../../types';
import ActionButtons from '../ActionButtons/index.web';
import StatusTag from '../StatusTag/index.web';

/**
 * 商戶表格組件
 *
 * 顯示商戶列表及分頁
 */
const MerchantTable: React.FC<MerchantTableProps> = ({
  loading,
  data,
  currentPage,
  pageSize,
  total,
  onPageChange,
}) => {
  // 使用 navigate 進行路由導航
  const navigate = useNavigate();

  // 處理查看詳情
  const handleReview = (id: string | number) => {
    console.log('查看商戶詳情:', id);

    // 導航到審核詳情頁面
    navigate(`/merchant/audit-detail/${id}`);
  };

  // 定義表格列
  const columns: ColumnsType<MerchantData> = [
    {
      title: '商戶ID',
      dataIndex: 'id',
      key: 'id',
      className: 'text-sm',
      width: 100,
    },
    {
      title: '商戶主圖',
      dataIndex: 'image',
      key: 'image',
      className: 'text-sm',
      width: 80,
      render: (image) => (
        <div className="w-12 h-12 overflow-hidden rounded">
          <Image
            src={image}
            alt="商戶主圖"
            className="w-full h-full object-cover"
            fallback={EMPTY_IMG}
          />
        </div>
      ),
    },
    {
      title: '商戶名稱',
      dataIndex: 'name',
      key: 'name',
      className: 'text-sm',
      width: 150,
    },
    {
      title: '審核類型',
      dataIndex: 'reviewType',
      key: 'reviewType',
      className: 'text-sm',
      width: 100,
    },
    {
      title: '商戶地址',
      dataIndex: 'address',
      key: 'address',
      className: 'text-sm',
      width: 200,
      ellipsis: true,
      render: (_, record: MerchantData) => (
        <div className="overflow-hidden text-ellipsis whitespace-nowrap">
          {`${record.provinceName}${record.cityName}${record.districtName}${record.address}`}
        </div>
      ),
    },
    {
      title: '商戶分類',
      dataIndex: 'category',
      key: 'category',
      className: 'text-sm',
      width: 150,
    },
    {
      title: '當前分配人',
      dataIndex: 'assignee',
      key: 'assignee',
      className: 'text-sm',
      width: 120,
    },
    {
      title: '提交時間',
      dataIndex: 'applyTime',
      key: 'applyTime',
      className: 'text-sm',
      width: 150,
    },
    {
      title: '審核狀態',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => <StatusTag status={status} />,
      className: 'text-sm',
    },
    {
      title: '更新時間',
      dataIndex: 'updateTime',
      key: 'updateTime',
      className: 'text-sm',
      width: 120,
    },
    {
      title: '更新人',
      dataIndex: 'updatedBy',
      key: 'updatedBy',
      className: 'text-sm',
      width: 120,
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      width: 60,
      render: (_, record) => (
        <ActionButtons
          record={record}
          onReview={(id: string | number) => handleReview(id)}
        />
      ),
      className: 'text-sm',
    },
  ];

  return (
    <div>
      <h2 className="text-lg font-medium mb-4">工單列表</h2>

      <div className="overflow-x-auto">
        <Table
          columns={columns}
          dataSource={data}
          rowKey="id"
          loading={loading}
          pagination={false}
          className="w-full"
          size="middle"
          scroll={{ x: 1500 }}
          bordered
        />
      </div>

      <div className="flex justify-between items-center mt-4 flex-wrap">
        {total > 0 && (
          <div className="text-sm text-gray-500 mb-2 sm:mb-0">
            顯示 1 到 {Math.min(pageSize, total)} 條，共 {total} 條記錄
          </div>
        )}

        <Pagination
          hideOnSinglePage
          current={currentPage}
          pageSize={pageSize}
          total={total}
          onChange={onPageChange}
          showSizeChanger
          showQuickJumper
          showTotal={(total) => `共 ${total} 條`}
          pageSizeOptions={['10', '20', '50']}
          className="text-sm"
        />
      </div>
    </div>
  );
};

export default MerchantTable;
