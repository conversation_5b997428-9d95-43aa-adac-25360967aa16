import { ReloadOutlined, SearchOutlined } from '@ant-design/icons';
import { Button, Cascader, Col, Form, Input, Row, Select } from 'antd';
import React from 'react';

import type { SearchFormProps } from '../../types';
const { Option } = Select;

/**
 * 搜索表單組件
 *
 * 用於搜索商戶信息
 */
const SearchForm: React.FC<SearchFormProps> = ({
  nameOrId,
  assignee,
  status,
  categoryOptions,
  onNameOrIdChange,
  onAssigneeChange,
  onMerchantTypesChange,
  onStatusChange,
  onSearch,
  onReset,
}) => {
  // 當前分配人選項
  const assigneeOptions = [
    { value: 'zhangsan', label: '張三(<EMAIL>)' },
    { value: 'lisi', label: '李四(<EMAIL>)' },
    { value: 'wangwu', label: '王五(<EMAIL>)' },
    { value: 'zhaoliu', label: '趙六(<EMAIL>)' },
    { value: 'chenqi', label: '陳七(<EMAIL>)' },
    { value: 'huangba', label: '黃八(<EMAIL>)' },
  ];

  // 審核狀態選項
  const statusOptions = [
    { value: 'ALL', label: '全部' },
    { value: 'PENDING_REVIEW', label: '待審核' },
    { value: 'REJECTED', label: '已駁回' },
    { value: 'APPROVED', label: '已通過' },
    { value: 'DRAFT', label: '待提交' },
  ];

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      onSearch();
    }
  };

  return (
    <div className="bg-gray-50 p-4 rounded-md">
      <Form layout="vertical" className="mb-0">
        <Row gutter={[16, 12]}>
          {/* 商戶名稱/ID */}
          <Col xs={24} sm={12} md={6}>
            <Form.Item label="店鋪名稱/ID" className="mb-0">
              <Input
                placeholder="請輸入店鋪名稱/ID"
                value={nameOrId}
                onChange={(e) => onNameOrIdChange(e.target.value)}
                onKeyDown={handleKeyDown}
                prefix={<SearchOutlined className="text-gray-400" />}
              />
            </Form.Item>
          </Col>

          {/* 當前分配人 */}
          <Col xs={24} sm={12} md={6}>
            <Form.Item label="當前分配人" className="mb-0">
              <Select
                placeholder="請選擇分配人"
                value={assignee}
                onChange={onAssigneeChange}
                className="w-full"
                showSearch
                optionFilterProp="children"
                size="middle"
              >
                <Option value="">全部</Option>
                {assigneeOptions.map((option) => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>

          {/* 商戶分類 */}
          <Col xs={24} sm={12} md={6}>
            <Form.Item label="商戶分類" className="mb-0">
              <Cascader
                fieldNames={{
                  label: 'name',
                  value: 'id',
                  children: 'children',
                }}
                placeholder="請選擇商戶分類"
                options={categoryOptions}
                onChange={(value) => {
                  const formattedValue = value?.map?.((item) => {
                    return item?.at?.(-1) || '';
                  });
                  onMerchantTypesChange(formattedValue);
                }}
                showSearch={{
                  filter: (inputValue, path) => {
                    return path?.some?.(
                      (option) =>
                        option.name
                          .toLowerCase()
                          .indexOf(inputValue.toLowerCase()) > -1
                    );
                  },
                }}
                className="w-full"
                size="middle"
                expandTrigger="hover"
                multiple
                maxTagCount="responsive"
              />
            </Form.Item>
          </Col>

          {/* 審核狀態 */}
          <Col xs={24} sm={12} md={6}>
            <Form.Item label="審核狀態" className="mb-0">
              <Select
                placeholder="請選擇審核狀態"
                value={status}
                onChange={onStatusChange}
                className="w-full"
                size="middle"
              >
                {statusOptions.map((option) => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>

          {/* 按鈕區域 */}
          <Col xs={24} className="flex justify-end -mt-[18px]">
            <Button
              onClick={onReset}
              icon={<ReloadOutlined />}
              className="mr-2"
            >
              重設
            </Button>
            <Button
              type="primary"
              onClick={onSearch}
              className="bg-blue-500 hover:bg-blue-600 border-blue-500 hover:border-blue-600"
            >
              搜尋
            </Button>
          </Col>
        </Row>
      </Form>
    </div>
  );
};

export default SearchForm;
