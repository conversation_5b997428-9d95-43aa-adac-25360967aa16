import { Button } from 'antd';
import React from 'react';

import type { ActionButtonsProps } from '../../types';

/**
 * 操作按鈕組件
 *
 * 提供查看詳情按鈕
 */
const ActionButtons: React.FC<ActionButtonsProps> = ({ record, onReview }) => {
  return (
    <Button
      type="primary"
      onClick={() => onReview(record.id)}
      size="small"
      className="bg-blue-500 hover:bg-blue-600 border-blue-500 hover:border-blue-600 text-white text-sm"
    >
      詳情
    </Button>
  );
};

export default ActionButtons;
