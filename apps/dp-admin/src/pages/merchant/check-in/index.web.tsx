import React from 'react';

import MerchantTable from './components/MerchantTable/index.web';
import SearchForm from './components/SearchForm/index.web';
import { useMerchantList } from './services';

/**
 * 入住審核頁面
 *
 * 用於管理和審核商戶的入駐申請。運營人員可以在此頁面查看待審核的商戶信息，
 * 進行審核操作，並跟蹤審核狀態。
 */
const MerchantCheckIn: React.FC = () => {
  // 使用自定義 hooks 獲取數據和狀態
  const {
    loading,
    data,
    total,
    filterState,
    handlePageChange,
    handleSearch,
    handleReset,
    handleNameOrIdChange,
    handleAssigneeChange,
    handleMerchantTypesChange,
    handleStatusChange,
    categoryOptions,
  } = useMerchantList();

  // 處理搜索提交
  const handleSearchSubmit = () => {
    handleSearch();
  };

  // 處理重置
  const handleResetForm = () => {
    handleReset();
  };

  return (
    <div className="bg-white p-4 sm:p-6 md:p-8 rounded-lg shadow-sm">
      <div className="mb-6">
        <h1 className="text-xl sm:text-2xl font-medium mb-6">入住審核</h1>

        {/* 搜索區域 */}
        <div className="mb-6">
          <SearchForm
            nameOrId={filterState.nameOrId}
            assignee={filterState.assignee}
            merchantTypes={filterState.merchantTypes}
            status={filterState.status}
            categoryOptions={categoryOptions}
            onNameOrIdChange={handleNameOrIdChange}
            onAssigneeChange={handleAssigneeChange}
            onMerchantTypesChange={handleMerchantTypesChange}
            onStatusChange={handleStatusChange}
            onSearch={handleSearchSubmit}
            onReset={handleResetForm}
          />
        </div>

        {/* 商戶列表 */}
        <MerchantTable
          loading={loading}
          data={data}
          currentPage={filterState.currentPage}
          pageSize={filterState.pageSize}
          total={total}
          onPageChange={handlePageChange}
        />
      </div>
    </div>
  );
};

export default MerchantCheckIn;
