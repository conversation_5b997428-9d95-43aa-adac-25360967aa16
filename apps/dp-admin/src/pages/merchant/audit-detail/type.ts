export interface IMerchant {
  // 基本信息
  applicationId: string; // 申请ID
  merchantId: string; // 商户ID
  merchantName: string; // 商户名称
  merchantType: string; // 商户类型
  description: string; // 描述
  address: string; // 地址
  merchantCreatedTime: string; // 商户创建时间
  merchantUpdatedTime: string; // 商户更新时间

  // 营业执照信息
  businessLicenseNo: string; // 营业执照号
  businessLicenseImage: string; // 营业执照图片

  // 法人信息
  legalRepresentative: string; // 法定代表人姓名
  legalIdCardNo: string; // 法定代表人身份证号
  legalPhone: string; // 法定代表人手机号

  // 负责人信息（实际经营者）
  businessOwner: string; // 负责人姓名
  businessOwnerIdCardNo: string; // 负责人身份证号
  businessOwnerIdCardFront: string; // 负责人身份证正面照片
  businessOwnerIdCardBack: string; // 负责人身份证背面照片
  businessOwnerPhone: string; // 负责人手机号

  // 台湾餐饮业特定证件
  foodBusinessRegistrationNo?: string; // 食品业者登记字号
  healthPermitNo?: string; // 卫生许可证号
  buildingUsagePermitNo?: string; // 建筑物使用执照号
  fireSafetyCertificateNo?: string; // 消防安全检查合格证明号
  publicLiabilityInsuranceNo?: string; // 公共意外责任保险单号
  foodSafetyManagerCertificateNo?: string; // 食品安全卫生管理人员证书号
  alcoholSalesPermitNo?: string; // 酒类销售许可证号
  environmentalPermitNo?: string; // 环保许可证号

  // 店铺信息
  shopName: string; // 店铺名称
  shopType: string; // 店铺类型
  shopDescription?: string; // 店铺描述

  // 地址信息
  provinceId: string; // 省份ID
  provinceName: string; // 省份名称
  cityId: string; // 城市ID
  cityName: string; // 城市名称
  districtId: string; // 区县ID
  districtName: string; // 区县名称
  addressDetail?: string; // 详细地址
  latitude: number; // 纬度
  longitude: number; // 经度

  // Google POI相关
  googlePoiId?: string; // Google POI ID
  googlePlaceId?: string; // Google Place ID
  importedFromGoogle: boolean; // 是否从Google导入

  // 照片信息
  photos?: ShopPhoto[]; // 店铺照片

  // 分类信息
  categories?: Category[]; // 店铺分类

  // 申请状态信息
  applicationType: 'NEW_SHOP' | 'UPDATE_INFO'; // 申请类型
  applicationStatus:
    | 'PENDING_REVIEW'
    | 'APPROVED'
    | 'REJECTED'
    | 'CANCELLED'
    | 'DRAFT'; // 申请状态
  applicant: string; // 申请人
  remark?: string; // 备注

  // 审核记录
  reviewRecord?: ReviewRecord; // 审核记录

  // 时间信息
  createdAt: string; // 创建时间
  createdBy: string; // 创建人
  updatedAt: string; // 更新时间
  updatedBy: string; // 更新人
}

export interface ShopPhoto {
  id: string;
  url: string;
  type: string;
}

export interface Category {
  level: number;
  categoryId: number;
  categoryName: string;
  parentId: number;
}

export interface ReviewRecord {
  reviewId: number;
  applicationId: number;
  reviewStatus: string;
  backStep?: string;
  reviewComment?: string;
  reviewer: string;
  reviewedAt: string;
}

// 用于表单提交的审核数据
export interface ReviewSubmitData {
  applicationId: string;
  reviewComment?: string;
  operator: string;
  errorStep: string[];
}
