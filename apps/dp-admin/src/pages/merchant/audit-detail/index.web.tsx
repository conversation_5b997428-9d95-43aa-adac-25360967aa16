import { message, Spin } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate, useParams } from 'react-router-dom';

import AccountInfo from './components/AccountInfo/index.web';
import ActionButtons from './components/ActionButtons/index.web';
import BusinessLicense from './components/BusinessLicense/index.web';
import ImageUploadTips from './components/ImageUploadTips/index.web';
import LegalPerson from './components/LegalPerson/index.web';
import PageHeader from './components/PageHeader/index.web';
import StoreInfo from './components/StoreInfo/index.web';
import SystemInfo from './components/SystemInfo/index.web';
import {
  approveMerchant,
  getMerchantDetail,
  rejectMerchant,
} from './service/apis';
import type { IMerchant } from './type';

/**
 * 審核詳情頁面
 *
 * 顯示商戶審核的詳細信息
 */
const AuditDetail: React.FC = () => {
  // 從路徑參數獲取 ID
  const { id: pathId } = useParams<{ id: string }>();

  // 從查詢參數獲取 ID
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const queryId = queryParams.get('id');

  // 優先使用查詢參數中的 ID，如果沒有則使用路徑參數中的 ID
  const id = queryId || pathId || '';

  const navigate = useNavigate();
  const [loading, setLoading] = useState<boolean>(true);
  const [merchantData, setMerchantData] = useState<IMerchant | null>(null);

  // 加載商戶詳情數據
  useEffect(() => {
    if (!id) {
      message.error('缺少商戶ID參數');
      return;
    }

    const fetchMerchantDetail = async () => {
      setLoading(true);
      try {
        const [success, data] = await getMerchantDetail(id);
        if (success) {
          const newData = {
            ...data,
            merchantCreatedTime:
              'merchantCreatedTime' in data &&
              dayjs(data.merchantCreatedTime).format('YYYY-MM-DD HH:mm:ss'),
          };
          setMerchantData(newData as IMerchant);
        } else {
          message.error('獲取商戶詳情失敗');
        }
      } catch (error) {
        console.error('獲取商戶詳情出錯:', error);
        message.error('獲取商戶詳情出錯');
      } finally {
        setLoading(false);
      }
    };

    fetchMerchantDetail();
  }, [id]);

  // 返回列表頁
  const handleBack = () => {
    navigate(-1);
  };

  // 處理通過審核
  const handleApprove = async () => {
    if (!merchantData?.applicationId) {
      message.error('缺少申請ID');
      return;
    }

    try {
      const [success] = await approveMerchant(merchantData.applicationId);
      if (success) {
        message.success('審核通過成功');
        navigate(-1);
      } else {
        message.error('審核通過失敗');
      }
    } catch (error) {
      console.error('審核通過出錯:', error);
      message.error('審核通過出錯');
    }
  };

  // 處理駁回申請
  const handleReject = async (reasons: string[], remarks: string) => {
    if (!merchantData?.applicationId) {
      message.error('缺少申請ID');
      return;
    }

    try {
      const reviewData = {
        applicationId: merchantData.applicationId,
        reviewComment: remarks,
        errorStep: reasons,
        operator: '1001', // 操作人id
      };
      const [success] = await rejectMerchant(reviewData);
      if (success) {
        message.success('駁回申請成功');
        navigate(-1);
      } else {
        message.error('駁回申請失敗');
      }
    } catch (error) {
      console.error('駁回申請出錯:', error);
      message.error('駁回申請出錯');
    }
  };

  return (
    <div className="bg-white p-4 sm:p-6 md:p-8 rounded-lg shadow-sm">
      <div className="mb-6">
        <PageHeader onBack={handleBack} />

        {loading ? (
          <div className="flex justify-center items-center h-64">
            <Spin size="large" />
          </div>
        ) : merchantData ? (
          <div>
            {/* 系統信息區域 */}
            <SystemInfo
              merchantId={merchantData.applicationId}
              submitTime={merchantData.merchantCreatedTime}
              status={merchantData.applicationStatus}
              reviewer={merchantData?.reviewRecord?.reviewer}
            />
            <div className="my-[10px]"></div>
            {/* 相似商户 */}
            <ImageUploadTips />

            <div className="my-[10px]"></div>
            {/* 店鋪信息 */}
            <StoreInfo
              storeName={merchantData.shopName}
              storeCategory={merchantData.shopType}
              storeAddress={`${merchantData.provinceName || ''}${merchantData.cityName || ''}${merchantData.districtName || ''}`}
              addressDetail={merchantData.address || ''}
              storeImage={merchantData.businessLicenseImage}
            />

            <div className="my-[10px]"></div>
            {/* 營業執照信息 */}
            <BusinessLicense
              licenseImage={merchantData.businessLicenseImage}
              licenseNumber={merchantData.businessLicenseNo}
              responsiblePerson={merchantData.legalRepresentative}
            />

            <div className="my-[10px]"></div>
            {/* 法人信息 */}
            <LegalPerson
              idNumber={merchantData.businessOwnerIdCardNo}
              name={merchantData.businessOwner}
              phone={merchantData.businessOwnerPhone}
              idCardFront={merchantData.businessOwnerIdCardFront}
              idCardBack={merchantData.businessOwnerIdCardBack}
            />

            <div className="my-[10px]"></div>
            {/* 管理帳號信息 */}
            <AccountInfo
              name={merchantData.businessOwner}
              idNumber={merchantData.businessOwnerIdCardNo}
              phone={merchantData.businessOwnerPhone}
            />

            {/* 底部操作按鈕 */}
            <ActionButtons
              onBack={handleBack}
              onApprove={handleApprove}
              onReject={handleReject}
              status={merchantData?.reviewRecord?.reviewStatus}
            />
          </div>
        ) : (
          <div className="text-center py-10">
            <p className="text-gray-500">未找到商戶信息</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default AuditDetail;
