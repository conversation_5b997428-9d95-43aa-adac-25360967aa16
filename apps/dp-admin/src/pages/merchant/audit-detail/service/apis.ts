import { request } from '@dp-frontend/request';

import type { IMerchant, ReviewSubmitData } from '../type';

/**
 * 获取商户详情
 * @param id 商户申请ID
 * @returns [success, data] 成功状态和数据
 */
export const getMerchantDetail = async (
  id: string
): Promise<[boolean, IMerchant | Error]> => {
  try {
    const data = await request.get<IMerchant>(
      `/admin-api/merchant/application/detail`,
      {
        applicationId: id,
      }
    );
    return [true, data];
  } catch (error) {
    console.error('获取商户详情失败:', error);
    return [false, error as Error];
  }
};

/**
 * 审核通过商户申请
 * @param applicationId 申请ID
 * @returns [success, data] 成功状态和数据
 */
export const approveMerchant = async (
  applicationId: string
): Promise<[boolean, unknown | Error]> => {
  try {
    const data = await request.post(
      ' /admin-api/merchant/application/approve',
      {
        applicationId,
        reviewComment: '',
        operator: '1001', // 操作人id
      }
    );
    return [true, data];
  } catch (error) {
    console.error('审核通过失败:', error);
    return [false, error as Error];
  }
};

/**
 * 驳回商户申请
 * @param reviewData 审核数据
 * @returns [success, data] 成功状态和数据
 */
export const rejectMerchant = async (
  reviewData: ReviewSubmitData
): Promise<[boolean, unknown | Error]> => {
  try {
    // 将ReviewSubmitData转换为普通对象
    const requestData = {
      applicationId: reviewData.applicationId,
      reviewComment: reviewData.reviewComment,
      operator: reviewData.operator, // 操作人id
      errorStep: reviewData.errorStep,
    };

    const data = await request.post(
      '/admin-api/merchant/application/reject',
      requestData
    );
    return [true, data];
  } catch (error) {
    console.error('驳回申请失败:', error);
    return [false, error as Error];
  }
};
