import { Card, Descriptions } from 'antd';
import React from 'react';

import CopyText from '../CopyText/index.web';

interface AccountInfoProps {
  name: string;
  idNumber: string;
  phone: string;
}

/**
 * 管理帳號信息組件
 *
 * 顯示管理帳號的姓名、身份證號碼和手機號
 */
const AccountInfo: React.FC<AccountInfoProps> = ({ name, idNumber, phone }) => {
  return (
    <Card title="管理帳號信息" className="mb-6 shadow-sm">
      <Descriptions
        column={{ xs: 1, sm: 2, md: 3 }}
        bordered={false}
        size="small"
      >
        <Descriptions.Item label="姓名" className="py-2">
          <div className="flex items-center">
            <span>{name}</span>
            {/* {name === '黃經理' && (
              <span className="ml-2 text-xs text-white bg-blue-500 px-1 py-0.5 rounded">
                管理員
              </span>
            )} */}
          </div>
        </Descriptions.Item>
        <Descriptions.Item label="身分證號碼" className="py-2">
          <div className="flex items-center">
            <span className="break-all">{idNumber}</span>
            {idNumber && <CopyText text={idNumber} />}
          </div>
        </Descriptions.Item>
        <Descriptions.Item label="手機號" className="py-2">
          <div className="flex items-center">
            <span>{phone}</span>
            {phone && <CopyText text={phone} />}
          </div>
        </Descriptions.Item>
      </Descriptions>
    </Card>
  );
};

export default AccountInfo;
