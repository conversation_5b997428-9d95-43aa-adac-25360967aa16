import { CopyOutlined } from '@ant-design/icons';
import { copyTextToClipboard } from '@utils/index';
import { message } from 'antd';
import React from 'react';

function CopyText({ text }: { text: string }) {
  const handleCopy = async () => {
    const type = await copyTextToClipboard(text);
    if (type) {
      message.success('复制成功');
    } else {
      message.error('复制失败');
    }
  };

  return (
    <span
      className="ml-2 text-xs text-blue-500 cursor-pointer"
      onClick={handleCopy}
    >
      <CopyOutlined />
    </span>
  );
}

export default CopyText;
