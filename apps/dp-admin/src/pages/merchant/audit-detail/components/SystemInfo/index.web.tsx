import { Card, Descriptions, Tag } from 'antd';
import React from 'react';

interface SystemInfoProps {
  merchantId: string;
  submitTime: string;
  status:
    | 'PENDING_REVIEW'
    | 'APPROVED'
    | 'REJECTED'
    | 'CANCELLED'
    | 'DRAFT'
    | string;
  reviewer: string | undefined;
}

/**
 * 系統信息組件
 *
 * 顯示商戶ID、提交時間、審核狀態和審核人員
 */
const SystemInfo: React.FC<SystemInfoProps> = ({
  merchantId,
  submitTime,
  status,
  reviewer,
}) => {
  // 根據狀態返回不同顏色的標籤
  const getStatusTag = (status: string) => {
    switch (status) {
      case 'PENDING_REVIEW':
        return <Tag color="orange">待審核</Tag>;
      case 'APPROVED':
        return <Tag color="green">已通過</Tag>;
      case 'REJECTED':
        return <Tag color="red">已駁回</Tag>;
      case 'CANCELLED':
        return <Tag color="gray">已取消</Tag>;
      case 'DRAFT':
        return <Tag color="gray">待提交</Tag>;
      default:
        return <Tag>未知狀態</Tag>;
    }
  };

  return (
    <Card className="mb-6 shadow-sm">
      <Descriptions
        bordered={false}
        layout="vertical"
        column={{ xs: 2, sm: 2, md: 4, lg: 4, xl: 4, xxl: 4 }}
      >
        <Descriptions.Item label="商戶ID">{merchantId}</Descriptions.Item>
        <Descriptions.Item label="提交時間">{submitTime}</Descriptions.Item>
        <Descriptions.Item label="審核狀態">
          {getStatusTag(status)}
        </Descriptions.Item>
        <Descriptions.Item label="審核人員">{reviewer}</Descriptions.Item>
      </Descriptions>
    </Card>
  );
};

export default SystemInfo;
