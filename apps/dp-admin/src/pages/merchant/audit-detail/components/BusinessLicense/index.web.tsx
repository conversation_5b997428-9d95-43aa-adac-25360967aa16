import { Card, Descriptions, Image } from 'antd';
import React from 'react';

import { EMPTY_IMG } from '@/utils';

import CopyText from '../CopyText/index.web';

interface BusinessLicenseProps {
  licenseNumber: string;
  responsiblePerson: string;
  licenseImage: string;
}

/**
 * 營業執照信息組件
 *
 * 顯示執照號碼、負責人和執照圖片
 */
const BusinessLicense: React.FC<BusinessLicenseProps> = ({
  licenseNumber,
  responsiblePerson,
  licenseImage,
}) => {
  return (
    <Card title="營業執照信息" className="mb-6 shadow-sm">
      <div className="flex flex-col md:flex-row">
        <div className="w-full md:w-2/3 pr-0 md:pr-4">
          <Descriptions column={1} bordered={false} size="small">
            <Descriptions.Item label="執照號碼" className="py-2">
              <div className="flex items-center">
                <span className="break-all">{licenseNumber}</span>
                {licenseNumber && <CopyText text={licenseNumber} />}
              </div>
            </Descriptions.Item>
            <Descriptions.Item label="負責人姓名" className="py-2">
              {responsiblePerson}
            </Descriptions.Item>
          </Descriptions>
        </div>
        <div className="w-full md:w-1/3 mt-4 md:mt-0">
          <div className="flex flex-col items-center">
            <div className="text-gray-500 mb-2">營業執照圖片</div>
            <div className="w-full max-w-xs">
              <Image
                src={licenseImage}
                alt="營業執照圖片"
                className="w-full object-cover rounded"
                fallback={EMPTY_IMG}
              />
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default BusinessLicense;
