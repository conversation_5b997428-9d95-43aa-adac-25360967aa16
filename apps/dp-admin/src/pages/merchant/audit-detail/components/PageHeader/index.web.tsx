import { ArrowLeftOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import React from 'react';

interface PageHeaderProps {
  onBack: () => void;
}

/**
 * 頁面頭部組件
 *
 * 顯示頁面標題和返回按鈕
 */
const PageHeader: React.FC<PageHeaderProps> = ({ onBack }) => {
  return (
    <div className="flex items-center mb-6">
      <Button
        type="link"
        icon={<ArrowLeftOutlined />}
        onClick={onBack}
        className="px-0 mr-2"
      >
        返回
      </Button>
      <h1 className="text-xl sm:text-2xl font-medium">商戶入住審核</h1>
    </div>
  );
};

export default PageHeader;
