import { Card, Table } from 'antd';
import React from 'react';

/**
 * 圖片上傳提示組件
 *
 * 顯示圖片上傳要求和狀態
 */
const ImageUploadTips = () => {
  // 表格列定義
  const columns = [
    {
      title: '資料名稱',
      dataIndex: 'name',
      key: 'name',
      width: '20%',
    },
    {
      title: '上傳狀態',
      dataIndex: 'status',
      key: 'status',
      width: '15%',
      render: (text: string) => {
        if (text === '已入駐') {
          return <span className="text-blue-500">{text}</span>;
        } else if (text === '待審核') {
          return <span className="text-orange-500">{text}</span>;
        }
        return text;
      },
    },
    {
      title: '規格說明',
      dataIndex: 'description',
      key: 'description',
      width: '45%',
    },
    {
      title: '操作',
      key: 'action',
      width: '20%',
      render: () => (
        <a
          href="#"
          className="text-blue-500"
          onClick={(e) => {
            e.preventDefault();
            // 處理查看圖片
          }}
        >
          查看詳情
        </a>
      ),
    },
  ];

  // 表格數據
  const data = [
    {
      key: '1',
      name: '商戶名稱',
      status: '已入駐',
      description: '照片清晰可見，尺寸不小於300x300；照片格式支持JPG、PNG',
    },
    {
      key: '2',
      name: '店鋪照片',
      status: '待審核',
      description: '照片清晰可見，尺寸不小於300x300；照片格式支持JPG、PNG',
    },
  ];

  return (
    <Card title="系統已存在的相似商戶" className="mb-6 shadow-sm">
      <div className="text-gray-500 mb-4">
        系統中已存在以下商戶照片，請審核是否符合要求：
      </div>
      <Table
        columns={columns}
        dataSource={data}
        pagination={false}
        size="small"
        className="mb-4"
      />
    </Card>
  );
};

export default ImageUploadTips;
