import { EnvironmentOutlined } from '@ant-design/icons';
import { Card, Descriptions, Image } from 'antd';
import React from 'react';

import { EMPTY_IMG } from '@/utils';

import CopyText from '../CopyText/index.web';

interface StoreInfoProps {
  storeName: string;
  storeCategory: string;
  storeAddress: string;
  addressDetail: string;
  storeImage: string;
}

/**
 * 店鋪信息組件
 *
 * 顯示店鋪名稱、分類、地址和門店照片
 */
const StoreInfo: React.FC<StoreInfoProps> = ({
  storeName,
  storeCategory,
  storeAddress,
  addressDetail,
  storeImage,
}) => {
  return (
    <Card title="店鋪信息" className="mb-6 shadow-sm">
      <div className="flex flex-col md:flex-row">
        <div className="w-full md:w-2/3 pr-0 md:pr-4">
          <Descriptions
            column={{ sm: 1, md: 2, lg: 2, xl: 2 }}
            bordered={false}
            size="small"
          >
            <Descriptions.Item label="店鋪名稱" className="py-2">
              <div className="flex items-center">
                <span>{storeName}</span>
                {storeName && <CopyText text={storeName} />}
              </div>
            </Descriptions.Item>
            <Descriptions.Item label="店鋪分類" className="py-2">
              {storeCategory}
            </Descriptions.Item>
            <Descriptions.Item label="店鋪地址" className="py-2">
              {storeAddress && (
                <div className="flex items-start flex-col">
                  <div className="flex items-start">
                    <span
                      className="text-xs text-blue-500 mr-[3px] cursor-pointer mt-[3px]"
                      onClick={() => {
                        window.open(
                          `https://maps.google.com/maps?q=${encodeURIComponent(storeAddress)}`
                          // `https://maps.googleapis.com/maps/api/js?key=AIzaSyAG2ktCRDw3ynec2891E9mCrUtVv5syPUE&libraries=places`
                        );
                      }}
                    >
                      <EnvironmentOutlined />
                    </span>
                    <span>{storeAddress}</span>
                    {/* <a
                    href={`https://maps.google.com/maps?q=${encodeURIComponent(storeAddress)}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="ml-2 text-xs text-white bg-blue-500 px-1 py-0.5 rounded"
                  >
                    Google Map
                  </a> */}
                  </div>
                  <span className="text-gray-500 mt-1">{addressDetail}</span>
                </div>
              )}
            </Descriptions.Item>
          </Descriptions>
        </div>
        <div className="w-full md:w-1/3 mt-4 md:mt-0">
          <div className="flex flex-col items-center">
            <div className="text-gray-500 mb-2">門店照片</div>
            <div className="w-full max-w-xs">
              <Image
                src={storeImage}
                alt="門店照片"
                className="w-full object-cover rounded"
                fallback={EMPTY_IMG}
              />
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default StoreInfo;
