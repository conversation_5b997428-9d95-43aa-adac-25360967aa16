import { CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import { Button, Checkbox, Form, Input, Modal } from 'antd';
import React, { useState } from 'react';

interface ActionButtonsProps {
  onBack: () => void;
  onApprove: () => void;
  onReject: (reasons: string[], remarks: string) => void;
  // 'PENDING_REVIEW' | 'APPROVED' | 'REJECTED' | 'CANCELLED' | 'DRAFT'
  status: string | undefined;
}

/**
 * 底部操作按鈕組件
 *
 * 顯示返回、駁回和通過按鈕，並處理相關操作
 */
const ActionButtons: React.FC<ActionButtonsProps> = ({
  onBack,
  onApprove,
  onReject,
  status,
}) => {
  const [approveModalVisible, setApproveModalVisible] =
    useState<boolean>(false);
  const [rejectModalVisible, setRejectModalVisible] = useState<boolean>(false);
  const [rejectForm] = Form.useForm();

  // 駁回原因選項
  const rejectReasons = [
    { label: '店鋪信息異常', value: 'infoErr' },
    { label: '營業執照信息異常', value: 'licenseErr' },
    { label: '法人信息異常', value: 'legalErr' },
    { label: '帳號所有人信息異常', value: 'accountErr' },
  ];

  // 顯示通過確認彈窗
  const showApproveModal = () => {
    setApproveModalVisible(true);
  };

  // 處理通過審核
  const handleApprove = () => {
    onApprove();
    setApproveModalVisible(false);
  };

  // 顯示駁回彈窗
  const showRejectModal = () => {
    rejectForm.resetFields();
    setRejectModalVisible(true);
  };

  // 是否可以点击操作
  const isDisabled = () => {
    if (status === 'PENDING_REVIEW') {
      return false;
    }
    return true;
  };

  // 處理駁回申請
  const handleReject = () => {
    rejectForm.validateFields().then((values) => {
      onReject(values.reasons, values.remarks);
      setRejectModalVisible(false);
    });
  };

  return (
    <div>
      <div className="mt-6 flex justify-end gap-4">
        <Button onClick={onBack}>返回</Button>
        <Button
          type="primary"
          danger
          onClick={showRejectModal}
          disabled={isDisabled()}
        >
          駁回申請
        </Button>
        <Button
          type="primary"
          onClick={showApproveModal}
          disabled={isDisabled()}
        >
          通過審核
        </Button>
      </div>

      {/* 通過確認彈窗 */}
      <Modal
        title={
          <div className="flex items-center">
            <CheckCircleOutlined
              style={{ color: '#34C759' }}
              className="mr-2"
            />
            <span>審核確認</span>
          </div>
        }
        open={approveModalVisible}
        onOk={handleApprove}
        onCancel={() => setApproveModalVisible(false)}
        okText="確認"
        cancelText="取消"
        centered
      >
        <p className="text-lg">請確認資料審核通過</p>
      </Modal>

      {/* 駁回原因彈窗 */}
      <Modal
        title={
          <div className="flex items-center">
            <CloseCircleOutlined className="text-red-500 mr-2" />
            <span>駁回原因</span>
          </div>
        }
        open={rejectModalVisible}
        onOk={handleReject}
        onCancel={() => setRejectModalVisible(false)}
        okText="確認"
        cancelText="取消"
        centered
        width={600}
      >
        <Form form={rejectForm} layout="vertical">
          <Form.Item
            name="reasons"
            label="駁回原因"
            rules={[{ required: true, message: '請選擇駁回原因' }]}
          >
            <Checkbox.Group
              options={rejectReasons}
              className="flex flex-col gap-2"
            />
          </Form.Item>

          <Form.Item
            name="remarks"
            label="駁回說明"
            rules={[
              { required: true, message: '請填寫駁回說明' },
              { max: 400, message: '駁回說明不能超過400個字' },
            ]}
          >
            <Input.TextArea
              rows={4}
              placeholder="請填寫駁回說明，不超過400個字"
              showCount
              maxLength={400}
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ActionButtons;
