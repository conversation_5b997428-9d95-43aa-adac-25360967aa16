import { Card, Descriptions, Image } from 'antd';
import React from 'react';

import { EMPTY_IMG } from '@/utils';

import CopyText from '../CopyText/index.web';

interface LegalPersonProps {
  idNumber: string;
  name: string;
  phone: string;
  idCardFront: string;
  idCardBack: string;
}

/**
 * 法人信息組件
 *
 * 顯示法人身份證號碼、姓名、手機號和身份證照片
 */
const LegalPerson: React.FC<LegalPersonProps> = ({
  idNumber,
  name,
  phone,
  idCardFront,
  idCardBack,
}) => {
  return (
    <Card title="法人信息" className="mb-6 shadow-sm">
      <div className="flex flex-col">
        <div className="w-full mb-4">
          <Descriptions
            column={{ xs: 1, sm: 2, md: 3 }}
            bordered={false}
            size="small"
          >
            <Descriptions.Item label="身分證號碼" className="py-2">
              <div className="flex items-center">
                <span className="break-all">{idNumber}</span>
                {idNumber && <CopyText text={idNumber} />}
              </div>
            </Descriptions.Item>
            <Descriptions.Item label="姓名" className="py-2">
              {name}
            </Descriptions.Item>
            <Descriptions.Item label="手機號" className="py-2">
              {phone}
            </Descriptions.Item>
          </Descriptions>
        </div>
        <div className="w-full">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex flex-col items-start">
              <div className="text-gray-500 mb-2">身分證正面</div>
              <div className="w-full">
                <Image
                  src={idCardFront}
                  alt="身分證正面"
                  className="w-full object-cover rounded"
                  fallback={EMPTY_IMG}
                />
              </div>
            </div>
            <div className="flex flex-col items-start">
              <div className="text-gray-500 mb-2">身分證反面</div>
              <div className="w-full">
                <Image
                  src={idCardBack}
                  alt="身分證反面"
                  className="w-full object-cover rounded"
                  fallback={EMPTY_IMG}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default LegalPerson;
