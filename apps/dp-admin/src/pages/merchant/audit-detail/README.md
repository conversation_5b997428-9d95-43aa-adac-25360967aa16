# 商戶審核詳情頁面

## 頁面概述

商戶審核詳情頁面用於展示待審核商戶的詳細資訊，並提供審核操作功能。運營人員可以在此頁面查看商戶的各項資料，並決定是否通過審核或駁回申請。

頁面通過 React 函數組件實現，使用 React Router 進行路由管理，支持通過路徑參數或查詢參數獲取商戶 ID。頁面採用響應式設計，適配不同尺寸的設備。

## 目錄結構

```
audit-detail/
├── components/                    # 組件目錄
│   ├── AccountInfo/               # 管理帳號信息組件
│   │   └── index.web.tsx
│   ├── ActionButtons/             # 操作按鈕組件
│   │   └── index.web.tsx
│   ├── BusinessLicense/           # 營業執照信息組件
│   │   └── index.web.tsx
│   ├── CopyText/                  # 文本複製組件
│   │   └── index.web.tsx
│   ├── ImageUploadTips/           # 圖片上傳提示組件
│   │   └── index.web.tsx
│   ├── LegalPerson/               # 法人信息組件
│   │   └── index.web.tsx
│   ├── PageHeader/                # 頁面標題組件
│   │   └── index.web.tsx
│   ├── StoreInfo/                 # 店鋪信息組件
│   │   └── index.web.tsx
│   └── SystemInfo/                # 系統信息組件
│       └── index.web.tsx
├── index.web.tsx                  # 頁面主入口
├── README.md                      # 頁面文檔
└── type.ts                        # 類型定義
```

## 頁面結構

頁面主要分為以下幾個部分：

1. **頁面標題與導航**
   - 返回列表按鈕
   - 審核詳情標題

2. **系統資訊區域**
   - 商戶ID
   - 提交時間
   - 審核狀態
   - 審核人員

3. **店鋪基本資訊**
   - 店鋪名稱
   - 店鋪分類
   - 店鋪地址（詳細地址與地圖位置）
   - 門店照片

4. **營業執照資訊**
   - 執照號碼
   - 負責人姓名
   - 營業執照圖片

5. **法人資訊**
   - 身分證號碼
   - 姓名
   - 手機號
   - 身分證正面照片
   - 身分證反面照片

6. **管理帳號資訊**
   - 姓名
   - 身分證號碼
   - 手機號

7. **審核操作區域**
   - 駁回按鈕
   - 通過按鈕

## 審核資料預覽

### 系統資訊
| 商戶ID | 提交時間 | 審核狀態 | 審核人員 |
|-------|---------|---------|---------|
| ABC12345678 | 2023-04-24 18:00:33 | 待審核 | 王經理 |

### 店鋪資訊
| 店鋪名稱 | 店鋪分類 | 詳細地址 | 補充地址 |
|---------|---------|---------|---------|
| 新東方大茶館 - 小橋夜市旗艦店 | 餐飲 - 咖啡廳 - 精品咖啡 | 台北市大安區信義南路一段107號 | 1樓 (建築東大堂旁邊出口) |

### 營業執照資訊
| 執照號碼 | 負責人姓名 |
|---------|-----------|
| 123456789343434343434343 | 許君陽 |

### 法人資訊
| 身分證號碼 | 姓名 | 手機號 |
|-----------|------|-------|
| 123456789343434343434343 | 許君陽 | 12345678 |

### 管理帳號資訊
| 姓名 | 身分證號碼 | 手機號 |
|------|-----------|-------|
| 黃經理 | 123456789 | 123456789 |

## 審核操作流程

### 通過審核
1. 點擊「通過」按鈕
2. 系統彈出確認視窗，顯示「請確認資料審核通過」
3. 點擊確認後，系統更新商戶狀態為「已通過」
4. 頁面顯示審核成功提示，並返回列表頁

### 駁回申請
1. 點擊「駁回」按鈕
2. 系統彈出駁回原因填寫視窗
3. 選擇駁回原因（多選）：
   - 店鋪資訊異常
   - 營業執照資訊異常
   - 法人資訊異常
   - 帳號所有人資訊異常
4. 填寫駁回說明（不超過400個漢字）
5. 點擊確認後，系統更新商戶狀態為「已駁回」
6. 頁面顯示駁回成功提示，並返回列表頁

## 審核資料驗證要點

### 店鋪資訊驗證
- 店鋪名稱是否與營業執照一致
- 店鋪地址是否真實有效
- 店鋪分類是否符合實際經營範圍
- 門店照片是否清晰且與描述相符

### 營業執照驗證
- 執照號碼是否有效
- 執照圖片是否清晰可辨識
- 執照上的資訊是否與填寫內容一致

### 法人資訊驗證
- 身分證號碼格式是否正確
- 身分證照片是否清晰且為本人
- 法人資訊是否與營業執照一致

### 管理帳號資訊驗證
- 管理人員資訊是否完整
- 聯絡方式是否有效

## 設計說明

1. 頁面採用卡片式佈局，將不同類型的資訊分組展示，提高可讀性
2. 重要資訊使用醒目的顏色標識，如審核狀態使用不同顏色的標籤
3. 圖片資訊可點擊放大查看，方便審核人員仔細檢查
4. 審核操作按鈕放置在頁面底部，避免誤操作
5. 駁回操作需要填寫原因，確保審核流程的規範性和可追溯性

## 技術實現

頁面使用 React 和 Ant Design 組件庫實現，主要包括：
- Card 組件：用於分組展示不同類型的資訊
- Descriptions 組件：用於展示詳細資訊
- Image 組件：用於展示和預覽圖片
- Modal 組件：用於實現確認和駁回原因填寫視窗
- Form 組件：用於實現駁回原因的表單
- Button 組件：用於實現審核操作按鈕
- Tag 組件：用於顯示審核狀態標籤
- Checkbox 組件：用於駁回原因多選
- Spin 組件：用於顯示加載狀態

## 組件結構

頁面由以下主要組件構成：

1. **AuditDetail**：頁面主組件，負責數據獲取和狀態管理
   - 使用 React Router 的 `useParams` 和 `useLocation` 獲取商戶 ID
   - 使用 `useState` 管理商戶數據和加載狀態
   - 處理審核操作邏輯

2. **PageHeader**：頁面標題組件
   - 顯示頁面標題和返回按鈕
   - 處理返回上一頁操作

3. **SystemInfo**：系統信息組件
   - 顯示商戶 ID、提交時間、審核狀態和審核人員
   - 根據狀態顯示不同顏色的標籤

4. **StoreInfo**：店鋪信息組件
   - 顯示店鋪名稱、分類、地址和門店照片
   - 提供地址複製和地圖查看功能

5. **BusinessLicense**：營業執照組件
   - 顯示執照號碼、負責人和執照圖片
   - 提供執照號碼複製功能

6. **LegalPerson**：法人信息組件
   - 顯示法人身份證號碼、姓名、手機號和身份證照片
   - 提供身份證號碼複製功能

7. **AccountInfo**：管理帳號組件
   - 顯示管理帳號的姓名、身份證號碼和手機號
   - 提供聯絡方式複製功能

8. **ActionButtons**：操作按鈕組件
   - 顯示返回、駁回和通過按鈕
   - 處理審核操作和彈窗邏輯

9. **CopyText**：文本複製組件
   - 提供文本複製功能
   - 顯示複製成功/失敗提示

## 數據流

1. **數據獲取流程**：
   - 頁面加載時，從 URL 獲取商戶 ID
   - 使用 ID 調用 API 獲取商戶詳細信息
   - 將獲取的數據保存到組件狀態中
   - 更新 UI 顯示商戶信息

2. **審核操作流程**：
   - 用戶點擊「通過」或「駁回」按鈕
   - 顯示確認彈窗或駁回原因填寫彈窗
   - 用戶確認後，調用相應的 API 更新商戶狀態
   - 顯示操作成功提示，並返回列表頁

3. **錯誤處理**：
   - API 請求失敗時顯示錯誤提示
   - 表單驗證錯誤時顯示相應提示
   - 網絡錯誤時提供重試選項

## 實現注意事項

1. **響應式設計**：
   - 使用 Tailwind CSS 的響應式類（如 `md:flex-row`、`sm:text-2xl` 等）實現不同設備的適配
   - 在小屏幕上垂直排列內容，在大屏幕上水平排列，提高可讀性

2. **圖片處理**：
   - 使用 Ant Design 的 Image 組件處理圖片預覽功能
   - 提供圖片加載失敗時的備用顯示（fallback）
   - 確保圖片尺寸適合容器，避免變形

3. **表單驗證**：
   - 駁回原因至少選擇一項
   - 駁回說明不超過 400 個漢字
   - 提供即時驗證反饋

4. **性能優化**：
   - 使用 React 的 `useState` 和 `useEffect` 管理組件狀態和生命週期
   - 避免不必要的重新渲染
   - 使用 `memo` 優化子組件渲染

5. **安全考慮**：
   - 敏感信息（如身份證號碼）部分隱藏顯示
   - 審核操作需要二次確認
   - 防止 XSS 攻擊，對顯示的數據進行轉義

6. **無障礙性**：
   - 提供適當的 ARIA 標籤
   - 確保鍵盤可訪問性
   - 提供足夠的顏色對比度
