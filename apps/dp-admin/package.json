{"name": "dp-admin", "version": "1.0.0", "main": "./src/index.js", "scripts": {"start": "expo start --clear", "android": "expo start --android --clear", "ios": "expo start --ios --clear", "web": "expo start --web --clear", "build:web": "expo export --platform web --clear"}, "private": true, "dependencies": {"@ahooksjs/use-url-state": "^3.5.1", "@ant-design/icons": "^6.0.0", "@babel/runtime": "^7.27.0", "@dp-frontend/navigation": "workspace:*", "@dp-frontend/request": "workspace:*", "@dp-frontend/ui": "workspace:*", "@expo/metro-runtime": "~4.0.0", "@tailwindcss/postcss": "^4.1.5", "antd": "^5.25.0", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "expo": "~52.0.46", "postcss": "^8.5.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-native": "0.76.9", "react-native-safe-area-context": "^4.12.0", "react-native-screens": "^4.4.0", "react-native-web": "~0.19.13", "react-router-dom": "^6.30.0", "react-strict-dom": "^0.0.34", "react-strict-dom-tailwind": "0.2.1-beta.0", "tailwindcss": "^4.1.5"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "babel-plugin-module-resolver": "^5.0.2", "postcss-react-strict-dom": "^0.0.6"}}