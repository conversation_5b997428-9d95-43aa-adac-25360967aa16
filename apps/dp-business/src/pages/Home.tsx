import React from 'react';
import { html } from 'react-strict-dom';
import { tw } from 'react-strict-dom-tailwind';

const Home = () => {
  return (
    <html.div style={tw('flex-1 bg-gray-50 flex flex-col pt-5')}>
      <html.p style={tw('text-base text-center mb-2.5 text-gray-800')}>
        欢迎来到商家端应用！
      </html.p>
      <html.p style={tw('text-base text-center mb-2.5 text-gray-800')}>
        这是使用Expo和React Strict DOM构建的商家端应用程序。
      </html.p>
    </html.div>
  );
};

export default Home;
