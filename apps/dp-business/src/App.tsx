import { Navigation } from '@dp-frontend/navigation';
import React from 'react';
import { SafeAreaProvider } from 'react-native-safe-area-context';

import { pageConfigs } from './configs/page-configs';

const App: React.FC = () => {
  return (
    <React.StrictMode>
      <SafeAreaProvider>
        <Navigation pages={pageConfigs} />
      </SafeAreaProvider>
    </React.StrictMode>
  );
};

export default App;
