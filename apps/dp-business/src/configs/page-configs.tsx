import type { PageConfig } from '@dp-frontend/navigation';

// 导入页面组件
import Home from '../pages/Home';
import Orders from '../pages/Orders';
import Products from '../pages/Products';

// 定义页面配置
export const pageConfigs: PageConfig[] = [
  {
    name: 'Main',
    component: Home, // 临时使用Home组件，实际上这个组件不会被直接渲染
    type: 'stack',
    options: {
      title: '商家端',
      headerShown: false,
      path: '',
    },
    isInitial: true,
    children: [
      {
        name: 'Home',
        component: Home,
        type: 'tab',
        options: {
          title: '首页',
          tabBarLabel: '首页',
          headerTitleAlign: 'center',
          path: 'home',
        },
      },
      {
        name: 'Orders',
        component: Orders,
        type: 'tab',
        options: {
          title: '订单',
          tabBarLabel: '订单',
          headerTitleAlign: 'center',
          path: 'orders',
        },
      },
      {
        name: 'Products',
        component: Products,
        type: 'tab',
        options: {
          title: '商品',
          tabBarLabel: '商品',
          headerTitleAlign: 'center',
          path: 'products',
        },
      },
    ],
  },
];
