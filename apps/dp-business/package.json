{"name": "dp-business", "version": "1.0.0", "main": "./src/index.js", "scripts": {"start": "expo start --clear", "android": "expo start --android --clear", "ios": "expo start --ios --clear", "web": "expo start --web --clear"}, "private": true, "dependencies": {"@babel/runtime": "^7.27.0", "@dp-frontend/request": "workspace:*", "@dp-frontend/navigation": "workspace:*", "@dp-frontend/ui": "workspace:*", "@expo/metro-runtime": "~4.0.0", "expo": "~52.0.46", "react": "^18.3.1", "react-dom": "^18.3.1", "react-native": "0.76.9", "react-native-safe-area-context": "^4.12.0", "react-native-screens": "^4.4.0", "react-native-web": "~0.19.13", "react-strict-dom": "^0.0.34", "react-strict-dom-tailwind": "0.2.1-beta.0"}, "devDependencies": {"postcss-react-strict-dom": "^0.0.6"}}